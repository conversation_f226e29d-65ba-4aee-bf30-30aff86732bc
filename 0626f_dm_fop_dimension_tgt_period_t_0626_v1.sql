CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_dimension_tgt_period_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
 /*
创建时间：2025-06-7
创建人  ：wwx1077703
背景描述：将汇总层的数据插入到TGT接口表，具体逻辑如下
发货量、收货量的数据从盈利结转率汇总表（dm_fop_dimension_carryover_sum_t）获取
设备前收入  设备前成本从盈利量对价前纲价格成本汇总表（dm_fop_dimension_cost_price_sum_t）获取
率（制毛、收入占比、结转率、均本、均价）：
制毛=1-上面的成本/上面的收入
收入占比 =上面的量纲收入/上面的LV2收入
均价=上面的收入/上面的收入量
整体需要计算ytd、季度、半年度、年度的数据
处理逻辑以dm_fop_dimension_qty_sum_t作为主表来关联对应的数据进行处理


参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_filled_t();
*/
 
 declare
	v_sp_name VARCHAR(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_tgt_period_t('||p_version_code||')';
	v_tbl_name VARCHAR(100) := 'fin_dm_opt_fop.dm_fop_dimension_tgt_period_t';
	v_tbl2_name VARCHAR(100) := 'fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t';
	v_version_code VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   BIGINT; --步骤号
	v_dml_row_count  NUMBER DEFAULT 0 ;
  v_max_ytd  VARCHAR(50);  -- 最大期次的上一年
	


begin
	x_success_flag := '1';                                 --1表示成功
  
  -- 	 --1.开始日志
--   v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
	
	       -- 获取版本号，如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select max(version_code) into v_version_code
        from fin_dm_opt_fop.dm_fop_dimension_cost_rev_sum_t ;	 -- 关联的主表
        end if 
        ;	
        
        select  (left(max(period_id)::VARCHAR,4)::numeric-1)::VARCHAR  into  v_max_ytd  from fin_dm_opt_fop.dm_fop_dimension_cost_rev_sum_t  where version_code= v_version_code;
	
-- 	 --1.开始日志
--   v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',获取最大版本和上年',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  

      
      -- 1.把收入和成本的数据全部灌到临时表，后面用来关联
      -- 同纬度下，取最大的一条数据  按照收入最大来取
      drop table if exists dm_fop_dimension_cost_price_sum_tmp;
	    create temporary table  dm_fop_dimension_cost_price_sum_tmp
		  as
      with  dimension_cost_price_sum   as (
      select   -- distinct  152256     
      cps.version_code	                  -- 版本编码
      ,cps.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,cps.time_window_code	              -- 统计时间窗          
      ,cps.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,cps.bg_code	                          -- BG编码
      ,cps.bg_name	                          -- BG名称
      ,cps.oversea_code	                  -- 区域编码
      ,cps.oversea_desc	                  -- 区域
      ,cps.lv1_prod_rnd_team_code  as lv1_code	                      -- 重量级团队LV1编码
      ,cps.lv1_prod_rd_team_cn_name as lv1_name	                      -- 重量级团队LV1描述
      ,cps.lv2_prod_rnd_team_code as lv2_code	                      -- 重量级团队LV2编码
      ,cps.lv2_prod_rd_team_cn_name as lv2_name	                      -- 重量级团队LV2名称
      ,cps.dimension_group_code   	          -- 量纲分组编码            
      ,cps.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,cps.dimension_group_en_name	          -- 量纲分组英文名称        
      ,cps.dimension_subcategory_code    	  -- 量纲子类编码               
      ,cps.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,cps.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      ,cps.currency_code    as currency	                      -- 币种
      ,cps.equip_rev_cons_before_amt	      -- 设备收入额（对价前） 
      ,cps.equip_cost_cons_before_amt	      -- 设备成本额（对价前） 
      ,row_number()over(partition by cps.period_id,cps.scenarios,cps.time_window_code,cps.bg_code,cps.oversea_code,cps.lv1_prod_rnd_team_code,cps.lv2_prod_rnd_team_code,
                        cps.dimension_group_code,cps.dimension_subcategory_code ,cps.currency_code order by cps.equip_rev_cons_before_amt desc ) rk 
      from    fin_dm_opt_fop.dm_fop_dimension_cost_rev_sum_t	 cps -- 对价前设备收入、对价前设备成本、   YTD
      where  cps.del_flag = 'N' and  cps.version_code =  v_version_code
      )
      select  
      pss.version_code	                  -- 版本编码
      ,pss.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,pss.time_window_code	              -- 统计时间窗          
      ,pss.period_id	                      -- 会计期
      ,pss.target_period	                  -- 目标时点 无 我来计算的
      ,pss.bg_code	                          -- BG编码
      ,pss.bg_name	                          -- BG名称
      ,pss.oversea_code	                  -- 区域编码
      ,pss.oversea_desc	                  -- 区域
      ,pss.lv1_code	                      -- 重量级团队LV1编码
      ,pss.lv1_name	                      -- 重量级团队LV1描述
      ,pss.lv2_code	                      -- 重量级团队LV2编码
      ,pss.lv2_name	                      -- 重量级团队LV2名称
      ,pss.dimension_group_code   	          -- 量纲分组编码            
      ,pss.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,pss.dimension_group_en_name	          -- 量纲分组英文名称        
      ,pss.dimension_subcategory_code    	  -- 量纲子类编码               
      ,pss.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,pss.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      ,pss.currency	                      -- 币种
      ,pss.equip_rev_cons_before_amt	      -- 设备收入额（对价前） 
      ,pss.equip_cost_cons_before_amt	      -- 设备成本额（对价前） 
      ,case when  pss.rk = 1 then 'N' else 'Y'  end  as  del_flag 
      ,case when  pss.rk = 1 then null else '上游对价前汇总表最小粒度的非最大收入记录'  end  as  remark 
       from    dimension_cost_price_sum pss 
      ;
      v_dml_row_count := sql%rowcount;  -- 收集数据量
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '对价前的价格和成本表灌倒临时表',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
      
      --       更新无量纲和场景的逻辑提前，只需要对对价前表做场景和无量纲得更新
      drop table if exists nodimensions_carryover_win_tmp;
	    create temporary table  nodimensions_carryover_win_tmp
		  as
      with  dimensions_win  as  (
      select    distinct 
       lv1_code	  
       ,lv2_code	  
       ,case when lv1_code in ('137565','133277','134557') then  coalesce(dimension_group_code,'无量纲') 
             when lv1_code in ('100001','101775')   then  coalesce( dimension_subcategory_code,'无量纲')
             else 'lv2' end as dimension_code   
      from   dm_fop_dimension_cost_price_sum_tmp
      where  lv1_code    in ('137565','133277','134557','100001','101775','100011') 
      )
      select  dw.lv1_code	  
       ,dw.lv2_code	
       ,dw.dimension_code
      from  dimensions_win dw 
      where  dw.dimension_code in ('无量纲')
      and  not exists (select  1  from  dimensions_win win where   win.dimension_code not in ('无量纲') 
                                and win.lv1_code = dw.lv1_code	
                                and win.lv2_code = dw.lv2_code	) ;
      -- 更新无量纲的逻辑
      update  dm_fop_dimension_cost_price_sum_tmp set scenarios = '量纲分组' , dimension_group_code   = coalesce(dimension_group_code,'NODIM'),dimension_group_cn_name   = coalesce(dimension_group_cn_name,'无分组'),dimension_group_en_name =coalesce(dimension_group_en_name,'NODIM'  )
      ,remark = remark||'，更新量纲分组，无值更新为NODIM'
      where    lv1_code in ('137565','133277','134557') ;
      update  dm_fop_dimension_cost_price_sum_tmp set scenarios = '量纲子类', dimension_subcategory_code = coalesce(dimension_subcategory_code,'NOSUB'),dimension_subcategory_cn_name   =coalesce(dimension_subcategory_cn_name,'无子类'),dimension_subcategory_en_name =coalesce(dimension_subcategory_en_name,'NOSUB' ) , dimension_group_code   = null,dimension_group_cn_name   = null,dimension_group_en_name =null
      ,remark = remark||'，更新量纲子类，无值更新为NOSUB,同事将分组的值更新为null'
      where    lv1_code in ('100001','101775')  ;
--       更新场景字段的逻辑
      update   dm_fop_dimension_cost_price_sum_tmp  tmp    
        SET tmp.scenarios='LV2',tmp.dimension_group_code   = NULL,tmp.dimension_group_cn_name   = NULL,tmp.dimension_group_en_name =NULL,tmp.dimension_subcategory_code =NULL,tmp.dimension_subcategory_cn_name   =NULL,tmp.dimension_subcategory_en_name =NULL ,tmp.remark=tmp.remark||'，更新场景的LV2，并把对应的量纲字段清空'
        from  nodimensions_carryover_win_tmp upd 
        where   1=1 
        and  tmp.lv1_code = upd.lv1_code  
        and tmp.lv2_code = upd.lv2_code  ;
        
        v_dml_row_count := sql%rowcount;  -- 收集数据量
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '对价前的价格和成本表灌倒临时表先把场景和对应的无量纲逻辑做处理',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
        
--       select   *   from    fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t  limit  1000
      -- 2.先把发货量、收货量的数据全部灌到临时表，后面用来关联收入和成本 -- 收入量
      drop table if exists dm_fop_dimension_carryover_sum_tmp;
	    create temporary table  dm_fop_dimension_carryover_sum_tmp
		  as
      with   carryover_sum   as  (
      select     
      qty.version_code	                  -- 版本编码
      ,qty.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,qty.time_window_code	              -- 统计时间窗          
      ,qty.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,qty.bg_code	                          -- BG编码
      ,qty.bg_name	                          -- BG名称
      ,qty.oversea_code	                  -- 区域编码
      ,qty.oversea_desc	                  -- 区域
      ,qty.lv1_prod_rnd_team_code  as lv1_code	                      -- 重量级团队LV1编码
      ,qty.lv1_prod_rd_team_cn_name as lv1_name	                      -- 重量级团队LV1描述
      ,qty.lv2_prod_rnd_team_code as lv2_code	                      -- 重量级团队LV2编码
      ,qty.lv2_prod_rd_team_cn_name as lv2_name	                      -- 重量级团队LV2名称
      ,qty.dimension_group_code   	          -- 量纲分组编码            
      ,qty.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,qty.dimension_group_en_name	          -- 量纲分组英文名称        
      ,qty.dimension_subcategory_code    	  -- 量纲子类编码               
      ,qty.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,qty.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      -- 币种
      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,qty.ship_qty	                      -- 发货量（历史） 
      ,qty.rev_qty	                          -- 收入量（历史） 
      ,row_number()over(partition by qty.period_id,qty.scenarios,qty.time_window_code,qty.bg_code,qty.oversea_code,qty.lv1_prod_rnd_team_code,qty.lv2_prod_rnd_team_code,
                        qty.dimension_group_code,qty.dimension_subcategory_code   order by qty.rev_qty desc ) rk 
      from    fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t	  qty    --  发货量、收入量、结转量基线的数据来源表   月度  转YTD
      where  qty.del_flag = 'N'  and  qty.version_code =  v_version_code
      )
       select     
      css.version_code	                  -- 版本编码
      ,css.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,css.time_window_code	              -- 统计时间窗          
      ,css.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,css.bg_code	                          -- BG编码
      ,css.bg_name	                          -- BG名称
      ,css.oversea_code	                  -- 区域编码
      ,css.oversea_desc	                  -- 区域
      ,css.lv1_code	                      -- 重量级团队LV1编码
      ,css.lv1_name	                      -- 重量级团队LV1描述
      ,css.lv2_code	                      -- 重量级团队LV2编码
      ,css.lv2_name	                      -- 重量级团队LV2名称
      ,css.dimension_group_code   	          -- 量纲分组编码            
      ,css.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,css.dimension_group_en_name	          -- 量纲分组英文名称        
      ,css.dimension_subcategory_code    	  -- 量纲子类编码               
      ,css.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,css.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      -- 币种
      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,css.ship_qty	                      -- 发货量（历史） 
      ,css.rev_qty	                          -- 收入量（历史） 
      ,case when  css.rk = 1 then 'N' else 'Y'  end  as  del_flag 
      ,case when  css.rk = 1 then null else '上游对价前汇总表最小粒度的非最大收入量记录'  end  as  remark 
      from    carryover_sum	  css
      ;
      v_dml_row_count := sql%rowcount;  -- 收集数据量
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => '对结转量表灌倒临时表',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
      -- 3.关联上述两表，得到
      drop table if exists dm_fop_dimension_base_process_tmp;
	    create temporary table  dm_fop_dimension_base_process_tmp
		  as
      select    -- 157226
      cps.version_code	                  -- 版本编码
      ,cps.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,cps.time_window_code	              -- 统计时间窗          
      ,cps.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,cps.bg_code	                          -- BG编码
      ,cps.bg_name	                          -- BG名称
      ,cps.oversea_code	                  -- 区域编码
      ,cps.oversea_desc	                  -- 区域
      ,cps.lv1_code	                      -- 重量级团队LV1编码
      ,cps.lv1_name	                      -- 重量级团队LV1描述
      ,cps.lv2_code	                      -- 重量级团队LV2编码
      ,cps.lv2_name	                      -- 重量级团队LV2名称
      ,cps.dimension_group_code   	          -- 量纲分组编码            
      ,cps.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,cps.dimension_group_en_name	          -- 量纲分组英文名称        
      ,cps.dimension_subcategory_code    	  -- 量纲子类编码               
      ,cps.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,cps.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,cps.currency	                      -- 币种
      ,cps.equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,cps.equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,qty.ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,qty.rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when cps.del_flag='Y' or  qty.del_flag='Y' then 'Y'  else 'N'  end  del_flag
      ,cps.del_flag||qty.del_flag||'，'||cps.remark||','||qty.remark  as   remark 
--       ,coalesce(cps.remark,'SNULL')||','||coalesce(qty.remark,'SNULL')  as   remark 
      from    dm_fop_dimension_cost_price_sum_tmp	 cps 
      left  join  dm_fop_dimension_carryover_sum_tmp	  qty 
      on 
      qty.version_code =  cps.version_code                
      and qty.scenarios	 =     cps.scenarios              
      and qty.time_window_code	=   cps.time_window_code           
      and qty.period_id	 =     cps.period_id             
      and qty.bg_code	 =    cps.bg_code                   
      and qty.oversea_code	=   cps.oversea_code                
      and qty.lv1_code	=     cps.lv1_code                 
      and qty.lv2_code	=     cps.lv2_code                 
      and coalesce(qty.dimension_group_code ,'SNULLC') = 	coalesce(cps.dimension_group_code ,'SNULLC')   
      and coalesce(qty.dimension_subcategory_code  ,'SNULLC')= coalesce( cps.dimension_subcategory_code,'SNULLC') 
--       and qty.del_flag = cps.del_flag 
      ;
      v_dml_row_count := sql%rowcount;  -- 收集数据量
        perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 6,
        p_log_cal_log_desc => '对价前的价格和成本临时表和结转量表做关联处理',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
      -- 开始分别得到YTD Q H Y的数据
--       SELECT  unnest(ARRAY['Y','Q']), time_window_code FROM  (
--       select  DISTINCT  time_window_code  from   fin_dm_opt_fop.dm_fop_dimension_info_t  where  time_window_code = 'YTD'  )a
--       分开按照年  YTD  季度  半年度的数据插入到结果表
       -- YTD和年度数据，值一致
      drop table if exists dm_fop_dimension_base_process_all_tmp;
	    create temporary table  dm_fop_dimension_base_process_all_tmp
		  as  
      select       
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,UNNEST(ARRAY[left(base.period_id::VARCHAR,4),base.period_id::VARCHAR||'YTD'])    AS  target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.dimension_group_code   	          -- 量纲分组编码            
      ,base.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,base.dimension_group_en_name	          -- 量纲分组英文名称        
      ,base.dimension_subcategory_code    	  -- 量纲子类编码               
      ,base.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,base.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.del_flag
      ,base.remark
      from   dm_fop_dimension_base_process_tmp  base 
      union  all 
       -- 计算季度
      select     -- 856386
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期 
      ,left(base.period_id::VARCHAR,4)||'Q'||(CEIL(right(base.period_id::VARCHAR,2)::numeric / 3)::VARCHAR)     AS  target_period             -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.dimension_group_code   	          -- 量纲分组编码            
      ,base.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,base.dimension_group_en_name	          -- 量纲分组英文名称        
      ,base.dimension_subcategory_code    	  -- 量纲子类编码               
      ,base.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,base.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,base.currency	                      -- 币种
      -- 以下4个指标，当月份在3以内的时候，取本身，否则向前减去1个季度的指标值
      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.equip_rev_cons_before_amt 
        else  base.equip_rev_cons_before_amt - qd.equip_rev_cons_before_amt  end  as    equip_rev_cons_before_amt
      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.equip_cost_cons_before_amt 
        else  base.equip_cost_cons_before_amt - qd.equip_cost_cons_before_amt  end as    equip_cost_cons_before_amt
      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.ship_qty 
        else  base.ship_qty - qd.ship_qty   end  as    ship_qty
      -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.rev_qty 
         else  base.rev_qty - qd.rev_qty  end   as    rev_qty  -- SELECT  COUNT(*)
      ,base.del_flag
      ,base.remark -- select  count(1)
      from   dm_fop_dimension_base_process_tmp  base -- 201666
      LEFT JOIN ( SELECT    case when right(q1.period_id::VARCHAR,1) = '3' then  2 
                              when right(q1.period_id::VARCHAR,1) = '6' then  3 
                         else  4 end  as q_flag, 
                         left(q1.period_id::VARCHAR,4) as y_flag,
                  q1.version_code,q1.scenarios,q1.time_window_code,q1.bg_code,q1.oversea_code,q1.lv1_code,q1.lv2_code,  
                  q1.dimension_group_code,q1.dimension_subcategory_code,q1.currency
                  ,q1.equip_rev_cons_before_amt,
                  q1.equip_cost_cons_before_amt,q1.ship_qty,q1.rev_qty,q1.del_flag,q1.remark
                  from  dm_fop_dimension_base_process_tmp  q1  where  right(q1.period_id::VARCHAR,1) in ('3','6','9')
                  ) qd -- 46860
              
      on left(base.period_id::VARCHAR,4) = qd.y_flag
       and   CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = qd.q_flag  
       and    base.version_code = qd.version_code 
       and   base.scenarios =  qd.scenarios  
       and   base.time_window_code =  qd.time_window_code  
       and  base.bg_code = qd.bg_code
       and  base.oversea_code = qd.oversea_code   
       and  base.lv1_code = qd.lv1_code   
       and  base.lv2_code = qd.lv2_code
       and  coalesce(base.dimension_group_code,'SNULLC') = coalesce(qd.dimension_group_code,'SNULLC') 
       and  coalesce(base.dimension_subcategory_code,'SNULLC') = coalesce(qd.dimension_subcategory_code,'SNULLC') 
       and   base.currency = qd.currency 
       and  base.del_flag =qd.del_flag
       and  base.remark = qd.remark
      union  all 
      -- 计算半年度
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期 
      ,left(base.period_id::VARCHAR,4)||'H'||(CEIL(right(base.period_id::VARCHAR,2)::numeric / 6)::VARCHAR)     AS  target_period             -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.dimension_group_code   	          -- 量纲分组编码            
      ,base.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,base.dimension_group_en_name	          -- 量纲分组英文名称        
      ,base.dimension_subcategory_code    	  -- 量纲子类编码               
      ,base.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,base.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,base.currency	                      -- 币种
      -- 以下4个指标，当月份在3以内的时候，取本身，否则向前减去1个季度的指标值
      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.equip_rev_cons_before_amt 
        else  base.equip_rev_cons_before_amt - qd.equip_rev_cons_before_amt  end  as    equip_rev_cons_before_amt
      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.equip_cost_cons_before_amt 
        else  base.equip_cost_cons_before_amt - qd.equip_cost_cons_before_amt  end as    equip_cost_cons_before_amt
      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.ship_qty 
        else  base.ship_qty - qd.ship_qty   end  as    ship_qty
      -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.rev_qty 
         else  base.rev_qty - qd.rev_qty  end   as    rev_qty
       ,base.del_flag
      ,base.remark
      from   dm_fop_dimension_base_process_tmp  base
      LEFT JOIN ( SELECT  2  as q_flag, 
                         left(q1.period_id::VARCHAR,4) as y_flag,
                  q1.version_code,q1.scenarios,q1.time_window_code,q1.bg_code,q1.oversea_code,q1.lv1_code,q1.lv2_code,  
                  q1.dimension_group_code,q1.dimension_subcategory_code,q1.currency
                  ,q1.equip_rev_cons_before_amt,q1.del_flag,q1.remark,
                  q1.equip_cost_cons_before_amt,q1.ship_qty,q1.rev_qty  
                  from  dm_fop_dimension_base_process_tmp  q1  where  right(q1.period_id::VARCHAR,1) ='6' 
                  ) qd
      on  left(base.period_id::VARCHAR,4) = qd.y_flag
       and   CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = qd.q_flag  
       and    base.version_code = qd.version_code 
       and base.scenarios =  qd.scenarios  
       and base.time_window_code =  qd.time_window_code  
       and  base.bg_code = qd.bg_code
       and  base.oversea_code = qd.oversea_code   
       and  base.lv1_code = qd.lv1_code   
       and  base.lv2_code = qd.lv2_code
       and  coalesce(base.dimension_group_code,'SNULLC') = coalesce(qd.dimension_group_code,'SNULLC') 
       and  coalesce(base.dimension_subcategory_code,'SNULLC') = coalesce(qd.dimension_subcategory_code,'SNULLC') 
       and   base.currency = qd.currency 
       and  base.del_flag =qd.del_flag
       and  base.remark = qd.remark
        ;
       
       
  v_dml_row_count := sql%rowcount;  -- 收集数据量
  
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 7,
        p_log_cal_log_desc => '所有指标按照年度、半年度、季度、ytd分步骤，全部都临时表，此时却均本均价',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
        
        -- 在这里把均本均价的数据插进来
-- 4.把均本均价的数据插入到临时表，后面关联用  select   *   from   dm_fop_dimension_unit_cost_price_sum_tmp
      drop table if exists dm_fop_dimension_unit_cost_price_sum_tmp;
	    create temporary table  dm_fop_dimension_unit_cost_price_sum_tmp    
      as    
      with    unit_cost_price_sum  as (
      select  
       cpt.version_code	      		    -- 版本编码
       ,cpt.period_id					    -- 会计期
       ,cpt.time_window_code			    -- 统计时间窗          
       ,cpt.bg_code						    -- BG编码
       ,cpt.bg_name						    -- BG名称
       ,cpt.oversea_code				    -- 区域编码
       ,cpt.oversea_desc				    -- 区域名称
       ,cpt.lv1_prod_rnd_team_code		as lv1_code    -- 重量级团队LV1编码
       ,cpt.lv1_prod_rd_team_cn_name		as lv1_cn_name    -- 重量级团队LV1中文描述
       ,cpt.lv1_prod_rd_team_en_name	 	as lv1_en_name   -- 重量级团队LV1英文描述
       ,cpt.lv2_prod_rnd_team_code			as lv2_code    -- 重量级团队LV2编码
       ,cpt.lv2_prod_rd_team_cn_name		as lv2_cn_name    -- 重量级团队LV2中文描述
       ,cpt.lv2_prod_rd_team_en_name		as lv2_en_name    -- 重量级团队LV2英文描述
       ,cpt.scenarios	                    -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
       ,cpt.dimension_group_code   -- 量纲分组编码  
       ,cpt.dimension_group_cn_name        	  -- 量纲分组中文名                
       ,cpt.dimension_group_en_name	          -- 量纲分组英文名称   
       ,cpt.dimension_subcategory_code    	  -- 量纲子类编码  
       ,cpt.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
       ,cpt.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
       ,cpt.cost_unit_price					-- 成本单位平均价
       ,cpt.rev_unit_price					-- 收入单位平均价
       ,cpt.currency_code  as  currency               	-- 币种                                            
       ,cpt.source_table					-- 来源表 select   *
       ,row_number()over(partition by cpt.period_id,cpt.scenarios,cpt.time_window_code,cpt.bg_code,cpt.oversea_code,cpt.lv1_prod_rnd_team_code,cpt.lv2_prod_rnd_team_code,
                        cpt.dimension_group_code,cpt.dimension_subcategory_code ,cpt.currency_code  order by cpt.rev_unit_price desc ) rk 
       from    fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t	  cpt    --  均本均价的汇总表   月度  转YTD 697594
      where  cpt.del_flag = 'N'  and  cpt.version_code = v_version_code
      )
       select  
       cps.version_code	      		    -- 版本编码
       ,cps.period_id					    -- 会计期
       ,cps.time_window_code			    -- 统计时间窗          
       ,cps.bg_code						    -- BG编码
       ,cps.bg_name						    -- BG名称
       ,cps.oversea_code				    -- 区域编码
       ,cps.oversea_desc				    -- 区域名称
       ,cps.lv1_code    -- 重量级团队LV1编码
       ,cps.lv1_cn_name    -- 重量级团队LV1中文描述
       ,cps.lv1_en_name   -- 重量级团队LV1英文描述
       ,cps.lv2_code    -- 重量级团队LV2编码
       ,cps.lv2_cn_name    -- 重量级团队LV2中文描述
       ,cps.lv2_en_name    -- 重量级团队LV2英文描述
       ,cps.scenarios	                    -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
       ,cps.dimension_group_code   -- 量纲分组编码  
       ,cps.dimension_group_cn_name        	  -- 量纲分组中文名                
       ,cps.dimension_group_en_name	          -- 量纲分组英文名称   
       ,cps.dimension_subcategory_code    	  -- 量纲子类编码  
       ,cps.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
       ,cps.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
       ,cps.cost_unit_price					-- 成本单位平均价
       ,cps.rev_unit_price					-- 收入单位平均价
       ,cps.currency               	-- 币种                                            
       ,cps.source_table					-- 来源表 select   *
       ,case when cps.rk=1 then 'N' else 'Y'  end  as  del_flag
       ,case when cps.rk=1 then null  else '上游均本均价汇总表最小粒度的非最大均价记录'  end  as  remark
       from    unit_cost_price_sum  cps 
      ;
      
        v_dml_row_count := sql%rowcount;  -- 收集数据量
  
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 8,
        p_log_cal_log_desc => '把均本均价的表灌倒临时表',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      

      -- 以前面关联出来的收入量、发货量、设备成本额、设备收入额的数据，关联均本均价的数据，其中YTD和年度的均本均价用关联的数据，其他的率指标直接使用计算出来
       drop table if exists dm_fop_dimension_all_item_process_tmp;
	    create temporary table  dm_fop_dimension_all_item_process_tmp
		  as
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.dimension_group_code   	          -- 量纲分组编码            
      ,base.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,base.dimension_group_en_name	          -- 量纲分组英文名称        
      ,base.dimension_subcategory_code    	  -- 量纲子类编码               
      ,base.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,base.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据 
      ,case when ( length(base.target_period)=4 or right(base.target_period,3) = 'YTD') then ucp.cost_unit_price
      else 
          case when base.rev_qty= 0  and base.equip_cost_cons_before_amt = 0 then null
               when  base.rev_qty= 0  and base.equip_cost_cons_before_amt != 0 then -999999
               else  base.equip_cost_cons_before_amt/base.rev_qty	 end
               end   as unit_cost -- 单位成本 设备成本/收入量
      ,case when ( length(base.target_period)=4 or right(base.target_period,3) = 'YTD') then ucp.rev_unit_price
      else 
          case  when  base.rev_qty = 0 and base.equip_rev_cons_before_amt = 0 then null 
                when  base.rev_qty = 0 and base.equip_rev_cons_before_amt != 0 then -999999
                else  base.equip_rev_cons_before_amt	/base.rev_qty	end
                end    as unit_price  -- 单位价格 设备收入/收入量
      ,null::numeric  as  rev_percent -- 收入占比待更新 量纲的收入/LV2的收入
      ,case when  base.ship_qty=0  and base.rev_qty=0 then null 
           when  base.ship_qty=0  and base.rev_qty !=0 then  -999999
           else  base.rev_qty/base.ship_qty  end  as  carryover_rate -- 结转率  收入量/发货量
      ,case when base.equip_rev_cons_before_amt = 0  and  base.equip_rev_cons_before_amt-base.equip_cost_cons_before_amt = 0 then null  
            when base.equip_rev_cons_before_amt = 0  and  base.equip_rev_cons_before_amt-base.equip_cost_cons_before_amt != 0 then -999999 
            else  (1-base.equip_cost_cons_before_amt/base.equip_rev_cons_before_amt) end  as mgp_ratio
--       ,'均本均价YTD和年度的是关联上游表，季度和半年度价与量的计算，结转率都是通过计算'::text   as  remark  -- select  *
      ,case when base.del_flag='Y' or  ucp.del_flag='Y' then 'Y'  else 'N'  end  del_flag
      ,base.del_flag||ucp.del_flag||'，'||base.remark||','||ucp.remark  as   remark 
      from   dm_fop_dimension_base_process_all_tmp  base 
      left  join  dm_fop_dimension_unit_cost_price_sum_tmp ucp 
      on 
      base.version_code =  ucp.version_code                
      and base.scenarios	 =     ucp.scenarios              
      and base.time_window_code	=   ucp.time_window_code           
      and base.period_id	 =     ucp.period_id             
      and base.bg_code	 =    ucp.bg_code                   
      and base.oversea_code	=   ucp.oversea_code                
      and base.lv1_code	=     ucp.lv1_code                 
      and base.lv2_code	=     ucp.lv2_code                 
      and coalesce(base.dimension_group_code ,'SNULLC') = 	coalesce(ucp.dimension_group_code ,'SNULLC')   
      and coalesce(base.dimension_subcategory_code  ,'SNULLC')= coalesce( ucp.dimension_subcategory_code,'SNULLC')  
      and base.currency = ucp.currency
      
      ;
        v_dml_row_count := sql%rowcount;  -- 收集数据量
  
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 9,
        p_log_cal_log_desc => '所有指标按照年度、半年度、季度、ytd分步骤，全部都临时表涵盖均本均价',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      

      
-- 5G&LTE FDD	153324
-- GUC	133661
-- SRAN	101212
-- 5G&LTE TDD	153326
      
     
--     这一段更新无量纲和场景得逻辑提前只对对价前价格和成本表处理       
--        select   *   from    dm_fop_dimension_all_item_process_tmp
--       最后更新无量纲和场景的逻辑
--       drop table if exists nodimensions_carryover_win_tmp;
-- 	    create temporary table  nodimensions_carryover_win_tmp
-- 		  as
--       with  dimensions_win  as  (
--       select    distinct 
--        lv1_code	  
--        ,lv2_code	  
--        ,case when lv1_code in ('137565','133277','134557') then  coalesce(dimension_group_code,'无量纲') 
--              when lv1_code in ('100001','101775')   then  coalesce( dimension_subcategory_code,'无量纲')
--              else 'lv2' end as dimension_code   
--       from   dm_fop_dimension_all_item_process_tmp
--       where  lv1_code    in ('137565','133277','134557','100001','101775','100011') 
--       )
--       select  dw.lv1_code	  
--        ,dw.lv2_code	
--        ,dw.dimension_code
--       from  dimensions_win dw 
--       where  dw.dimension_code in ('无量纲')
--       and  not exists (select  1  from  dimensions_win win where   win.dimension_code not in ('无量纲') 
--                                 and win.lv1_code = dw.lv1_code	
--                                 and win.lv2_code = dw.lv2_code	) ;
--       -- 更新无量纲的逻辑
--       update  dm_fop_dimension_all_item_process_tmp set scenarios = '量纲分组' , dimension_group_code   = coalesce(dimension_group_code,'NODIM'),dimension_group_cn_name   = coalesce(dimension_group_cn_name,'无分组'),dimension_group_en_name =coalesce(dimension_group_en_name,'NODIM'  )
--       ,remark = remark||'，更新量纲分组，无值更新为NODIM'
--       where    lv1_code in ('137565','133277','134557') ;
--       update  dm_fop_dimension_all_item_process_tmp set scenarios = '量纲子类', dimension_subcategory_code = coalesce(dimension_subcategory_code,'NOSUB'),dimension_subcategory_cn_name   =coalesce(dimension_subcategory_cn_name,'无子类'),dimension_subcategory_en_name =coalesce(dimension_subcategory_en_name,'NOSUB' ) , dimension_group_code   = null,dimension_group_cn_name   = null,dimension_group_en_name =null
--       ,remark = remark||'，更新量纲子类，无值更新为NOSUB,同事将分组的值更新为null'
--       where    lv1_code in ('100001','101775')  ;
-- --       更新场景字段的逻辑
--       update   dm_fop_dimension_all_item_process_tmp  tmp    
--         SET tmp.scenarios='LV2',tmp.dimension_group_code   = NULL,tmp.dimension_group_cn_name   = NULL,tmp.dimension_group_en_name =NULL,tmp.dimension_subcategory_code =NULL,tmp.dimension_subcategory_cn_name   =NULL,tmp.dimension_subcategory_en_name =NULL ,tmp.remark=tmp.remark||'，更新场景的LV2，并把对应的量纲字段清空'
--         from  nodimensions_carryover_win_tmp upd 
--         where   1=1 
--         and  tmp.lv1_code = upd.lv1_code  
--         and tmp.lv2_code = upd.lv2_code  ;
        
        

        
      -- 单独处理lv2层的收入
      drop table if exists dm_fop_dimension_upd_rev_percent_process_tmp;
	    create temporary table  dm_fop_dimension_upd_rev_percent_process_tmp
		  as
      with   lv2_sum  as (
      select   
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  as lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency	                      -- 币种 
          ,sum(base.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt 
        from   dm_fop_dimension_all_item_process_tmp  base 
        where  del_flag = 'N'
        group by 
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency	
      ), lv2_ran_sum
        as (
        select   
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  as lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.dimension_subcategory_code	                      -- 币种 
          ,base.currency	                      -- 币种 
          ,sum(base.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt 
          ,sum(base.equip_cost_cons_before_amt)  as equip_cost_cons_before_amt 
        from   dm_fop_dimension_all_item_process_tmp  base 
        where   base.lv2_code in   ('133661', '101212', '153326', '153324') 
        and   dimension_subcategory_code='NOSUB'
        and  del_flag = 'N'
        group by 
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.dimension_subcategory_code	   
          ,base.currency	   
        )
                  
         select   
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,base.lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency	                      -- 币种 
          ,base.equip_rev_cons_before_amt 
          ,lrs.equip_rev_cons_before_amt  as ran_rev_cons_before_amt
          ,lrs.equip_cost_cons_before_amt  as ran_cost_cons_before_amt
        from   lv2_sum  base 
        left join lv2_ran_sum lrs 
        on  base.version_code = lrs.version_code	                  -- 版本编码
         and  base.scenarios=	lrs.scenarios                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
         and  base.time_window_code=	 lrs.time_window_code             -- 统计时间窗 
         and  base.period_id=	  lrs.period_id                    -- 会计期
         and  base.target_period=	 lrs.target_period                 -- 目标时点 无 我来计算的
         and  base.bg_code=	  lrs.bg_code                        -- BG编码
         and  base.oversea_code=	lrs.oversea_code                  -- 区域编码
         and  base.lv1_code=	lrs.lv1_code                      -- 重量级团队LV1编码
         and  base.lv2_vir= lrs.lv2_vir   -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
         and  base.currency=	lrs.currency 
          ;              -- 币种  ;
      
       -- 更新收入占比,RAN里面的无量纲是相同的一个值，所以先聚合，在关联
        update   dm_fop_dimension_all_item_process_tmp  tmp    
        SET tmp.rev_percent=  case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324') and  tmp.dimension_subcategory_code='NOSUB' 
                                  then  
                                     case when upd.equip_rev_cons_before_amt = 0  and upd.ran_rev_cons_before_amt = 0 then null 
                                         when upd.equip_rev_cons_before_amt = 0   and upd.ran_rev_cons_before_amt != 0 then -999999 
                                         else  upd.ran_rev_cons_before_amt/upd.equip_rev_cons_before_amt end
                                  else
                                    case when upd.equip_rev_cons_before_amt = 0  and tmp.equip_rev_cons_before_amt = 0 then null 
                                         when upd.equip_rev_cons_before_amt = 0   and tmp.equip_rev_cons_before_amt != 0 then -999999 
                                         else  tmp.equip_rev_cons_before_amt/upd.equip_rev_cons_before_amt end 
                                 end,
              tmp.mgp_ratio= case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324') and  tmp.dimension_subcategory_code='NOSUB' 
                                  then
                                     case when upd.ran_rev_cons_before_amt = 0  and  upd.ran_cost_cons_before_amt = 0 then null  
                                      when upd.ran_rev_cons_before_amt = 0  and  upd.ran_cost_cons_before_amt != 0 then -999999 
                                      else  (1-upd.ran_cost_cons_before_amt/upd.ran_rev_cons_before_amt) end 
                                  else 
                                       tmp.mgp_ratio  end  
        from  dm_fop_dimension_upd_rev_percent_process_tmp upd 
        where   1=1  and  tmp.del_flag='N'
        and   tmp.version_code = upd.version_code  
        and  tmp.scenarios = upd.scenarios  
        and tmp.time_window_code = upd.time_window_code 
        and tmp.period_id=upd.period_id  
        and tmp.target_period=upd.target_period  
        and tmp.bg_code = upd.bg_code
        and tmp.oversea_code = upd.oversea_code 
        and tmp.lv1_code = upd.lv1_code  
        and  case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324')  then 'RAN' else tmp.lv2_code  end = upd.lv2_vir 
           and tmp.currency = upd.currency ;
           
           
           -- 	 --1.开始日志
v_dml_row_count := sql%rowcount;  -- 收集数据量
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 10,
        p_log_cal_log_desc => 'RAN的占比和结转率的计算',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
        

      -- 更新插入TGT接口表 select   *  from   fin_dm_opt_fop.dm_fop_dimension_tgt_period_t ;
      truncate  table   fin_dm_opt_fop.dm_fop_dimension_tgt_period_t ;
      insert  into fin_dm_opt_fop.dm_fop_dimension_tgt_period_t (
      version_code	                  -- 版本编码
      ,scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,time_window_code	              -- 统计时间窗          
      ,period_id	                      -- 会计期
      ,target_period	                  -- 目标时点 无 我来计算的
      ,bg_code	                          -- BG编码
      ,bg_name	                          -- BG名称
      ,oversea_code	                  -- 区域编码
      ,oversea_desc	                  -- 区域
      ,lv1_code	                      -- 重量级团队LV1编码
      ,lv1_name	                      -- 重量级团队LV1描述
      ,lv2_code	                      -- 重量级团队LV2编码
      ,lv2_name	                      -- 重量级团队LV2名称
      ,dimension_group_code   	          -- 量纲分组编码            
      ,dimension_group_cn_name        	  -- 量纲分组中文名                
      ,dimension_group_en_name	          -- 量纲分组英文名称        
      ,dimension_subcategory_code    	  -- 量纲子类编码               
      ,dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      ,currency	                      -- 币种
      ,equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,unit_cost	                      -- 单位成本  dm_fop_dimension_unit_cost_price_sum_t 有数据   cost_unit_price
      ,unit_price	                      -- 单位价格  dm_fop_dimension_unit_cost_price_sum_t 有数据   rev_unit_price
      ,rev_percent	                      -- 收入占比 
      ,carryover_rate	                  -- 结转率   dm_fop_dimension_carryover_sum_t
      ,mgp_ratio	                      -- 制毛率
      ,remark	                          -- 备注
      ,created_by	                      -- 创建人
      ,creation_date	                  -- 创建时间
      ,last_updated_by	                  -- 修改人
      ,last_update_date	              -- 修改时间
      ,del_flag	                      -- 是否删除
      )
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.dimension_group_code   	          -- 量纲分组编码            
      ,base.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,base.dimension_group_en_name	          -- 量纲分组英文名称        
      ,base.dimension_subcategory_code    	  -- 量纲子类编码               
      ,base.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,base.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.unit_cost -- 单位成本 设备成本/收入量
      ,base.unit_price  -- 单位价格 设备收入/收入量
      ,base.rev_percent -- 收入占比待更新 量纲的收入/LV2的收入
      ,base.carryover_rate -- 结转率  收入量/发货量
      ,base.mgp_ratio
      ,base.remark
      ,'-1'  as created_by
      ,now()  as creation_date
      ,'-1'  as  last_updated_by
      ,now()  as  last_update_date
      ,base.del_flag  
      from   dm_fop_dimension_all_item_process_tmp  base 
      
      ;
			
      -- 	 --1.开始日志
v_dml_row_count := sql%rowcount;  -- 收集数据量
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 11,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',插入接口表',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
      delete  from  fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t  where  version_code= v_version_code;
      insert  into fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t(
      version_code	                  -- 版本编码
      ,scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,time_window_code	              -- 统计时间窗          
      ,period_id	                      -- 会计期
      ,target_period	                  -- 目标时点 无 我来计算的
      ,bg_code	                          -- BG编码
      ,bg_name	                          -- BG名称
      ,oversea_code	                  -- 区域编码
      ,oversea_desc	                  -- 区域
      ,lv1_code	                      -- 重量级团队LV1编码
      ,lv1_name	                      -- 重量级团队LV1描述
      ,lv2_code	                      -- 重量级团队LV2编码
      ,lv2_name	                      -- 重量级团队LV2名称
      ,dimension_group_code   	          -- 量纲分组编码            
      ,dimension_group_cn_name        	  -- 量纲分组中文名                
      ,dimension_group_en_name	          -- 量纲分组英文名称        
      ,dimension_subcategory_code    	  -- 量纲子类编码               
      ,dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      ,currency	                      -- 币种
      ,equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,unit_cost	                      -- 单位成本  dm_fop_dimension_unit_cost_price_sum_t 有数据   cost_unit_price
      ,unit_price	                      -- 单位价格  dm_fop_dimension_unit_cost_price_sum_t 有数据   rev_unit_price
      ,rev_percent	                      -- 收入占比 
      ,carryover_rate	                  -- 结转率   dm_fop_dimension_carryover_sum_t
      ,mgp_ratio	                      -- 制毛率
      ,remark	                          -- 备注
      ,created_by	                      -- 创建人
      ,creation_date	                  -- 创建时间
      ,last_updated_by	                  -- 修改人
      ,last_update_date	              -- 修改时间
      ,del_flag	                      -- 是否删除
      )
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.dimension_group_code   	          -- 量纲分组编码            
      ,base.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,base.dimension_group_en_name	          -- 量纲分组英文名称        
      ,base.dimension_subcategory_code    	  -- 量纲子类编码               
      ,base.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,base.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.unit_cost -- 单位成本 设备成本/收入量
      ,base.unit_price  -- 单位价格 设备收入/收入量
      ,base.rev_percent -- 收入占比待更新 量纲的收入/LV2的收入
      ,base.carryover_rate -- 结转率  收入量/发货量
      ,base.mgp_ratio
      ,base.remark
      ,'-1'  as created_by
      ,now()  as creation_date
      ,'-1'  as  last_updated_by
      ,now()  as  last_update_date
      ,base.del_flag  
      from   dm_fop_dimension_all_item_process_tmp  base 
      
      ;

  	-- 写结束日志
    v_dml_row_count := sql%rowcount;  -- 收集数据量
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 12,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl2_name||',目标表中最大版本编码:'||v_version_code||',插入历史表',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
			) ;
	x_success_flag := '2001';
	
	
		
 end;
 $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100 ;