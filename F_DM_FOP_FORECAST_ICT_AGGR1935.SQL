CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_FORECAST_ICT_AGGR(OUT X_SUCCESS_FLAG TEXT)
 RETURNS TEXT
 LANGUAGE PLPGSQL
 NOT FENCED NOT SHIPPABLE
AS $$
 /*
创建时间：
创建人  ：TWX1139790
背景描述：将算法预测后的数据、量本价勾稽后的预测数据以及TGT表的实际数据，整合处理后落入YTD结果表
参数描述：参数一(P_VERSION_CODE)：版本编码202505
          参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_FORECAST_ICT_AGGR();
*/
 
 DECLARE
    V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_FORECAST_ICT_AGGR';
    V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
    V_STEP_NUM   BIGINT := 0; --步骤号
BEGIN
    X_SUCCESS_FLAG := '1';                                 --1表示成功
    
       -- 如果是传 VERSION_CODE 调函数取JAVA传入的 P_VERSION_CODE ，如果是自动调度的则取 当前年月 版本
/*        IF P_VERSION_CODE IS NOT NULL THEN 
        SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
        ELSE */
        SELECT TO_CHAR(CURRENT_DATE,'YYYYMM') AS VERSION_CODE INTO V_VERSION_CODE ;
--        END IF ;
  
  -- LV2层级表数据聚合ICT
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH ICT_LV2_TMP AS (
  -- 聚合为ICT数据
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         'RICT001' AS BG_CODE,
         'ICT' AS BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         SUM(EQUIP_REV_CONS_BEFORE_AMT) AS EQUIP_REV_CONS_BEFORE_AMT,
         SUM(EQUIP_COST_CONS_BEFORE_AMT) AS EQUIP_COST_CONS_BEFORE_AMT,
         SUM(EQUIP_REV_CONS_AFTER_AMT) AS EQUIP_REV_CONS_AFTER_AMT,
         SUM(EQUIP_COST_CONS_AFTER_AMT) AS EQUIP_COST_CONS_AFTER_AMT
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T
      WHERE VERSION_CODE = V_VERSION_CODE
      GROUP BY VERSION_CODE,
               PERIOD_ID,
               TARGET_PERIOD,
               SCENARIOS,
               PHASE_DATE,
               OVERSEA_CODE,
               OVERSEA_DESC,
               LV1_CODE,
               LV1_NAME,
               LV2_CODE,
               LV2_NAME,
               CURRENCY_CODE,
               FCST_TYPE
  ),
  ICT_DATA_TMP AS (
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         DECODE(EQUIP_REV_CONS_BEFORE_AMT,0,-999999,1-(EQUIP_COST_CONS_BEFORE_AMT/EQUIP_REV_CONS_BEFORE_AMT)) AS MGP_RATIO_BEFORE,
         DECODE(EQUIP_REV_CONS_AFTER_AMT,0,-999999,1-(EQUIP_COST_CONS_AFTER_AMT/EQUIP_REV_CONS_AFTER_AMT)) AS MGP_RATIO_AFTER
      FROM ICT_LV2_TMP
  ),
  FOP_TGT_LAST_12YTD_TMP AS (
  -- 取TGT表LV2层级制毛率（对价后）的指标值
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         CURRENCY,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_TGT_PERIOD_T
      WHERE VERSION_CODE = V_VERSION_CODE
      AND CURRENCY = 'CNY'
	  AND BG_NAME = 'ICT'
      AND TARGET_PERIOD = YEAR(CURRENT_TIMESTAMP)-2||'12YTD'   -- 取T-1年12月YTD数据
   )
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.EQUIP_REV_CONS_AFTER_AMT,
         T1.EQUIP_COST_CONS_AFTER_AMT,
         -- 当计算所得的制毛率绝对值<1，直接取计算所得值；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值<1，则取去年12月YTD的制毛率；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值>1，则直接处理为0；
         CASE WHEN ABS(T1.MGP_RATIO_BEFORE) > 1 AND T1.MGP_RATIO_BEFORE <> -999999 AND ABS(T2.MGP_RATIO_BEFORE) < 1 
              THEN T2.MGP_RATIO_BEFORE
              WHEN ABS(T1.MGP_RATIO_BEFORE) > 1 AND T1.MGP_RATIO_BEFORE <> -999999 AND ABS(T2.MGP_RATIO_BEFORE) > 1 
              THEN 0
              ELSE T1.MGP_RATIO_BEFORE
         END AS MGP_RATIO_BEFORE,
         CASE WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) < 1 
              THEN T2.MGP_RATIO_AFTER
              WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) > 1 
              THEN 0
              ELSE T1.MGP_RATIO_AFTER
         END AS MGP_RATIO_AFTER,
         'DM_FOP_YTD_LV2_ARTICULATED_AGGR_T' AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM ICT_DATA_TMP T1
	  LEFT JOIN FOP_TGT_LAST_12YTD_TMP T2 
       ON T1.SCENARIOS = T2.SCENARIOS
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.CURRENCY_CODE = T2.CURRENCY;

  DBMS_OUTPUT.PUT_LINE('LV2层ICT数据已插入结果表');
  
  -- LV1层级表数据聚合ICT
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         MGP_RATIO_AFTER,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH ICT_LV1_TMP AS (
  -- 聚合为ICT数据
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         'RICT001' AS BG_CODE,
         'ICT' AS BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         SUM(EQUIP_REV_CONS_AFTER_AMT) AS EQUIP_REV_CONS_AFTER_AMT,
         SUM(EQUIP_COST_CONS_AFTER_AMT) AS EQUIP_COST_CONS_AFTER_AMT
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T
      WHERE VERSION_CODE = V_VERSION_CODE
      GROUP BY VERSION_CODE,
               PERIOD_ID,
               TARGET_PERIOD,
               SCENARIOS,
               PHASE_DATE,
               OVERSEA_CODE,
               OVERSEA_DESC,
               LV1_CODE,
               LV1_NAME,
               CURRENCY_CODE,
               FCST_TYPE
  ),
  ICT_DATA_TMP AS (
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         DECODE(EQUIP_REV_CONS_AFTER_AMT,0,-999999,1-(EQUIP_COST_CONS_AFTER_AMT/EQUIP_REV_CONS_AFTER_AMT)) AS MGP_RATIO_AFTER
      FROM ICT_LV1_TMP
  ),
  FOP_TGT_LAST_12YTD_TMP AS (
  -- 取TGT表LV1层级制毛率（对价后）的指标值
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_TGT_PERIOD_T
      WHERE VERSION_CODE = V_VERSION_CODE
      AND CURRENCY = 'CNY'
	  AND BG_NAME = 'ICT'
      AND LV2_CODE IS NULL   -- 取LV2编码为空，LV1层级的数据
      AND TARGET_PERIOD = YEAR(CURRENT_TIMESTAMP)-2||'12YTD'   -- 取T-1年12月YTD数据
   )
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.EQUIP_REV_CONS_AFTER_AMT,
         T1.EQUIP_COST_CONS_AFTER_AMT,
         -- 当计算所得的制毛率绝对值<1，直接取计算所得值；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值<1，则取去年12月YTD的制毛率；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值>1，则直接处理为0；
         CASE WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) < 1 
              THEN T2.MGP_RATIO_AFTER
              WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) > 1 
              THEN 0
              ELSE T1.MGP_RATIO_AFTER
         END AS MGP_RATIO_AFTER,
         'DM_FOP_YTD_LV2_ARTICULATED_AGGR_T' AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM ICT_DATA_TMP T1
	  LEFT JOIN FOP_TGT_LAST_12YTD_TMP T2 
       ON T1.SCENARIOS = T2.SCENARIOS
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.CURRENCY_CODE = T2.CURRENCY;

  DBMS_OUTPUT.PUT_LINE('LV1层ICT数据已插入结果表');
  
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T';
  
  RETURN 'SUCCESS';
  
END$$
/