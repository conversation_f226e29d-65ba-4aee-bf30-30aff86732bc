CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_dimension_lv2_tgt_period_filled_t"(OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
 /*
创建时间：2025-06-7
创建人  ：wwx1077703
背景描述：将汇总层的数据插入到TGT的LV2接口表，具体逻辑如下
LV1LV2的对接后的收入成本的数据从盈利量纲-ICT损益汇总表 汇总表（dm_fop_dimension_ict_pl_sum_t）获取
设备前收入  设备前成本从盈利量对价前纲价格成本汇总表（dm_fop_dimension_cost_price_sum_t）获取
制毛率：=（对价后收入-对价后成本）/对价后收入
MAC调整率：（对价后收入-对价前收入）/对价后收入
制毛调整率：=（对价后收入-对价后成本）/对价后收入-（对价前收入-对价前成本）/对价前收入


参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_filled_t();
*/
 
 declare
	v_sp_name VARCHAR(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_lv2_tgt_period_filled_t()';
	v_tbl_name VARCHAR(100) := 'fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t';
	v_version_code VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   BIGINT; --步骤号
	v_dml_row_count  NUMBER DEFAULT 0 ;
  v_max_ytd  VARCHAR(50);  -- 最大期次的上一年
  lv1lv2_fields TEXT[] := ARRAY['equip_rev_cons_after_amt','mgp_ratio_after','mgp_ratio_before','mca_adjust_ratio','mgp_adjust_ratio']; -- 量纲需要调整的字段数组
  lv1lv2_fix_cd  TEXT := '  1=1 ';
  nolv1_fix_cd  TEXT := '  tmp.scenarios != ''LV1''  ';
  lv1lv2_upd TEXT; -- LV2的更新
  field   VARCHAR(50);
  lv1lv2_query TEXT; -- 量纲的查询，用于更新填充空置
	


begin
	x_success_flag := '1';                                 --1表示成功
	
	     
        
        select  (left(max(period_id)::VARCHAR,4)::numeric-1)::VARCHAR  into  v_max_ytd  from fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t ;
	
-- 	 --1.开始日志
--   v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => 'LV2层级数据汇总表'||v_tbl_name||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  

-- 1.把量纲表的TGT表的数据拿过来插入到临时表，用于后续的计算,只拿YTD的数据
      -- 同纬度下，取最大的一条数据  按照收入最大来取
      drop table if exists dimension_lv2_tgt_period_tmp;
	    create temporary table  dimension_lv2_tgt_period_tmp
		  as
        SELECT
          tgt.version_code,
          tgt.scenarios,
          tgt.time_window_code,
          tgt.period_id,
          tgt.target_period,
          tgt.bg_code,
          tgt.bg_name,
          tgt.oversea_code,
          tgt.oversea_desc,
          tgt.lv1_code,
          tgt.lv1_name,
          tgt.lv2_code,
          tgt.lv2_name,
          tgt.currency,
          tgt.equip_rev_cons_before_amt,  -- 对价前收入
          tgt.equip_cost_cons_before_amt, -- 对价前成本
          tgt.equip_rev_cons_after_amt,   -- 对价后收入
          tgt.equip_cost_cons_after_amt,  -- 对价后成本
          case when coalesce(tgt.equip_rev_cons_after_amt,0) = 0 then null else  (1-  tgt.equip_cost_cons_after_amt/tgt.equip_rev_cons_after_amt) end  as mgp_ratio_after,     -- 制毛率 对价后
          case when coalesce(tgt.equip_rev_cons_before_amt,0) = 0 then null else  (1-  tgt.equip_cost_cons_before_amt/tgt.equip_rev_cons_before_amt) end  as mgp_ratio_before,    -- 制毛率 对价前
          case when coalesce(tgt.equip_rev_cons_after_amt,0) = 0 then null else  (1-  tgt.equip_rev_cons_before_amt/tgt.equip_rev_cons_after_amt) end  as mca_adjust_ratio,    -- MCA调整率
          (case when coalesce(tgt.equip_rev_cons_after_amt,0) = 0 then null else  (1-  tgt.equip_rev_cons_after_amt/tgt.equip_rev_cons_after_amt) end -
          case when coalesce(tgt.equip_rev_cons_before_amt,0) = 0 then null else  (1-  tgt.equip_cost_cons_before_amt/tgt.equip_rev_cons_before_amt) end ) as mgp_adjust_ratio ,    -- 制毛调整率
         '计算率指标'  as remark 
        FROM
          fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t tgt 
        WHERE
          tgt.del_flag = 'N' 
          AND RIGHT ( target_period, 3 ) = 'YTD'
          ;
       
          
    BEGIN
			-- 有量纲的填充
-- 			FOR field  IN SELECT * FROM unnest(dimens_fields) LOOP
			FOR idx IN 1..array_length(lv1lv2_fields, 1) LOOP
			field := replace(lv1lv2_fields[idx], E'\\{', ''); -- 去除括号
			drop table if exists dm_fop_dimension_upd_tmp;    
			-- 动态拼接 SQL 查询，替换字段名			更新表的语句用一个范围，更新动作分别处理
			lv1lv2_query := '
				create temporary table dm_fop_dimension_upd_tmp
				 as
				 select   
					version_code, 
					scenarios, 
					time_window_code, 
					period_id, 
					bg_code,  
					oversea_code,
					lv1_code, 
					lv2_code, 
					currency,
					data_upt  as  '|| field ||'
					from   (
			 select   
					  wnf.version_code, 
					  wnf.scenarios, 
					  wnf.time_window_code, 
					  wnf.period_id, 
					  wnf.bg_code, 
					  wnf.oversea_code, 
					  wnf.lv1_code, 
					  wnf.lv2_code, 
					  wnf.currency, 
				   ROW_NUMBER()over(partition by wnf.version_code,wnf.scenarios,wnf.time_window_code,wnf.bg_code,wnf.oversea_code,wnf.lv1_code,wnf.lv2_code,wnf.currency,wnf.period_id 
                            order by (wnf.period_id - inf.period_id)  )  as  rn ,
					 coalesce(inf.'|| field ||',0)  as   data_upt
							 from   (  -- 321
							select version_code, scenarios, time_window_code, period_id, bg_code, oversea_code,lv1_code, lv2_code,currency
							from   dimension_lv2_tgt_period_tmp    
							where   '|| field || ' is null
							) wnf 
							left  join dimension_lv2_tgt_period_tmp inf 
							on inf.version_code = wnf.version_code  
                 and  inf.scenarios = wnf.scenarios  
                 and inf.time_window_code = wnf.time_window_code 
                 and inf.bg_code=wnf.bg_code  
								 and inf.lv1_code = wnf.lv1_code  
                 and inf.oversea_code = wnf.oversea_code  
								 and coalesce(inf.lv2_code,''SNULLC'')=coalesce(wnf.lv2_code,''SNULLC'') 
                 and inf.currency = wnf.currency  
								 and inf.period_id < wnf.period_id  
                 and  inf.'|| field ||'  is  not  null 
							)a 
							where  a.rn=1  ' ;
              
              IF field IN ('mca_adjust_ratio','mgp_adjust_ratio','mgp_ratio_before') THEN
                  -- 有量刚，均本均价 结转率填充
                  lv1lv2_upd := '
                  update   dimension_lv2_tgt_period_tmp  tmp    
                  SET tmp.'||field|| '=upd.'||field|| '
                  from  dm_fop_dimension_upd_tmp upd 
                  where   '||nolv1_fix_cd||'
                  and   tmp.'|| field ||'  is     null 
                  and   tmp.version_code = upd.version_code  
                  and  tmp.scenarios = upd.scenarios  
                  and tmp.time_window_code = upd.time_window_code 
                  and tmp.bg_code=upd.bg_code  
                  and tmp.lv1_code = upd.lv1_code  
                  and tmp.oversea_code = upd.oversea_code  
                  and coalesce(tmp.lv2_code,''SNULLC'')=coalesce(upd.lv2_code,''SNULLC'') 
                  and tmp.currency = upd.currency  
                  and tmp.period_id = upd.period_id ' ;
                  
                  
                  EXECUTE lv1lv2_query;
                  EXECUTE lv1lv2_upd;
                  drop table if exists dm_fop_dimension_upd_tmp;
              END IF;
              
              IF field IN ('equip_rev_cons_after_amt','mgp_ratio_after') THEN
                  -- 无量刚，占比填充
                  lv1lv2_upd := '
                  update   dimension_lv2_tgt_period_tmp  tmp    
                  SET tmp.'||field|| '='||0|| '
                  from  dm_fop_dimension_upd_tmp upd 
                  where   '||lv1lv2_fix_cd||'
                  and   tmp.'|| field ||'  is     null 
                  and   tmp.version_code = upd.version_code  
                  and  tmp.scenarios = upd.scenarios  
                  and tmp.time_window_code = upd.time_window_code 
                  and tmp.bg_code=upd.bg_code  
                  and tmp.lv1_code = upd.lv1_code  
                  and tmp.oversea_code = upd.oversea_code  
                  and coalesce(tmp.lv2_code,''SNULLC'')=coalesce(upd.lv2_code,''SNULLC'') 
                  and tmp.currency = upd.currency  
                  and tmp.period_id = upd.period_id ' ;
                  
                  
                  EXECUTE lv1lv2_query;
                  EXECUTE lv1lv2_upd;
                  drop table if exists dm_fop_dimension_upd_tmp;
              END IF;
              
              

							
							
							

			-- 执行动态 SQL
-- 			EXECUTE dimens_query;
-- 			EXECUTE dimens_upd;
			END LOOP;
			END;
       
        
        
        
      
       
      
      -- 更新YTD的数据，在填充表填充数据
          DROP TABLE
          IF
            EXISTS max_period_rate_ytd_data_tmp;
          CREATE TEMPORARY TABLE max_period_rate_ytd_data_tmp -- 3124
          AS SELECT
            'YTD' AS data_flag,
            version_code,
            scenarios,
            time_window_code,
            period_id,
            bg_code,
            oversea_code,
            lv1_code,
            lv2_code,
            currency,
            equip_rev_cons_after_amt AS equip_rev_cons_after_amt_ytd_data,
            mgp_ratio_after AS mgp_ratio_ytd_data,
            mca_adjust_ratio AS mca_adjust_ratio_ytd_data,
            mgp_adjust_ratio AS mgp_adjust_ratio_ytd_data,
            '' remark 
          FROM
            dimension_lv2_tgt_period_tmp 
          WHERE
            period_id = v_max_ytd || '12';

      


      
--       把前面汇总到一起的数据，再计算率指标插入到结果表
--       最后把数据更新到接口表
      -- 更新插入TGT接口表
      truncate  table   fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t ;
      insert  into fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t (
      version_code	                  -- 版本编码
      ,scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,time_window_code	              -- 统计时间窗          
      ,period_id	                      -- 会计期
      ,target_period	                  -- 目标时点 无 我来计算的
      ,bg_code	                          -- BG编码
      ,bg_name	                          -- BG名称
      ,oversea_code	                  -- 区域编码
      ,oversea_desc	                  -- 区域
      ,lv1_code	                      -- 重量级团队LV1编码
      ,lv1_name	                      -- 重量级团队LV1描述
      ,lv2_code	                      -- 重量级团队LV2编码
      ,lv2_name	                      -- 重量级团队LV2名称          
      ,currency	                      -- 币种
      ,equip_rev_cons_before_amt	      -- 设备收入额（对价前） 
      ,equip_cost_cons_before_amt	      -- 设备成本额（对价前） 
      ,equip_rev_cons_after_amt	      -- 设备收入额（对价后） 
      ,equip_cost_cons_after_amt	      -- 设备成本额（对价后）  
      ,mgp_ratio_after	                      -- 制毛率（对价后）
      ,mgp_ratio_before	                      -- 制毛率（对价前）
      ,mca_adjust_ratio                 -- MCA调整率
      ,mgp_adjust_ratio                 -- 制毛调整率
      ,equip_rev_cons_after_amt_ytd_data
      ,mgp_ratio_after_ytd_data
      ,mca_adjust_ratio_ytd_data
      ,mgp_adjust_ratio_ytd_data
      ,remark	                          -- 备注
      ,created_by	                      -- 创建人
      ,creation_date	                  -- 创建时间
      ,last_updated_by	                  -- 修改人
      ,last_update_date	              -- 修改时间
      ,del_flag	                      -- 是否删除
      )
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前）  
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前）  
      ,base.equip_rev_cons_after_amt	      -- 设备收入额（对价后）  
      ,base.equip_cost_cons_after_amt	      -- 设备成本额（对价后）
      ,base.mgp_ratio_after  -- 制毛率（对价后） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,base.mgp_ratio_before  -- 制毛率（对价前） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,base.mca_adjust_ratio -- MCA调整率  用新的累计的算：=（对价后收入-对价前收入）/对价后收入
      ,base.mgp_adjust_ratio --制毛调整率 用新的累计的算：=（对价后收入-对价后成本）/对价后收入-（对价前收入-对价前成本）/对价前收入
      ,max_ytd.equip_rev_cons_after_amt_ytd_data
      ,max_ytd.mgp_ratio_ytd_data
      ,max_ytd.mca_adjust_ratio_ytd_data
      ,max_ytd.mgp_adjust_ratio_ytd_data
      ,base.remark
      ,'-1'  as created_by
      ,now()  as creation_date
      ,'-1'  as  last_updated_by
      ,now()  as  last_update_date
      ,'N'  as  del_flag
      from   dimension_lv2_tgt_period_tmp  base 
      left join max_period_rate_ytd_data_tmp  max_ytd
      on   base.version_code = max_ytd.version_code
      and  base.scenarios = max_ytd.scenarios
      and  base.time_window_code = max_ytd.time_window_code
      and  base.bg_code = max_ytd.bg_code
      and  base.oversea_code = max_ytd.oversea_code
      and  base.lv1_code = max_ytd.lv1_code
      and  coalesce(base.lv2_code,'SNULLC') = coalesce(max_ytd.lv2_code,'SNULLC') 
      and  base.currency = max_ytd.currency  
      ;
			
      
  
  	-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码:'||v_version_code||',dm_fop_dimension_lv2_tgt_period_t目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
-- 

-- 
-- --处理异常信息
-- 	exception
-- 		when others then
-- 		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
-- 			p_log_sp_name => v_sp_name,    --sp名称
-- 			p_log_step_num  => null,
-- 			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
-- 			) ;
-- 	x_success_flag := '2001';
	
	
		
 end;
 $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100 ;