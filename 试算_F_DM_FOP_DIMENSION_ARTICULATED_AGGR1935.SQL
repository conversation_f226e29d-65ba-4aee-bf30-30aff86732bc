CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ARTICULATED_AGGR(OUT X_SUCCESS_FLAG TEXT)
 RETURNS TEXT
 LANGUAGE PLPGSQL
 NOT FENCED NOT SHIPPABLE
AS $$
 /*
创建时间：
创建人  ：TWX1139790
背景描述：将整合处理后的YTD数据表，处理成季度、半年度、年度步长数据
年度：取12月YTD预测结果
半年度：1、MM为1-6月时，取6月的YTD预测结果；2、MM为7-12月时，取12月的YTD预测结果-6月的YTD实际数
季度：1、MM为1-3月时，取3月的YTD预测结果；2、MM为4-6月时，取6月的YTD预测结果-3月的YTD实际数；
      3、MM为7-9月时，取9月的YTD预测结果-6月的YTD实际数；4、MM为10-12月时，取12月的YTD预测结果-9月的YTD实际数。
参数描述：参数一(P_VERSION_CODE)：版本编码202505
          参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ARTICULATED_AGGR();
*/
 
 DECLARE
    V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ARTICULATED_AGGR';
    V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
    V_STEP_NUM   BIGINT := 0; --步骤号
    V_CURR_PERIOD INT := CAST(TO_CHAR(CURRENT_TIMESTAMP,'MM') AS INT);
    V_YEAR_PERIOD INT := CAST(YEAR(CURRENT_TIMESTAMP)-1||'12' AS INT);   -- 固定取每年12月的预测YTD数据，作为年度步长
    V_Q_DATA_PERIOD INT;   -- 季度月取数月份
    V_Q_CUT_PERIOD INT;   -- 季度月需要扣减月份
    V_H_DATA_PERIOD INT;   -- 半年度取数月份 
    V_H_CUT_PERIOD INT;   -- 半年度需要扣减月份
    V_Q_TAG VARCHAR(10);   -- 季度月份标签
    V_H_TAG VARCHAR(10);   -- 半年度月份标签
BEGIN
    X_SUCCESS_FLAG := '1';                                 --1表示成功
    
  -- 取出不同步长，当前对应需要计算的月份，作为变量
  IF V_CURR_PERIOD BETWEEN 1 AND 3 THEN 
     V_Q_TAG := 'Q1';
     V_H_TAG := 'H1'; 
     V_Q_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'03' AS INT);
     V_Q_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'00' AS INT);   -- 1-3月，无需扣减月份
     V_H_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'06' AS INT);
     V_H_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'00' AS INT);   -- 1-6月，无需扣减月份
  ELSIF V_CURR_PERIOD BETWEEN 4 AND 6 THEN 
     V_Q_TAG := 'Q2';
     V_H_TAG := 'H1';
     V_Q_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'06' AS INT);
     V_Q_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'03' AS INT);   -- 4-6月，需扣减3月YTD的数
     V_H_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'06' AS INT);
     V_H_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'00' AS INT);   -- 1-6月，无需扣减月份
  ELSIF V_CURR_PERIOD BETWEEN 7 AND 9 THEN 
     V_Q_TAG := 'Q3';
     V_H_TAG := 'H2';
     V_Q_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'09' AS INT);
     V_Q_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'06' AS INT);   -- 7-9月，需扣减6月YTD的数
     V_H_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'12' AS INT);
     V_H_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'06' AS INT);   -- 7-12月，需扣减6月YTD的数
  ELSIF V_CURR_PERIOD BETWEEN 10 AND 12 THEN 
     V_Q_TAG := 'Q4';
     V_H_TAG := 'H2';
     V_Q_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'12' AS INT);
     V_Q_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'09' AS INT);   -- 10-12月，需扣减9月YTD的数
     V_H_DATA_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'12' AS INT);
     V_H_CUT_PERIOD := CAST(YEAR(CURRENT_TIMESTAMP)-1||'06' AS INT);   -- 7-12月，需扣减6月YTD的数
  END IF;

       -- 如果是传 VERSION_CODE 调函数取JAVA传入的 P_VERSION_CODE ，如果是自动调度的则取 当前年月 版本
/*        IF P_VERSION_CODE IS NOT NULL THEN 
        SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
        ELSE */
        SELECT TO_CHAR(CURRENT_DATE,'YYYYMM') AS VERSION_CODE INTO V_VERSION_CODE ;
--        END IF ;

  -- 删除结果表历史版本数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATED_AGGR_T WHERE VERSION_CODE = '||V_VERSION_CODE;   -- 量纲层级不同步长数据表
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOP.DM_FOP_LV2_ARTICULATED_AGGR_T WHERE VERSION_CODE = '||V_VERSION_CODE;   -- LV2层级不同步长数据表
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOP.DM_FOP_LV1_ARTICULATED_AGGR_T WHERE VERSION_CODE = '||V_VERSION_CODE;   -- LV1层级不同步长数据表
  
  -- 创建TGT上年12月YTD数据临时表
  DROP TABLE IF EXISTS FOP_TGT_LAST_12YTD_TMP;
  CREATE TEMPORARY TABLE FOP_TGT_LAST_12YTD_TMP (
    VERSION_CODE CHARACTER VARYING(100),
    PERIOD_ID NUMERIC,
    TARGET_PERIOD VARCHAR(50),
    SCENARIOS CHARACTER VARYING(50),
    BG_CODE CHARACTER VARYING(50),
    BG_NAME CHARACTER VARYING(200),
    OVERSEA_CODE CHARACTER VARYING(50),
    OVERSEA_DESC CHARACTER VARYING(50),
    LV1_CODE CHARACTER VARYING(50),
    LV1_NAME CHARACTER VARYING(600),
    LV2_CODE CHARACTER VARYING(50),
    LV2_NAME CHARACTER VARYING(600),
    DIMENSION_GROUP_CODE CHARACTER VARYING(50),
    DIMENSION_GROUP_CN_NAME CHARACTER VARYING(600),
    DIMENSION_GROUP_EN_NAME CHARACTER VARYING(600),
    DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(50),
    DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(600),
    DIMENSION_SUBCATEGORY_EN_NAME CHARACTER VARYING(600),
    CURRENCY_CODE CHARACTER VARYING(50),
    DATA_SOURCE CHARACTER VARYING(50),
    UNIT_PRICE NUMERIC(38,10),
    UNIT_COST NUMERIC(38,10),
    MGP_RATIO_BEFORE NUMERIC(38,10),
    MGP_RATIO_AFTER NUMERIC(38,10)
   )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);
  
  -- 创建不同步长数据临时表
  DROP TABLE IF EXISTS FOP_DIMENSION_STEP_TMP;
  CREATE TEMPORARY TABLE FOP_DIMENSION_STEP_TMP (
    VERSION_CODE CHARACTER VARYING(100),
    PERIOD_ID NUMERIC,
    TARGET_PERIOD VARCHAR(50),
    SCENARIOS CHARACTER VARYING(50),
    PHASE_DATE CHARACTER VARYING(100),
    BG_CODE CHARACTER VARYING(50),
    BG_NAME CHARACTER VARYING(200),
    OVERSEA_CODE CHARACTER VARYING(50),
    OVERSEA_DESC CHARACTER VARYING(50),
    LV1_CODE CHARACTER VARYING(50),
    LV1_NAME CHARACTER VARYING(600),
    LV2_CODE CHARACTER VARYING(50),
    LV2_NAME CHARACTER VARYING(600),
    DIMENSION_GROUP_CODE CHARACTER VARYING(50),
    DIMENSION_GROUP_CN_NAME CHARACTER VARYING(600),
    DIMENSION_GROUP_EN_NAME CHARACTER VARYING(600),
    DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(50),
    DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(600),
    DIMENSION_SUBCATEGORY_EN_NAME CHARACTER VARYING(600),
    CURRENCY_CODE CHARACTER VARYING(50),
    FCST_TYPE CHARACTER VARYING(100),
    DATA_SOURCE CHARACTER VARYING(50),
    EQUIP_REV_CONS_BEFORE_AMT NUMERIC(38,10),
    EQUIP_COST_CONS_BEFORE_AMT NUMERIC(38,10),
    EQUIP_REV_CONS_AFTER_AMT NUMERIC(38,10),
    EQUIP_COST_CONS_AFTER_AMT NUMERIC(38,10),
    CARRYOVER_QTY NUMERIC(38,10),
    SHIP_QTY NUMERIC(38,10)
   )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);
  
  -- 取得TGT表上年12月YTD的4个指标值，插入临时表
  INSERT INTO FOP_TGT_LAST_12YTD_TMP(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         DATA_SOURCE,
         UNIT_PRICE,
         UNIT_COST,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER
  )
  -- 取TGT表量纲层级单位成本、单位价格、制毛率（对价前）3个指标值
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY,
         'TAB_DIMENSION' AS DATA_SOURCE,
         UNIT_PRICE,
         UNIT_COST,
         MGP_RATIO AS MGP_RATIO_BEFORE,
         NULL AS MGP_RATIO_AFTER
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_TGT_PERIOD_T
      WHERE VERSION_CODE = V_VERSION_CODE
      AND SCENARIOS IN ('量纲子类','量纲分组')
      AND CURRENCY = 'CNY'
      AND TARGET_PERIOD = YEAR(CURRENT_TIMESTAMP)-2||'12YTD'   -- 取T-1年12月YTD数据
  UNION ALL 
  -- 取TGT表LV2层级制毛率（对价后）的指标值
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         NULL AS DIMENSION_GROUP_CODE,
         NULL AS DIMENSION_GROUP_CN_NAME,
         NULL AS DIMENSION_GROUP_EN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_CODE,
         NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY,
         'TAB_LV2' AS DATA_SOURCE,
         NULL AS UNIT_PRICE,
         NULL AS UNIT_COST,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_TGT_PERIOD_T
      WHERE VERSION_CODE = V_VERSION_CODE
      AND CURRENCY = 'CNY'
      AND TARGET_PERIOD = YEAR(CURRENT_TIMESTAMP)-2||'12YTD';   -- 取T-1年12月YTD数据
  DBMS_OUTPUT.PUT_LINE('取得TGT表上年12月YTD的4个指标值，插入临时表');
  
  -- 取YTD数据参与不同步长逻辑计算
  INSERT INTO FOP_DIMENSION_STEP_TMP(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         CARRYOVER_QTY,
         SHIP_QTY
  )
  -- 从预测YTD表取到需要取数的对应几个月的数据值
  WITH YTD_DATA_TMP AS(
  -- 量纲层级
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         'TAB_DIMENSION' AS DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         NULL AS EQUIP_REV_CONS_AFTER_AMT,
         NULL AS EQUIP_COST_CONS_AFTER_AMT,
         CARRYOVER_QTY,
         SHIP_QTY
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T   -- 预测聚合后量纲层级YTD表
      WHERE VERSION_CODE = V_VERSION_CODE
      AND PERIOD_ID IN (V_Q_DATA_PERIOD,V_H_DATA_PERIOD,V_YEAR_PERIOD)   -- 取需要处理的几个月数据（来源表只有预测YTD的数据）
  UNION ALL 
  -- LV2层级
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         NULL AS DIMENSION_GROUP_CODE,
         NULL AS DIMENSION_GROUP_CN_NAME,
         NULL AS DIMENSION_GROUP_EN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_CODE,
         NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         'TAB_LV2' AS DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         NULL AS CARRYOVER_QTY,
         NULL AS SHIP_QTY
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T   -- 预测聚合后LV2层级YTD表
      WHERE VERSION_CODE = V_VERSION_CODE
      AND PERIOD_ID IN (V_Q_DATA_PERIOD,V_H_DATA_PERIOD,V_YEAR_PERIOD)   -- 取需要处理的几个月数据（来源表只有预测YTD的数据）
  UNION ALL 
  -- LV1层级
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         NULL AS LV2_CODE,
         NULL AS LV2_NAME,
         NULL AS DIMENSION_GROUP_CODE,
         NULL AS DIMENSION_GROUP_CN_NAME,
         NULL AS DIMENSION_GROUP_EN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_CODE,
         NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         'TAB_LV1' AS DATA_SOURCE,
         NULL AS EQUIP_REV_CONS_BEFORE_AMT,
         NULL AS EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         NULL AS CARRYOVER_QTY,
         NULL AS SHIP_QTY
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T   -- 预测聚合后量纲层级YTD表
      WHERE VERSION_CODE = V_VERSION_CODE
      AND PERIOD_ID IN (V_Q_DATA_PERIOD,V_H_DATA_PERIOD,V_YEAR_PERIOD)   -- 取需要处理的几个月数据（来源表只有预测YTD的数据）
  ),
  -- 从预测YTD表取到需要扣减的对应几个月的数据值
  CUT_DATA_TMP AS(
  -- 量纲层级
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         'TAB_DIMENSION' AS DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         NULL AS EQUIP_REV_CONS_AFTER_AMT,
         NULL AS EQUIP_COST_CONS_AFTER_AMT,
         CARRYOVER_QTY,
         SHIP_QTY
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T   -- 预测聚合后量纲层级YTD表
      WHERE VERSION_CODE = V_VERSION_CODE
      AND PERIOD_ID IN (V_Q_CUT_PERIOD,V_H_CUT_PERIOD)   -- 取需要扣减处理的几个月数据（来源表只有预测YTD的数据）
  UNION ALL 
  -- LV2层级
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         NULL AS DIMENSION_GROUP_CODE,
         NULL AS DIMENSION_GROUP_CN_NAME,
         NULL AS DIMENSION_GROUP_EN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_CODE,
         NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         'TAB_LV2' AS DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         NULL AS CARRYOVER_QTY,
         NULL AS SHIP_QTY
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T   -- 预测聚合后LV2层级YTD表
      WHERE VERSION_CODE = V_VERSION_CODE
      AND PERIOD_ID IN (V_Q_CUT_PERIOD,V_H_CUT_PERIOD)   -- 取需要扣减处理的几个月数据（来源表只有预测YTD的数据）
  UNION ALL 
  -- LV1层级
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         NULL AS LV2_CODE,
         NULL AS LV2_NAME,
         NULL AS DIMENSION_GROUP_CODE,
         NULL AS DIMENSION_GROUP_CN_NAME,
         NULL AS DIMENSION_GROUP_EN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_CODE,
         NULL AS DIMENSION_SUBCATEGORY_CN_NAME,
         NULL AS DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         'TAB_LV1' AS DATA_SOURCE,
         NULL AS EQUIP_REV_CONS_BEFORE_AMT,
         NULL AS EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         NULL AS CARRYOVER_QTY,
         NULL AS SHIP_QTY
      FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T   -- 预测聚合后量纲层级YTD表
      WHERE VERSION_CODE = V_VERSION_CODE
      AND PERIOD_ID IN (V_Q_CUT_PERIOD,V_H_CUT_PERIOD)   -- 取需要扣减处理的几个月数据（来源表只有预测YTD的数据）
  )
  -- 年度步长的数据
  SELECT VERSION_CODE,
         PERIOD_ID,
         SUBSTR(TARGET_PERIOD,1,4) AS TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         CARRYOVER_QTY,
         SHIP_QTY
      FROM YTD_DATA_TMP 
	  WHERE SUBSTR(TARGET_PERIOD,1,6) = V_YEAR_PERIOD
  UNION ALL 
  -- 取半年度步长的数据
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         SUBSTR(T1.TARGET_PERIOD,1,4)||V_H_TAG AS TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.DATA_SOURCE,
         T1.EQUIP_REV_CONS_BEFORE_AMT - NVL(T2.EQUIP_REV_CONS_BEFORE_AMT,0) AS EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT - NVL(T2.EQUIP_COST_CONS_BEFORE_AMT,0) AS EQUIP_COST_CONS_BEFORE_AMT,
         T1.EQUIP_REV_CONS_AFTER_AMT - NVL(T2.EQUIP_REV_CONS_AFTER_AMT,0) AS EQUIP_REV_CONS_AFTER_AMT,
         T1.EQUIP_COST_CONS_AFTER_AMT - NVL(T2.EQUIP_COST_CONS_AFTER_AMT,0) AS EQUIP_COST_CONS_AFTER_AMT,
         T1.CARRYOVER_QTY - NVL(T2.CARRYOVER_QTY,0) AS CARRYOVER_QTY,
         T1.SHIP_QTY - NVL(T2.SHIP_QTY,0) AS SHIP_QTY
      FROM (SELECT * FROM YTD_DATA_TMP WHERE PERIOD_ID = V_H_DATA_PERIOD) T1
      LEFT JOIN (SELECT * FROM YTD_DATA_TMP WHERE PERIOD_ID = V_H_CUT_PERIOD) T2
      ON T1.SCENARIOS = T2.SCENARIOS 
      AND T1.PHASE_DATE = T2.PHASE_DATE
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND NVL(T1.LV2_CODE,'S3') = NVL(T2.LV2_CODE,'S3')
      AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
      AND T1.DATA_SOURCE = T2.DATA_SOURCE
      AND T1.FCST_TYPE = T2.FCST_TYPE
  UNION ALL 
  -- 取季度步长的数据
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         SUBSTR(T1.TARGET_PERIOD,1,4)||V_Q_TAG AS TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.DATA_SOURCE,
         T1.EQUIP_REV_CONS_BEFORE_AMT - NVL(T2.EQUIP_REV_CONS_BEFORE_AMT,0) AS EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT - NVL(T2.EQUIP_COST_CONS_BEFORE_AMT,0) AS EQUIP_COST_CONS_BEFORE_AMT,
         T1.EQUIP_REV_CONS_AFTER_AMT - NVL(T2.EQUIP_REV_CONS_AFTER_AMT,0) AS EQUIP_REV_CONS_AFTER_AMT,
         T1.EQUIP_COST_CONS_AFTER_AMT - NVL(T2.EQUIP_COST_CONS_AFTER_AMT,0) AS EQUIP_COST_CONS_AFTER_AMT,
         T1.CARRYOVER_QTY - NVL(T2.CARRYOVER_QTY,0) AS CARRYOVER_QTY,
         T1.SHIP_QTY - NVL(T2.SHIP_QTY,0) AS SHIP_QTY
      FROM (SELECT * FROM YTD_DATA_TMP WHERE PERIOD_ID = V_Q_DATA_PERIOD) T1
      LEFT JOIN (SELECT * FROM YTD_DATA_TMP WHERE PERIOD_ID = V_Q_CUT_PERIOD) T2
      ON T1.SCENARIOS = T2.SCENARIOS 
      AND T1.PHASE_DATE = T2.PHASE_DATE
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND NVL(T1.LV2_CODE,'S3') = NVL(T2.LV2_CODE,'S3')
      AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
      AND T1.DATA_SOURCE = T2.DATA_SOURCE
      AND T1.FCST_TYPE = T2.FCST_TYPE;
  
  DBMS_OUTPUT.PUT_LINE('得到对应不同步长的部分直取指标数据');

  
  -- 将量纲层级的年度、半年度、季度步长数据插入结果表
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         CARRYOVER_QTY,
         SHIP_QTY, 
         REV_PERCENT,
         UNIT_PRICE,
         UNIT_COST,
         MGP_RATIO,
         CARRYOVER_RATE,
--         UNIT_PRICE_FCST_UPPER,
--         UNIT_PRICE_FCST_LOWER,
--         UNIT_COST_FCST_UPPER,
--         UNIT_COST_FCST_LOWER,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH SUM_NODIM_REV_TMP AS(
  -- RAN下无量纲数据求和
  SELECT T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.OVERSEA_CODE,
         T1.LV1_CODE,
         'RAN' AS LV2_CODE,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         SUM( T1.EQUIP_REV_CONS_BEFORE_AMT) AS RAN_SUM_REV_AMT,   -- 分子：RAN下无量纲数据求和
         SUM(SUM( T1.EQUIP_REV_CONS_BEFORE_AMT)) OVER(PARTITION BY T1.TARGET_PERIOD,T1.SCENARIOS,T1.PHASE_DATE,T1.BG_CODE,T1.OVERSEA_CODE,T1.LV1_CODE,T1.CURRENCY_CODE,T1.FCST_TYPE) AS RAN_LV2_SUM_REV_AMT,   -- 分母：RAN下LV2数据求和
         SUM( T1.EQUIP_COST_CONS_BEFORE_AMT) AS RAN_SUM_COST_AMT,   -- 分子：RAN下无量纲数据求和
         SUM(SUM( T1.EQUIP_COST_CONS_BEFORE_AMT)) OVER(PARTITION BY T1.TARGET_PERIOD,T1.SCENARIOS,T1.PHASE_DATE,T1.BG_CODE,T1.OVERSEA_CODE,T1.LV1_CODE,T1.CURRENCY_CODE,T1.FCST_TYPE) AS RAN_LV2_SUM_COST_AMT   -- 分母：RAN下LV2数据求和
      FROM FOP_DIMENSION_STEP_TMP T1
      WHERE T1.LV2_CODE IN ( '153324','153326','133661','101212')   -- 无线下LV2为RAN：'5G&LTE FDD','5G&LTE TDD','GUC','SRAN'
      AND T1.SCENARIOS IN ('量纲分组','量纲子类')
      AND T1.DATA_SOURCE = 'TAB_DIMENSION'   -- 取量纲层级表数据
      GROUP BY T1.TARGET_PERIOD,
               T1.SCENARIOS,
               T1.PHASE_DATE,
               T1.BG_CODE,
               T1.OVERSEA_CODE,
               T1.LV1_CODE,
               T1.DIMENSION_GROUP_CODE,
               T1.DIMENSION_SUBCATEGORY_CODE,
               T1.CURRENCY_CODE,
               T1.FCST_TYPE
  ),
  SUM_DATA_TMP AS(
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.CARRYOVER_QTY,
         T1.SHIP_QTY, 
         CASE WHEN T1.LV2_CODE IN ('153324','153326','133661','101212') AND (T1.DIMENSION_GROUP_CODE ='NODIM' OR T1.DIMENSION_SUBCATEGORY_CODE = 'NOSUB')   -- RAN指标处理
              THEN DECODE(T2.RAN_LV2_SUM_REV_AMT,0,-999999,T2.RAN_SUM_REV_AMT/T2.RAN_LV2_SUM_REV_AMT)
              ELSE DECODE(T3.EQUIP_REV_CONS_BEFORE_AMT,0,-999999,T1.EQUIP_REV_CONS_BEFORE_AMT/T3.EQUIP_REV_CONS_BEFORE_AMT)
         END AS REV_PERCENT,
         DECODE(T1.CARRYOVER_QTY,0,-999999,T1.EQUIP_REV_CONS_BEFORE_AMT/T1.CARRYOVER_QTY) AS UNIT_PRICE,   -- 异常值处理
         DECODE(T1.CARRYOVER_QTY,0,-999999,T1.EQUIP_COST_CONS_BEFORE_AMT/T1.CARRYOVER_QTY) AS UNIT_COST,   -- 异常值处理
         CASE WHEN T1.LV2_CODE IN ('153324','153326','133661','101212') AND (T1.DIMENSION_GROUP_CODE ='NODIM' OR T1.DIMENSION_SUBCATEGORY_CODE = 'NOSUB')   -- RAN指标处理
              THEN DECODE(T2.RAN_LV2_SUM_COST_AMT,0,-999999,1-(T2.RAN_SUM_COST_AMT/T2.RAN_LV2_SUM_COST_AMT))
              ELSE DECODE(T1.EQUIP_REV_CONS_BEFORE_AMT,0,-999999,1-(T1.EQUIP_COST_CONS_BEFORE_AMT/T1.EQUIP_REV_CONS_BEFORE_AMT))
         END AS MGP_RATIO,   -- 异常值处理
         DECODE(T1.SHIP_QTY,0,-999999,T1.CARRYOVER_QTY/T1.SHIP_QTY) AS CARRYOVER_RATE
      FROM FOP_DIMENSION_STEP_TMP T1 
	  LEFT JOIN SUM_NODIM_REV_TMP T2 
      ON T1.TARGET_PERIOD = T2.TARGET_PERIOD
      AND T1.SCENARIOS = T2.SCENARIOS
      AND T1.PHASE_DATE = T2.PHASE_DATE
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND NVL(T1.LV2_CODE,'S3') = NVL(T2.LV2_CODE,'S3')
      AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
      AND T1.FCST_TYPE = T2.FCST_TYPE
	  LEFT JOIN (SELECT * FROM FOP_DIMENSION_STEP_TMP WHERE DATA_SOURCE = 'TAB_LV2') T3 
      ON T1.TARGET_PERIOD = T3.TARGET_PERIOD
      AND T1.SCENARIOS = T3.SCENARIOS
      AND T1.PHASE_DATE = T3.PHASE_DATE
      AND T1.BG_CODE = T3.BG_CODE
      AND T1.OVERSEA_CODE = T3.OVERSEA_CODE
      AND T1.LV1_CODE = T3.LV1_CODE
      AND T1.LV2_CODE = T3.LV2_CODE
      AND T1.CURRENCY_CODE = T3.CURRENCY_CODE
      AND T1.FCST_TYPE = T3.FCST_TYPE
	  WHERE T1.SCENARIOS IN ('量纲分组','量纲子类')
      AND T1.DATA_SOURCE = 'TAB_DIMENSION'   -- 取量纲层级表数据
     ) 
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.CARRYOVER_QTY,
         T1.SHIP_QTY, 
         T1.REV_PERCENT,
         CASE WHEN T1.UNIT_PRICE > 0   -- 均价为正数时，直取计算所得数值，否则取上年12月YTD均价数值
              THEN T1.UNIT_PRICE
              ELSE T2.UNIT_PRICE
         END AS UNIT_PRICE,
         CASE WHEN T1.UNIT_COST > 0   -- 均本为正数时，直取计算所得数值，否则取上年12月YTD均本数值
              THEN T1.UNIT_COST
              ELSE T2.UNIT_COST
         END AS UNIT_COST,
         -- 当计算所得的制毛率绝对值<1，直接取计算所得值；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值<1，则取去年12月YTD的制毛率；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值>1，则直接处理为0；
         CASE WHEN ABS(T1.MGP_RATIO) > 1 AND T1.MGP_RATIO <> -999999 AND ABS(T2.MGP_RATIO_BEFORE) < 1 
              THEN T2.MGP_RATIO_BEFORE
              WHEN ABS(T1.MGP_RATIO) > 1 AND T1.MGP_RATIO <> -999999 AND ABS(T2.MGP_RATIO_BEFORE) > 1 
              THEN 0
              ELSE T1.MGP_RATIO
         END AS MGP_RATIO,
         T1.CARRYOVER_RATE,
--         UNIT_PRICE_FCST_UPPER,
--         UNIT_PRICE_FCST_LOWER,
--         UNIT_COST_FCST_UPPER,
--         UNIT_COST_FCST_LOWER,
         NULL AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
       FROM SUM_DATA_TMP T1 
       LEFT JOIN (SELECT * FROM FOP_TGT_LAST_12YTD_TMP WHERE DATA_SOURCE = 'TAB_DIMENSION') T2 
       ON T1.SCENARIOS = T2.SCENARIOS
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
       AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
       AND T1.CURRENCY_CODE = T2.CURRENCY_CODE;
   
  DBMS_OUTPUT.PUT_LINE('将量纲层级的年度、半年度、季度步长数据插入结果表');
  
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATED_AGGR_T';
  
  -------------------------------------------------------------------------LV2层级不同步长数据逻辑计算--------------------------------------------------------------------------
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_LV2_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER,
         MCA_ADJUST_RATIO,
         MGP_ADJUST_RATE,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH SUM_LV2_DATA_TMP AS (
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         DECODE(EQUIP_REV_CONS_BEFORE_AMT,0,-999999,1-(EQUIP_COST_CONS_BEFORE_AMT/EQUIP_REV_CONS_BEFORE_AMT)) AS MGP_RATIO_BEFORE,
         DECODE(EQUIP_REV_CONS_AFTER_AMT,0,-999999,1-(EQUIP_COST_CONS_AFTER_AMT/EQUIP_REV_CONS_AFTER_AMT)) AS MGP_RATIO_AFTER,
         DECODE(EQUIP_REV_CONS_BEFORE_AMT,0,-999999,1-(EQUIP_REV_CONS_AFTER_AMT/EQUIP_REV_CONS_BEFORE_AMT)) AS MCA_ADJUST_RATIO
      FROM FOP_DIMENSION_STEP_TMP
	  WHERE DATA_SOURCE = 'TAB_LV2'
  ),
  MGP_DATA_TMP AS (
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.OVERSEA_CODE,
         T1.LV1_CODE,
         T1.LV2_CODE,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         -- 当计算所得的制毛率绝对值<1，直接取计算所得值；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值<1，则取去年12月YTD的制毛率；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值>1，则直接处理为0；
         CASE WHEN ABS(T1.MGP_RATIO_BEFORE) > 1 AND T1.MGP_RATIO_BEFORE <> -999999 AND ABS(T2.MGP_RATIO_BEFORE) < 1 
              THEN T2.MGP_RATIO_BEFORE
              WHEN ABS(T1.MGP_RATIO_BEFORE) > 1 AND T1.MGP_RATIO_BEFORE <> -999999 AND ABS(T2.MGP_RATIO_BEFORE) > 1 
              THEN 0
              ELSE T1.MGP_RATIO_BEFORE
         END AS MGP_RATIO_BEFORE,
         CASE WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) < 1 
              THEN T2.MGP_RATIO_AFTER
              WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) > 1 
              THEN 0
              ELSE T1.MGP_RATIO_AFTER
         END AS MGP_RATIO_AFTER
      FROM SUM_LV2_DATA_TMP T1 
	  LEFT JOIN (SELECT * FROM FOP_TGT_LAST_12YTD_TMP WHERE DATA_SOURCE = 'TAB_LV2') T2 
       ON T1.SCENARIOS = T2.SCENARIOS
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
  )
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.EQUIP_REV_CONS_AFTER_AMT,
         T1.EQUIP_COST_CONS_AFTER_AMT,
         T2.MGP_RATIO_BEFORE,
         T2.MGP_RATIO_AFTER,
         T1.MCA_ADJUST_RATIO,
         T2.MGP_RATIO_AFTER - T2.MGP_RATIO_BEFORE AS MGP_ADJUST_RATE,    -- 制毛调整率需要进行异常数据处理，待跟建华姐确认
         NULL AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
       FROM SUM_LV2_DATA_TMP T1
	   LEFT JOIN MGP_DATA_TMP T2
	   ON T1.SCENARIOS = T2.SCENARIOS
       AND T1.PHASE_DATE = T2.PHASE_DATE
       AND T1.BG_CODE = T2.BG_CODE
	   AND T1.TARGET_PERIOD = T2.TARGET_PERIOD
       AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.LV2_CODE = T2.LV2_CODE
       AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
       AND T1.FCST_TYPE = T2.FCST_TYPE;

  DBMS_OUTPUT.PUT_LINE('将LV2层级的年度、半年度、季度步长数据插入结果表');
  
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_LV2_ARTICULATED_AGGR_T';

  ---------------------------------------------------------------------------LV1层级年度、半年度、季度不同步长计算逻辑------------------------------------------------------------
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_LV1_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         MGP_RATIO_AFTER,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH SUM_LV1_DATA_TMP AS (
  SELECT VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         DECODE(EQUIP_REV_CONS_AFTER_AMT,0,-999999,1-(EQUIP_COST_CONS_AFTER_AMT/EQUIP_REV_CONS_AFTER_AMT)) AS MGP_RATIO_AFTER
     FROM FOP_DIMENSION_STEP_TMP
	  WHERE DATA_SOURCE = 'TAB_LV1'
  )
  SELECT T1.VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.EQUIP_REV_CONS_AFTER_AMT,
         T1.EQUIP_COST_CONS_AFTER_AMT,
         -- 当计算所得的制毛率绝对值<1，直接取计算所得值；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值<1，则取去年12月YTD的制毛率；当计算所得的制毛率绝对值>1，且不为异常值-999999，并且上年12月YTD制毛率绝对值>1，则直接处理为0；
         CASE WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) < 1 
              THEN T2.MGP_RATIO_AFTER
              WHEN ABS(T1.MGP_RATIO_AFTER) > 1 AND T1.MGP_RATIO_AFTER <> -999999 AND ABS(T2.MGP_RATIO_AFTER) > 1 
              THEN 0
              ELSE T1.MGP_RATIO_AFTER
         END AS MGP_RATIO_AFTER,
         NULL AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM SUM_LV1_DATA_TMP T1
      LEFT JOIN (SELECT * FROM FOP_TGT_LAST_12YTD_TMP WHERE DATA_SOURCE = 'TAB_LV1') T2 
       ON T1.SCENARIOS = T2.SCENARIOS
       AND T1.BG_CODE = T2.BG_CODE
       AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
       AND T1.LV1_CODE = T2.LV1_CODE
       AND T1.CURRENCY_CODE = T2.CURRENCY_CODE;
 
  DBMS_OUTPUT.PUT_LINE('将LV1层级的年度、半年度、季度步长数据插入结果表');

  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_LV1_ARTICULATED_AGGR_T';
  
  RETURN 'SUCCESS';
  
END$$
/