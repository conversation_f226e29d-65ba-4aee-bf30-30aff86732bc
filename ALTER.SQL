ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T ADD COLUMN EQUIP_REV_CONS_BEFORE_AMT_UPPER NUMERIC;
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T ADD COLUMN EQUIP_REV_CONS_BEFORE_AMT_LOWER NUMERIC;
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T ADD COLUMN EQUIP_COST_CONS_BEFORE_AMT_UPPER NUMERIC;
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T ADD COLUMN EQUIP_COST_CONS_BEFORE_AMT_LOWER NUMERIC;
COMMENT ON COLUMN DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T.EQUIP_REV_CONS_BEFORE_AMT_UPPER IS '设备收入上界';
COMMENT ON COLUMN DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T.EQUIP_REV_CONS_BEFORE_AMT_LOWER IS '设备收入下界';
COMMENT ON COLUMN DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T.EQUIP_COST_CONS_BEFORE_AMT_UPPER IS '设备成本上界';
COMMENT ON COLUMN DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T.EQUIP_COST_CONS_BEFORE_AMT_LOWER IS '设备成本下界';

ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T ADD COLUMN SOP_TYPE VARCHAR(50);
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T ADD COLUMN SOP_TYPE VARCHAR(50);
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T ADD COLUMN SOP_TYPE VARCHAR(50);
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATED_AGGR_T ADD COLUMN SOP_TYPE VARCHAR(50);
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_LV2_ARTICULATED_AGGR_T ADD COLUMN SOP_TYPE VARCHAR(50);
ALTER TABLE FIN_DM_OPT_FOP.DM_FOP_LV1_ARTICULATED_AGGR_T ADD COLUMN SOP_TYPE VARCHAR(50);
COMMENT ON COLUMN DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T.SOP_TYPE IS '预测类型';
COMMENT ON COLUMN DM_FOP_YTD_LV2_ARTICULATED_AGGR_T.SOP_TYPE IS '预测类型';
COMMENT ON COLUMN DM_FOP_YTD_LV1_ARTICULATED_AGGR_T.SOP_TYPE IS '预测类型';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATED_AGGR_T.SOP_TYPE IS '预测类型';
COMMENT ON COLUMN DM_FOP_LV2_ARTICULATED_AGGR_T.SOP_TYPE IS '预测类型';
COMMENT ON COLUMN DM_FOP_LV1_ARTICULATED_AGGR_T.SOP_TYPE IS '预测类型';

-- 创建序列
 CREATE SEQUENCE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_MID_S
 START  WITH  1
 INCREMENT  BY  1000
 NO MINVALUE  
 MAXVALUE 9223372036854775807
 CACHE 1;


DROP TABLE IF EXISTS FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_MID_T;
SET SEARCH_PATH = FIN_DM_OPT_FOP;
CREATE  TABLE DM_FOP_DIMENSION_ARTICULATION_MID_T (
    ROW_ID BIGINT DEFAULT NEXTVAL('DM_FOP_DIMENSION_ARTICULATION_MID_S'::REGCLASS) NOT NULL,
    VERSION_CODE CHARACTER VARYING(100),
    MON_CODE VARCHAR(50),
    SCENARIOS CHARACTER VARYING(50),
    PHASE_DATE CHARACTER VARYING(100),
    BG_CODE CHARACTER VARYING(50),
    OVERSEA_CODE CHARACTER VARYING(50),
    LV1_CODE CHARACTER VARYING(50),
    LV2_CODE CHARACTER VARYING(50),
    DIMENSION_GROUP_CODE CHARACTER VARYING(50),
    DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(50),
    CURRENCY_CODE CHARACTER VARYING(50),
    FCST_TYPE CHARACTER VARYING(100),
    DATA_SOURCE CHARACTER VARYING(100),
    EQUIP_REV_CONS_BEFORE_AMT NUMERIC,
    EQUIP_REV_CONS_AFTER_AMT NUMERIC,
    CARRYOVER_QTY NUMERIC,
    SHIP_QTY NUMERIC,
    MGP_RATIO_BEFORE NUMERIC,
    MGP_RATIO_AFTER NUMERIC,
    CREATED_BY BIGINT,
    CREATION_DATE TIMESTAMP WITHOUT TIME ZONE,
    LAST_UPDATED_BY BIGINT,
    LAST_UPDATE_DATE TIMESTAMP WITHOUT TIME ZONE,
    DEL_FLAG CHARACTER VARYING(10)
)
WITH (ORIENTATION=ROW, COMPRESSION=NO)
DISTRIBUTE BY HASH(ROW_ID)
TO GROUP GROUP_VERSION1;
ALTER TABLE DM_FOP_DIMENSION_ARTICULATION_MID_T ADD CONSTRAINT DM_FOP_DIMENSION_MID_ARTICULATION_T_PK PRIMARY KEY (ROW_ID);
COMMENT ON TABLE DM_FOP_DIMENSION_ARTICULATION_MID_T IS '勾稽后ETL数据处理中间表';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.VERSION_CODE IS '版本编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.MON_CODE IS '预测时点';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.SCENARIOS IS '场景';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.PHASE_DATE IS 'SOP期次';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.BG_CODE IS 'BG编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.OVERSEA_CODE IS '区域编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.LV1_CODE IS '重量级团队LV1编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.LV2_CODE IS '重量级团队LV2编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.DIMENSION_GROUP_CODE IS '量纲分组编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.DIMENSION_SUBCATEGORY_CODE IS '量纲子类编码';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.CURRENCY_CODE IS '币种';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.FCST_TYPE IS '预测方法';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.DATA_SOURCE IS '数据来源（TAB_DIMENSION：量纲层、TAB_LV2：LV2层级、TAB_LV1：LV1层级';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.EQUIP_REV_CONS_BEFORE_AMT IS '设备收入额（对价前）';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.EQUIP_REV_CONS_AFTER_AMT IS '设备收入额（对价后）';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.CARRYOVER_QTY IS '结转量';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.SHIP_QTY IS '发货量';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.MGP_RATIO_BEFORE IS '制毛率（对价前)';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.MGP_RATIO_AFTER IS '制毛率（对价后)';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.CREATED_BY IS '创建人';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.CREATION_DATE IS '创建时间';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.LAST_UPDATED_BY IS '修改人';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.LAST_UPDATE_DATE IS '修改时间';
COMMENT ON COLUMN DM_FOP_DIMENSION_ARTICULATION_MID_T.DEL_FLAG IS '是否删除';

