﻿-- 预测后提供给指标推演
-- dm_fop_dimension_fcst_t
-- dm_fop_dimension_lv2_fcst_t


-- dm_fop_dimension_fcst_t  量纲层级数据预测返回表
drop table if exists fin_dm_opt_fop.dm_fop_dimension_fcst_t;
create table fin_dm_opt_fop.dm_fop_dimension_fcst_t(
period_id	 	                          numeric,
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
target_period	 	                          varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
dimension_group_code   	             	varchar(100),
dimension_group_cn_name        	      varchar(100),
dimension_group_en_name	         			varchar(100),
dimension_subcategory_code    	      varchar(100),
dimension_subcategory_cn_name 	      varchar(100),
dimension_subcategory_en_name 	      varchar(100),
currency	 														varchar(50),
fcst_type	 														varchar(100),
rev_percent_fcst                      numeric,
equip_rev_before_fcst                 numeric,
unit_price_fcst_conf                  numeric(38,10),
unit_price_fcst                       numeric(38,10),
unit_price_fcst_upper                 numeric(38,10),
unit_price_fcst_lower                 numeric(38,10),
unit_cost_fcst_conf                   numeric(38,10),
unit_cost_fcst                        numeric(38,10),
unit_cost_fcst_upper                  numeric(38,10),
unit_cost_fcst_lower                  numeric(38,10),
mgp_rate_before_fcst_conf             numeric(38,10),
mgp_rate_before_fcst                  numeric(38,10),
mgp_rate_before_fcst_upper            numeric(38,10),
mgp_rate_before_fcst_lower            numeric(38,10),
unit_price_fcst_acc                   numeric,
unit_cost_fcst_acc                    numeric,
carryover_ratio_fcst                  numeric(38,10), -- 新增20250606
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(period_id);
 comment on table fin_dm_opt_fop.dm_fop_dimension_fcst_t                               is '量纲层级数据预测返回表';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.time_window_code                    is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.target_period                    is '预测时点 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.oversea_desc                    is '区域';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.dimension_group_code            is '量纲分组编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.dimension_group_cn_name         is '量纲分组中文名';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.dimension_group_en_name         is '量纲分组英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.dimension_subcategory_code      is '量纲子类编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.dimension_subcategory_cn_name   is '量纲子类中文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.dimension_subcategory_en_name   is '量纲子类英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.fcst_type is '预测方法';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.rev_percent_fcst                        is '收入占比预测';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.equip_rev_before_fcst                        is '对价前设备收入额';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_price_fcst_conf                        is '单位价格预测_置信度';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_price_fcst                        is '单位价格预测_预测值';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_price_fcst_upper                         is '单位价格预测_上标';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_price_fcst_lower                         is '单位价格预测_下标';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_cost_fcst_conf                       is '单位成本预测_置信度';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_cost_fcst                       is '单位成本预测_预测值';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_cost_fcst_upper          						 is '单位成本预测_上标';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_cost_fcst_lower          						 is '单位成本预测_下标';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.mgp_rate_before_fcst_conf        						 is '制毛率预测（对价前)_置信度';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.mgp_rate_before_fcst        						 is '制毛率预测（对价前)_预测值';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.mgp_rate_before_fcst_upper        					 is '制毛率预测（对价前)_上标';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.mgp_rate_before_fcst_lower        					 is '制毛率预测（对价前)_下标';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_price_fcst_acc          						 is '单价预测准确率';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.unit_cost_fcst_acc          						 is '单本预测准确率';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.carryover_ratio_fcst        					 is '结转率预测';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.del_flag                        is '是否删除';

-- dm_fop_dimension_lv2_fcst_t  量纲-lv1LV2层级数据预测返回表
drop table if exists fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t;
create table fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t(
period_id	 	                          numeric,
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
--phase_date	 				                      varchar(60),
target_period	 				                      varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
currency	 														varchar(50),
fcst_type	 														varchar(100),
equip_rev_after_fcst_conf                 numeric,
equip_rev_after_fcst                 numeric,
equip_rev_after_fcst_upper                 numeric,
equip_rev_after_fcst_lower                 numeric,
mgp_rate_after_fcst_conf             numeric(38,10),
mgp_rate_after_fcst                  numeric(38,10),
mgp_rate_after_fcst_upper            numeric(38,10),
mgp_rate_after_fcst_lower            numeric(38,10),
-- unit_price_timeseries_fcst_conf                  numeric(38,10),
-- unit_price_timeseries_fcst                       numeric(38,10),
-- unit_price_timeseries_fcst_upper                 numeric(38,10),
-- unit_price_timeseries_fcst_lower                 numeric(38,10),
-- unit_cost_timeseries_fcst_conf                   numeric(38,10),
-- unit_cost_timeseries_fcst                        numeric(38,10),
-- unit_cost_timeseries_fcst_upper                  numeric(38,10),
-- unit_cost_timeseries_fcst_lower                  numeric(38,10),
mca_adjust_ratio_fcst                  numeric(38,10),
mgp_adjust_ratio_fcst                  numeric(38,10),
-- carryover_ratio_fcst                  numeric(38,10),
-- unit_price_fcst_acc                   numeric,
-- unit_cost_fcst_acc                    numeric,
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(period_id);
 comment on table fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t                               is '量纲-lv1LV2层级数据预测返回表';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.time_window_code                    is '统计时间窗 ';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.phase_date                         is 'SOP期次';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.target_period                         is '预测时点';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_fcst_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.oversea_desc                    is '区域';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.fcst_type is '预测方法';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.equip_rev_after_fcst_conf                        is '对价后设备收入预测_置信度';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.equip_rev_after_fcst                        is '对价后设备收入预测_预测值';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.equip_rev_after_fcst_upper                        is '对价后设备收入预测_上标';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.equip_rev_after_fcst_lower                        is '对价后设备收入预测_下标';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_price_timeseries_fcst_conf                        is '单位价格预测_时序_置信度';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_price_timeseries_fcst                        is '单位价格预测_时序_预测值';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_price_timeseries_fcst_upper                         is '单位价格预测_时序_上标';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_price_timeseries_fcst_lower                         is '单位价格预测_时序_下标';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_cost_timeseries_fcst_conf                       is '单位成本预测_时序_置信度';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_cost_timeseries_fcst                       is '单位成本预测_时序_预测值';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_cost_timeseries_fcst_upper          						 is '单位成本预测_时序_上标';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_cost_timeseries_fcst_lower          						 is '单位成本预测_时序_下标';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.mgp_rate_after_fcst_conf        						 is '制毛率预测（对价后)_置信度';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.mgp_rate_after_fcst        						 is '制毛率预测（对价后)_预测值';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.mgp_rate_after_fcst_upper        					 is '制毛率预测（对价后)_上标';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.mgp_rate_after_fcst_lower        					 is '制毛率预测（对价后)_下标';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.mca_adjust_ratio_fcst        					 is 'MCA调整率预测';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.mgp_adjust_ratio_fcst        					 is '制毛调整率预测';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.carryover_ratio_fcst        					 is '结转率预测';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_price_fcst_acc          						 is '单价预测准确率';
-- comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.unit_cost_fcst_acc          						 is '单本预测准确率';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_fcst_t.del_flag                        is '是否删除';