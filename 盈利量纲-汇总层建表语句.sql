drop table if exists fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t;
create table fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t(
 version_code                            varchar(100),
 period_id                               numeric,
 time_window_code                        varchar(50),
 phase_date                              varchar(60 ),
 bg_code                                 varchar(50 ),
 bg_name                                 varchar(200),
 oversea_code                            varchar(50 ),
 oversea_desc                            varchar(50 ),
 lv1_prod_rnd_team_code                  varchar(50 ),
 lv1_prod_rd_team_cn_name                varchar(600),
 lv1_prod_rd_team_en_name                varchar(600),
 lv2_prod_rnd_team_code                  varchar(50 ),
 lv2_prod_rd_team_cn_name                varchar(600),
 lv2_prod_rd_team_en_name                varchar(600),
 scenarios                               varchar(50),
 dimension_group_code                    varchar(50 ),
 dimension_group_cn_name                 varchar(600),
 dimension_group_en_name                 varchar(600),
 dimension_subcategory_code              varchar(50 ),
 dimension_subcategory_cn_name           varchar(600),
 dimension_subcategory_en_name           varchar(600),
 snop_quantity                           numeric(38,10),
 source_table                            varchar(100),
 remark                                  varchar(500),
 created_by                              int8,
 creation_date                           timestamp,
 last_updated_by                         int8,
 last_update_date                        timestamp,
 del_flag                                varchar(10)
) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
distribute by hash(version_code,period_id,phase_date);
comment on table fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t                                         is '盈利量纲SOP计划量汇总表';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.version_code                            is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.period_id                               is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.time_window_code                        is '统计时间窗';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.phase_date                              is '预测期次';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.bg_code                                 is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.bg_name                                 is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.oversea_code                            is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.oversea_desc                            is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.lv1_prod_rnd_team_code                  is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.lv1_prod_rd_team_cn_name                is '重量级团队LV1中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.lv1_prod_rd_team_en_name                is '重量级团队LV1英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.lv2_prod_rnd_team_code                  is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.lv2_prod_rd_team_cn_name                is '重量级团队LV2中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.lv2_prod_rd_team_en_name                is '重量级团队LV2英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.scenarios                               is '场景';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.dimension_group_code                    is '量纲分组编码            ';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.dimension_group_cn_name                 is '量纲分组中文名                ';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.dimension_group_en_name                 is '量纲分组英文名称        ';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.dimension_subcategory_code              is '量纲子类编码               ';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.dimension_subcategory_cn_name           is '量纲子类中文名称           ';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.dimension_subcategory_en_name           is '量纲子类英文名称           ';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.snop_quantity                           is 'SNOP月计划量';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.source_table                            is '来源表';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.remark                                  is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.created_by                              is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.creation_date                           is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.last_updated_by                         is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.last_update_date                        is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t.del_flag                                is '是否删除';



drop table if exists fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t;
create table fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t(
 version_code                            varchar(100),
 period_id                               numeric,
 time_window_code                        varchar(50),
 bg_code                                 varchar(50),
 bg_name                                 varchar(200),
 oversea_code                            varchar(50 ),
 oversea_desc                            varchar(50 ),
 lv1_prod_rnd_team_code                  varchar(50),
 lv1_prod_rd_team_cn_name                varchar(600),
 lv1_prod_rd_team_en_name                varchar(600),
 lv2_prod_rnd_team_code                  varchar(50),
 lv2_prod_rd_team_cn_name                varchar(600),
 lv2_prod_rd_team_en_name                varchar(600),
 scenarios                               varchar(50),
 dimension_group_code                    varchar(50),
 dimension_group_cn_name                 varchar(600),
 dimension_group_en_name                 varchar(600),
 dimension_subcategory_code              varchar(50),
 dimension_subcategory_cn_name           varchar(600),
 dimension_subcategory_en_name           varchar(600),
 carryover_rate                          numeric(38,10),
 ship_qty                                numeric(38,10),
 rev_qty                                 numeric(38,10),
 source_table                            varchar(100),
 remark                                  varchar(500),
 created_by                              int8,
 creation_date                           timestamp,
 last_updated_by                         int8,
 last_update_date                        timestamp,
 del_flag                                varchar(10)
) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
distribute by hash(version_code,period_id);
comment on table fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t                                         is '盈利量纲结转率汇总表';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.version_code                            is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.period_id                               is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.time_window_code                        is '统计时间窗';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.bg_code                                 is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.bg_name                                 is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.oversea_code                            is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.oversea_desc                            is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.lv1_prod_rnd_team_code                  is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.lv1_prod_rd_team_cn_name                is '重量级团队LV1中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.lv1_prod_rd_team_en_name                is '重量级团队LV1英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.lv2_prod_rnd_team_code                  is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.lv2_prod_rd_team_cn_name                is '重量级团队LV2中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.lv2_prod_rd_team_en_name                is '重量级团队LV2英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.scenarios                               is '场景';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.dimension_group_code                    is '量纲分组编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.dimension_group_cn_name                 is '量纲分组中文名';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.dimension_group_en_name                 is '量纲分组英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.dimension_subcategory_code              is '量纲子类编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.dimension_subcategory_cn_name           is '量纲子类中文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.dimension_subcategory_en_name           is '量纲子类英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.carryover_rate                          is '结转率';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.ship_qty                                is '发货量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.rev_qty                                 is '收入量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.source_table                            is '来源表';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.remark                                  is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.created_by                              is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.creation_date                           is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.last_updated_by                         is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.last_update_date                        is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t.del_flag                                is '是否删除';


drop table if exists fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t;
create table fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t(
 version_code                            varchar(100),
 period_id                               numeric,
 time_window_code	                     varchar(50),
 bg_code                                 varchar(50),
 bg_name                                 varchar(200),
 oversea_code                            varchar(50 ),
 oversea_desc                            varchar(50 ),
 lv1_prod_rnd_team_code                  varchar(50),
 lv1_prod_rd_team_cn_name                varchar(600),
 lv1_prod_rd_team_en_name                varchar(600),
 lv2_prod_rnd_team_code                  varchar(50),
 lv2_prod_rd_team_cn_name                varchar(600),
 lv2_prod_rd_team_en_name                varchar(600),
 scenarios                               varchar(50),
 dimension_group_code                    varchar(50),
 dimension_group_cn_name                 varchar(600),
 dimension_group_en_name                 varchar(600),
 dimension_subcategory_code              varchar(50),
 dimension_subcategory_cn_name           varchar(600),
 dimension_subcategory_en_name           varchar(600),
 cost_unit_price                         numeric(38,10),
 rev_unit_price                          numeric(38,10),
 currency_code                           varchar(50),
 source_table                            varchar(100),
 remark                                  varchar(500),
 created_by                              int8,
 creation_date                           timestamp,
 last_updated_by                         int8,
 last_update_date                        timestamp,
 del_flag                                varchar(10)
) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
distribute by hash(version_code,period_id);
comment on table fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t                                          is '盈利量纲均本均价汇总表';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.version_code                            is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.period_id                               is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.time_window_code                        is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.bg_code                                 is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.bg_name                                 is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.oversea_code                            is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.oversea_desc                            is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.lv1_prod_rnd_team_code                  is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.lv1_prod_rd_team_cn_name                is '重量级团队LV1中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.lv1_prod_rd_team_en_name                is '重量级团队LV1英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.lv2_prod_rnd_team_code                  is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.lv2_prod_rd_team_cn_name                is '重量级团队LV2中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.lv2_prod_rd_team_en_name                is '重量级团队LV2英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.scenarios                               is '场景';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.dimension_group_code                    is '量纲分组编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.dimension_group_cn_name                 is '量纲分组中文名';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.dimension_group_en_name                 is '量纲分组英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.dimension_subcategory_code              is '量纲子类编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.dimension_subcategory_cn_name           is '量纲子类中文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.dimension_subcategory_en_name           is '量纲子类英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.cost_unit_price                         is '成本单位平均价';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.rev_unit_price                          is '收入单位平均价';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.currency_code                           is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.source_table                            is '来源表';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.remark                                  is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.created_by                              is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.creation_date                           is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.last_updated_by                         is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.last_update_date                        is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t.del_flag                                is '是否删除';

drop table if exists fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t;
create table fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t(
 version_code                            varchar(100),
 period_id                               numeric,
 time_window_code	                     varchar(50),
 bg_code                                 varchar(50 ),
 bg_name                                 varchar(200),
 oversea_code                            varchar(50 ),
 oversea_desc                            varchar(50 ),
 lv1_prod_rnd_team_code                  varchar(50 ),
 lv1_prod_rd_team_cn_name                varchar(600),
 lv1_prod_rd_team_en_name                varchar(600),
 lv2_prod_rnd_team_code                  varchar(50 ),
 lv2_prod_rd_team_cn_name                varchar(600),
 lv2_prod_rd_team_en_name                varchar(600),
 scenarios                               varchar(50),
 dimension_group_code                    varchar(50 ),
 dimension_group_cn_name                 varchar(600),
 dimension_group_en_name                 varchar(600),
 dimension_subcategory_code              varchar(50 ),
 dimension_subcategory_cn_name           varchar(600),
 dimension_subcategory_en_name           varchar(600),
 equip_rev_cons_before_amt               numeric(38,10),
 equip_cost_cons_before_amt              numeric(38,10),
 currency_code                           varchar(50),
 source_table                            varchar(100),
 remark                                  varchar(500),
 created_by                              int8,
 creation_date                           timestamp,
 last_updated_by                         int8,
 last_update_date                        timestamp,
 del_flag                                varchar(10)
) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
distribute by hash(version_code,period_id);
comment on table fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t                                          is '盈利量对价前纲价格成本汇总表';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.version_code                            is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.period_id                               is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.time_window_code                        is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.bg_code                                 is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.bg_name                                 is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.oversea_code                            is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.oversea_desc                            is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.lv1_prod_rnd_team_code                  is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.lv1_prod_rd_team_cn_name                is '重量级团队LV1中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.lv1_prod_rd_team_en_name                is '重量级团队LV1英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.lv2_prod_rnd_team_code                  is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.lv2_prod_rd_team_cn_name                is '重量级团队LV2中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.lv2_prod_rd_team_en_name                is '重量级团队LV2英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.scenarios                               is '场景';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.dimension_group_code                    is '量纲分组编码            ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.dimension_group_cn_name                 is '量纲分组中文名                ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.dimension_group_en_name                 is '量纲分组英文名称        ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.dimension_subcategory_code              is '量纲子类编码               ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.dimension_subcategory_cn_name           is '量纲子类中文名称           ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.dimension_subcategory_en_name           is '量纲子类英文名称           ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.equip_rev_cons_before_amt               is '设备收入额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.equip_cost_cons_before_amt              is '设备成本额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.currency_code                           is '币种                                            ';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.source_table                            is '来源表';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.remark                                  is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.created_by                              is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.creation_date                           is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.last_updated_by                         is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.last_update_date                        is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t.del_flag                                is '是否删除';

drop table if exists fin_dm_opt_fop.dm_fop_dimension_qty_sum_t;
create table fin_dm_opt_fop.dm_fop_dimension_qty_sum_t(
 version_code                            varchar(100),
 period_id                               numeric,
 time_window_code	                     varchar(50),
 bg_code                                 varchar(50 ),
 bg_name                                 varchar(200),
 oversea_code                            varchar(50 ),
 oversea_desc                            varchar(50 ),
 lv1_prod_rnd_team_code                  varchar(50 ),
 lv1_prod_rd_team_cn_name                varchar(600),
 lv1_prod_rd_team_en_name                varchar(600),
 lv2_prod_rnd_team_code                  varchar(50 ),
 lv2_prod_rd_team_cn_name                varchar(600),
 lv2_prod_rd_team_en_name                varchar(600),
 scenarios                               varchar(50),
 dimension_group_code                    varchar(50 ),
 dimension_group_cn_name                 varchar(600),
 dimension_group_en_name                 varchar(600),
 dimension_subcategory_code              varchar(50 ),
 dimension_subcategory_cn_name           varchar(600),
 dimension_subcategory_en_name           varchar(600),
 carryover_qty                           varchar(50),
 ship_qty                                numeric(38,10),
 rev_qty                                 numeric(38,10),
 source_table                            varchar(100),
 remark                                  varchar(500),
 created_by                              int8,
 creation_date                           timestamp,
 last_updated_by                         int8,
 last_update_date                        timestamp,
 del_flag                                varchar(10)
) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
distribute by hash(version_code,period_id);
comment on table fin_dm_opt_fop.dm_fop_dimension_qty_sum_t                                          is '盈利量发货量收入量结转量汇总表';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.version_code                            is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.period_id                               is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.time_window_code                        is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.bg_code                                 is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.bg_name                                 is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.oversea_code                            is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.oversea_desc                            is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.lv1_prod_rnd_team_code                  is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.lv1_prod_rd_team_cn_name                is '重量级团队LV1中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.lv1_prod_rd_team_en_name                is '重量级团队LV1英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.lv2_prod_rnd_team_code                  is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.lv2_prod_rd_team_cn_name                is '重量级团队LV2中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.lv2_prod_rd_team_en_name                is '重量级团队LV2英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.scenarios                               is '场景';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.dimension_group_code                    is '量纲分组编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.dimension_group_cn_name                 is '量纲分组中文名';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.dimension_group_en_name                 is '量纲分组英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.dimension_subcategory_code              is '量纲子类编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.dimension_subcategory_cn_name           is '量纲子类中文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.dimension_subcategory_en_name           is '量纲子类英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.carryover_qty                           is '结转量';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.ship_qty                                is '发货量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.rev_qty                                 is '收入量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.source_table                            is '来源表';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.remark                                  is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.created_by                              is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.creation_date                           is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.last_updated_by                         is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.last_update_date                        is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_qty_sum_t.del_flag                                is '是否删除';


drop table if exists fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t;
create table fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t(
 version_code                    varchar(100),
 period_id                       numeric,
 time_window_code	             varchar(50),
 currency_code                   varchar(50),
 bg_code                         varchar(50 ),
 bg_name                         varchar(200),
 bg_en_name                      varchar(200),
 lv1_prod_rnd_team_code          varchar(50 ),
 lv1_prod_rd_team_cn_name        varchar(600),
 lv1_prod_rd_team_en_name        varchar(600),
 lv2_prod_rnd_team_code          varchar(50 ),
 lv2_prod_rd_team_cn_name        varchar(600),
 lv2_prod_rd_team_en_name        varchar(600),
 oversea_code                    varchar(50 ),
 oversea_desc                    varchar(50 ),
 equip_rev_cons_after_amt        numeric(38,10),
 equip_cost_cons_after_amt       numeric(38,10),
 remark                          varchar(500),
 created_by                      int8,
 creation_date                   timestamp,
 last_updated_by                 int8,
 last_update_date                timestamp,
 del_flag                        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(version_code,period_id);
comment on table fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t                                 is '盈利量纲-ICT损益汇总表';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.version_code                    is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.time_window_code                is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.currency_code                   is '币种                                            ';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.bg_name                         is 'BG中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.bg_en_name                      is 'BG英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.lv1_prod_rnd_team_code          is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.lv1_prod_rd_team_cn_name        is '重量级团队LV1中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.lv1_prod_rd_team_en_name        is '重量级团队LV1英文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.lv2_prod_rnd_team_code          is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.lv2_prod_rd_team_cn_name        is '重量级团队LV2中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.lv2_prod_rd_team_en_name        is '重量级团队LV2中文描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.oversea_desc                    is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.equip_rev_cons_after_amt        is '设备收入额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.equip_cost_cons_after_amt       is '设备成本额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t.del_flag                        is '是否删除';





