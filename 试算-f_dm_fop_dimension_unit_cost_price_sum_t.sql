CREATE OR REPLACE FUNCTION fin_dm_opt_fop.f_dm_fop_dimension_unit_cost_price_sum_t(p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS pg_catalog.text AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：盈利量纲均本均价汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_unit_cost_price_sum_t();
*/
 
 declare
	v_sp_name varchar(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_unit_cost_price_sum_t('''||p_version_code||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t';
	v_version_code varchar(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   bigint; --步骤号
	v_dml_row_count  number default 0 ;


begin
	x_success_flag := '1';                                 --1表示成功
	
	       -- 如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select to_char(current_date,'yyyymm') as version_code into v_version_code ;	
        end if 
        ;	
	
	 --1.开始日志
  v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '盈利量纲均本均价汇总表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的最大版本编码:'||v_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  

	
		drop table if exists unit_product_tmp;
	     create temporary table  unit_product_tmp
		          as
		select distinct 
              lv1_prod_rnd_team_code
              ,lv1_prod_rd_team_cn_name
              ,lv1_prod_rd_team_en_name
              ,lv2_prod_rnd_team_code
              ,lv2_prod_rd_team_cn_name
              ,lv2_prod_rd_team_en_name
		from  dmdim.dm_dim_product_d
		where lv1_prod_rnd_team_code in ('100001'     --无线
                                          ,'134557'     --光
                                          ,'101775'     --数据存储
                                          ,'137565'     --数据通信
                                          ,'133277'     --计算
                                          ,'100011')    --云核心网
										  ;
										  
		
		drop table if exists unit_cost_tmp;
	    create temporary table  unit_cost_tmp
		          as 
		select t1.attr_group_code  
              ,t1.time_window_code		
              ,t1.stat_period_id                 
              ,t1.start_period_id                
              ,t1.end_period_id                  
              ,case when attr_group_code = '004' or  attr_group_code = '006'   or  attr_group_code = '008'  or  attr_group_code = '010' 
			        then 'RICT001' 
					else t1.lv0_prod_list_code
                     end as bg_code                  
              ,case when attr_group_code = '004' or  attr_group_code = '006'   or  attr_group_code = '008'  or  attr_group_code = '010' 
			        then 'ICT' 
					else t1.lv0_prod_list_cn_name     
					end as bg_name  
       		  ,t2.lv1_prod_rnd_team_code
              ,t2.lv1_prod_rd_team_cn_name
              ,t2.lv1_prod_rd_team_en_name	  
              ,t1.lv2_prod_rnd_team_code         
              ,t1.lv2_prod_rd_team_en_name       
              ,t1.lv2_prod_rd_team_cn_name 
              ,case when attr_group_code = '011' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			         then t1.dimension_group_code_l2  
                     when attr_group_code in ( '004','005','006','007')	
                     then null				
      			     else t1.product_dimension_group_code	
                     end as dimension_group_code    /*量纲分组*/                                      
               ,case when attr_group_code = '011' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			         then t1.dimension_group_l2_cn_name   
                     when attr_group_code in ( '004','005','006','007')	
                     then null						 
      			     else t1.product_dimension_group         
					 end as dimension_group_cn_name  /*量纲分组中文名*/                                         
               ,case when attr_group_code = '011' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			         then t1.dimension_group_l2_en_name   
                     when attr_group_code in ( '004','005','006','007')	
                     then null						 
      			     else t1.product_dimension_group_en_name 
					 end as dimension_group_en_name   /*量纲分组英文名*/ 
              ,case when attr_group_code in ( '004','005','006','007') 
			        then t1.dimension_subcategory_code
                    else null
                    end as	dimension_subcategory_code	/*量纲子类*/			
              ,case when attr_group_code in ( '004','005','006','007') 
			        then t1.dimension_subcategory_cn_name 
                    else null
                    end as	dimension_subcategory_cn_name	/*量纲子类中文名*/ 			
              ,case when attr_group_code in ( '004','005','006','007') 
			        then t1.dimension_subcategory_en_name 
                    else null	
                    end as	dimension_subcategory_en_name	/*量纲子类英文名*/ 			
              ,case when attr_group_code = '004' or  attr_group_code = '005'   or  attr_group_code = '008'  or  attr_group_code = '009' 
			        then 'GH0001' 
					else t1.domestic_or_oversea_code    
					end as oversea_code            /*区域*/ 	
              ,case when attr_group_code = '004' or  attr_group_code = '005'   or  attr_group_code = '008'  or  attr_group_code = '009' 
			        then '全球' 
					else t1.domestic_or_oversea_cname   
					end as oversea_cname           /*区域中文名*/ 	
              ,case when attr_group_code = '004' or  attr_group_code = '005'   or  attr_group_code = '008'  or  attr_group_code = '009' 
			        then 'GH0001' 
					else t1.domestic_or_oversea_ename   
					end as oversea_ename          /*区域英文名*/ 	                        
              ,t1.currency_code    
              ,t1.bs_value                       
              ,t1.bs_numerator_value             
              ,t1.bs_denominator_value           
          from  fin_dm_opt_fop.dwk_bs_ps_cost_unit_price_dcp_i t1
		  join  unit_product_tmp t2 
		  on t1.lv2_prod_rnd_team_code = t2.lv2_prod_rnd_team_code
          where 1=1
		  and t1.bs_code = 'BS_NPS_00000097'		  
          and t1.attr_group_code in ( '004','005','006','007','008','009','010','011')	
		  and time_window_code = 'YTD'	
		  ;
		  
		  		  
		drop table if exists unit_price_tmp;
	    create temporary table  unit_price_tmp
		          as 
		select                        
          t1.attr_group_code  
         ,t1.time_window_code               
         ,t1.stat_period_id                 
         ,t1.start_period_id                
         ,t1.end_period_id      		 
         ,case when attr_group_code = '006' or  attr_group_code = '008'   or  attr_group_code = '014'  or  attr_group_code = '016' 
			   then 'RICT001' 
			   else t1.lv0_prod_list_code        
			   end as bg_code       
         ,case when attr_group_code = '006' or  attr_group_code = '008'   or  attr_group_code = '014'  or  attr_group_code = '016' 
			   then 'ICT' 
			   else t1.lv0_prod_list_cn_name     
			   end as bg_name           
         ,t1.lv1_prod_rnd_team_code         
         ,t1.lv1_prod_rd_team_en_name       
         ,t1.lv1_prod_rd_team_cn_name       
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name  
         ,case when attr_group_code = '017' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			   then t1.dimension_group_code_l2  
               when attr_group_code in ( '006','007','008','009')	
               then null    			   
      		   else t1.product_dimension_group_code
               end as dimension_group_code    /*量纲分组*/                                      
         ,case when attr_group_code = '017' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			   then t1.dimension_group_l2_cn_name   
               when attr_group_code in ( '006','007','008','009')	
               then null 			   
      		   else t1.product_dimension_group         
			    end as dimension_group_cn_name    /*量纲中文名*/                                   
         ,case when attr_group_code = '017' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			   then t1.dimension_group_l2_en_name 
               when attr_group_code in ( '006','007','008','009')	
               then null 			   
      		   else t1.product_dimension_group_en_name 
			   end as dimension_group_en_name 	  /*量纲英文名*/   
         ,case when attr_group_code in ( '006','007','008','009') 
			   then t1.dimension_subcategory_code  
               else null
               end as	dimension_subcategory_code	  /*量纲子类*/	   
         ,case when attr_group_code in ( '006','007','008','009') 
			   then t1.dimension_subcategory_cn_name  
               else null
               end as dimension_subcategory_cn_name  /*量纲子类中文名*/
         ,case when attr_group_code in ( '006','007','008','009') 
			   then t1.dimension_subcategory_en_name  
               else null
               end as dimension_subcategory_en_name  /*量纲子类英文名*/
         ,case when attr_group_code = '006' or  attr_group_code = '007'   or  attr_group_code = '014'  or  attr_group_code = '015' 
			   then 'GH0001' 
			   else t1.domestic_or_oversea_code        
			   end as oversea_code    /*区域*/
         ,case when attr_group_code = '006' or  attr_group_code = '007'   or  attr_group_code = '014'  or  attr_group_code = '015' 
			   then '全球' 
			   else t1.domestic_or_oversea_cname       
			   end as oversea_cname   /*区域中文名*/
         ,case when attr_group_code = '006' or  attr_group_code = '007'   or  attr_group_code = '014'  or  attr_group_code = '015' 
			   then 'GH0001' 
			   else t1.domestic_or_oversea_ename       
			   end as oversea_ename        /*区域英文名*/            
         ,t1.currency_code                                                     
         ,t1.bs_value                       
         ,t1.bs_denominator_value           
         ,t1.bs_numerator_value             		  
		from  fin_dm_opt_fop.dwk_bs_ps_rev_unit_price_i t1
		where 1=1
		and bs_code = 'BS_NPS_00000085'
		and attr_group_code in ( '006','007','008','009','014','015','016','017')		
		and time_window_code = 'YTD'	
		;
  
         drop table if exists scenarios_tmp;
	    create temporary table  scenarios_tmp
		          as 
        select '量纲分组'  as scenarios
	     ,t1.time_window_code                                       
         ,t1.end_period_id    as period_id     		 
         ,t1.bg_code       
         ,t1.bg_name 
		 ,t1.oversea_code
         ,t1.oversea_cname as oversea_desc	 
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 
         ,t1.lv1_prod_rd_team_en_name 		 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name 
         ,t1.dimension_group_code                                          
         ,t1.dimension_group_cn_name                                       
         ,t1.dimension_group_en_name 	   
         ,t1.dimension_subcategory_code     
         ,t1.dimension_subcategory_cn_name  
         ,t1.dimension_subcategory_en_name                     
         ,case when t1.currency_code = 'RMB' 
		       then 'CNY' 
			   else t1.currency_code 
          end as currency_code			   
         ,t1.bs_value as cost_unit_price
         ,t2.bs_value as rev_unit_price
        from unit_cost_tmp t1	 
        left join unit_price_tmp t2    
		on  t1.time_window_code = t2.time_window_code                          
        and t1.end_period_id    = t2.end_period_id      	 
        and coalesce(t1.bg_code                   ,'SNULL')    = coalesce(t2.bg_code                   ,'SNULL')
        and coalesce(t1.oversea_code              ,'SNULL')    = coalesce(t2.oversea_code	           ,'SNULL')
        and coalesce(t1.lv1_prod_rnd_team_code    ,'SNULL')    = coalesce(t2.lv1_prod_rnd_team_code    ,'SNULL')           
        and coalesce(t1.lv2_prod_rnd_team_code    ,'SNULL')    = coalesce(t2.lv2_prod_rnd_team_code    ,'SNULL')
        and coalesce(t1.dimension_group_code      ,'SNULL')    = coalesce(t2.dimension_group_code      ,'SNULL')                              
        and coalesce(t1.dimension_subcategory_code,'SNULL')    = coalesce(t2.dimension_subcategory_code,'SNULL')                
        and coalesce(t1.currency_code             ,'SNULL')    = coalesce(t2.currency_code             ,'SNULL')
		where t1.lv1_prod_rnd_team_code in ('134557','133277','137565')	   /*光、计算、数据通信*/
		union all
		 select '量纲子类'  as scenarios
	     ,t1.time_window_code                                       
         ,t1.end_period_id    as period_id     		 
         ,t1.bg_code       
         ,t1.bg_name 
		 ,t1.oversea_code
         ,t1.oversea_cname as oversea_desc	 
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 
         ,t1.lv1_prod_rd_team_en_name 		 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name 	 
         ,t1.dimension_group_code                                          
         ,t1.dimension_group_cn_name                                       
         ,t1.dimension_group_en_name 	   
         ,t1.dimension_subcategory_code     
         ,t1.dimension_subcategory_cn_name  
         ,t1.dimension_subcategory_en_name                     
         ,case when t1.currency_code = 'RMB' 
		       then 'CNY' 
			   else t1.currency_code 
          end as currency_code			   
         ,t1.bs_value as cost_unit_price
         ,t2.bs_value as rev_unit_price
        from unit_cost_tmp t1	 
        left join unit_price_tmp t2    
		on  t1.time_window_code = t2.time_window_code                          
        and t1.end_period_id    = t2.end_period_id      	 
        and coalesce(t1.bg_code                   ,'SNULL')    = coalesce(t2.bg_code                   ,'SNULL')
        and coalesce(t1.oversea_code              ,'SNULL')    = coalesce(t2.oversea_code	           ,'SNULL')
        and coalesce(t1.lv1_prod_rnd_team_code    ,'SNULL')    = coalesce(t2.lv1_prod_rnd_team_code    ,'SNULL')           
        and coalesce(t1.lv2_prod_rnd_team_code    ,'SNULL')    = coalesce(t2.lv2_prod_rnd_team_code    ,'SNULL')
        and coalesce(t1.dimension_group_code      ,'SNULL')    = coalesce(t2.dimension_group_code      ,'SNULL')                              
        and coalesce(t1.dimension_subcategory_code,'SNULL')    = coalesce(t2.dimension_subcategory_code,'SNULL')                
        and coalesce(t1.currency_code             ,'SNULL')    = coalesce(t2.currency_code             ,'SNULL')
		where t1.lv1_prod_rnd_team_code in ('100001','101775')	   /*无线、数据存储*/
		union all
		 select 'LV2'  as scenarios
	     ,t1.time_window_code                                       
         ,t1.end_period_id    as period_id     		 
         ,t1.bg_code       
         ,t1.bg_name 
		 ,t1.oversea_code
         ,t1.oversea_cname as oversea_desc	 
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 
         ,t1.lv1_prod_rd_team_en_name 		 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name 	 
         ,t1.dimension_group_code                                          
         ,t1.dimension_group_cn_name                                       
         ,t1.dimension_group_en_name 	   
         ,t1.dimension_subcategory_code     
         ,t1.dimension_subcategory_cn_name  
         ,t1.dimension_subcategory_en_name                     
         ,case when t1.currency_code = 'RMB' 
		       then 'CNY' 
			   else t1.currency_code 
          end as currency_code			   
         ,t1.bs_value as cost_unit_price
         ,t2.bs_value as rev_unit_price
        from unit_cost_tmp t1	 
        left join unit_price_tmp t2    
		on  t1.time_window_code = t2.time_window_code                          
        and t1.end_period_id    = t2.end_period_id      	 
        and coalesce(t1.bg_code                   ,'SNULL')    = coalesce(t2.bg_code                   ,'SNULL')
        and coalesce(t1.oversea_code              ,'SNULL')    = coalesce(t2.oversea_code	           ,'SNULL')
        and coalesce(t1.lv1_prod_rnd_team_code    ,'SNULL')    = coalesce(t2.lv1_prod_rnd_team_code    ,'SNULL')           
        and coalesce(t1.lv2_prod_rnd_team_code    ,'SNULL')    = coalesce(t2.lv2_prod_rnd_team_code    ,'SNULL')
        and coalesce(t1.dimension_group_code      ,'SNULL')    = coalesce(t2.dimension_group_code      ,'SNULL')                              
        and coalesce(t1.dimension_subcategory_code,'SNULL')    = coalesce(t2.dimension_subcategory_code,'SNULL')                
        and coalesce(t1.currency_code             ,'SNULL')    = coalesce(t2.currency_code             ,'SNULL')
		where t1.lv1_prod_rnd_team_code in ('100011')	   /*云核心网*/
		;
		
		
		-- 删除最大版本数据
      delete fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t where version_code = v_version_code;
	  
	  insert into fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t(	
	   version_code     
      ,time_window_code
	  ,period_id
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv1_prod_rd_team_en_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
      ,lv2_prod_rd_team_en_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name
      ,currency_code	  
      ,cost_unit_price
      ,rev_unit_price                       
      ,source_table
      ,remark
      ,created_by
      ,creation_date
      ,last_updated_by
      ,last_update_date
      ,del_flag
	  )
	  select v_version_code   as version_code
	     ,t1.time_window_code                                       
         ,t1.period_id     		 
         ,t1.bg_code       
         ,t1.bg_name 
		 ,t1.oversea_code
         ,case when t1.oversea_code	= 'GH0002'  
		       then '国内' 
			   else t1.oversea_desc
			   end as oversea_desc
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 
         ,t1.lv1_prod_rd_team_en_name 		 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name 
         ,t1.scenarios		 
         ,t1.dimension_group_code                                          
         ,t1.dimension_group_cn_name                                       
         ,t1.dimension_group_en_name 	   
         ,t1.dimension_subcategory_code     
         ,t1.dimension_subcategory_cn_name  
         ,t1.dimension_subcategory_en_name                     
         ,case when t1.currency_code = 'RMB' 
		       then 'CNY' 
			   else t1.currency_code 
          end as currency_code			   
         ,t1.cost_unit_price
         ,t1.rev_unit_price
		 ,'dwk_bs_ps_cost_unit_price_dcp_i&dwk_bs_ps_rev_unit_price_i' as source_table
         ,'' as remark
 	     , -1 as created_by
 	     , current_timestamp as creation_date
 	     , -1 as last_updated_by
 	     , current_timestamp as last_update_date
 	     , 'N' as del_flag	
        from scenarios_tmp t1	
        where oversea_code not in ('GH0004')	-- 只取 中国区 和 海外的数据，剔除其他的数据		
        
		;
		
		
		-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码:'||v_version_code||',dm_fop_dimension_unit_cost_price_sum_t 目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
			) ;
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_dimension_unit_cost_price_sum_t;
	
		
 end;
 $BODY$
 LANGUAGE plpgsql VOLATILE
  COST 100

