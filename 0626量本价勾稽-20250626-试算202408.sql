-- 创建视图
CREATE OR REPLACE VIEW dm_fop_dimension_lv2_fcst_t_v AS SELECT scenarios as sce_code,target_period as mon_code,fcst_type as met_code,bg_code,oversea_code as geo_code,lv2_code as div_code,equip_rev_after_fcst,mgp_rate_after_fcst,mca_adjust_ratio_fcst,mgp_adjust_ratio_fcst,period_id FROM dm_fop_dimension_lv2_fcst_t
WHERE currency='CNY' and del_flag='N' and period_id=''202408'';

CREATE OR REPLACE VIEW dm_fop_dimension_fcst_t_1_v AS SELECT scenarios as sce_code,target_period as mon_code,fcst_type as met_code,bg_code,oversea_code as geo_code,lv2_code || '_' || dimension_subcategory_code as div_code,dimension_subcategory_code,unit_cost_fcst,unit_price_fcst,rev_percent_fcst,period_id,lv2_code,mgp_rate_before_fcst FROM dm_fop_dimension_fcst_t
WHERE lv1_name in ('无线','数据存储') and currency='CNY' and del_flag='N' AND period_id=''202408'';


CREATE OR REPLACE VIEW dm_fop_dimension_fcst_t_2_v AS SELECT scenarios as sce_code,target_period as mon_code,fcst_type as met_code,bg_code,oversea_code as geo_code,lv2_code || '_' || dimension_group_code as div_code,dimension_group_code,unit_cost_fcst,unit_price_fcst,rev_percent_fcst,period_id,lv2_code,mgp_rate_before_fcst FROM dm_fop_dimension_fcst_t
WHERE lv1_name in ('数据通信','计算','光') and currency='CNY' and del_flag='N' AND period_id=''202408'';


CREATE OR REPLACE VIEW dm_fop_dimension_fcst_sop_1_v AS SELECT fcst.scenarios as sce_code,fcst.target_period as mon_code,fcst.fcst_type as met_code,fcst.bg_code,fcst.oversea_code as geo_code,fcst.lv2_code || '_' || fcst.dimension_subcategory_code as div_code,fcst.dimension_subcategory_code,snop_quantity*carryover_ratio_fcst amount,phase_date,version_code,DECODE(instr(phase_date, '-'), 0,'FCST','BUDGET') sop_type,fcst.lv2_code,snop_quantity,fcst.period_id FROM (SELECT * FROM dm_fop_dimension_sop_plan_sum_t WHERE version_code=(select max(version_code) from dm_fop_dimension_sop_plan_sum_t where version_code like ''202408''||'%') AND del_flag='N') sop,
(SELECT * FROM dm_fop_dimension_fcst_t WHERE period_id=''202408'' AND fcst_type='YTD法' AND lv1_name in ('无线','数据存储') and currency='CNY' and del_flag='N') fcst
WHERE sop.period_id=fcst.target_period AND sop.scenarios=fcst.scenarios AND sop.bg_code=fcst.bg_code AND sop.oversea_code=fcst.oversea_code AND sop.lv1_prod_rnd_team_code=fcst.lv1_code AND sop.lv2_prod_rnd_team_code=fcst.lv2_code AND sop.dimension_subcategory_code=fcst.dimension_subcategory_code;

CREATE OR REPLACE VIEW dm_fop_dimension_fcst_sop_2_v AS SELECT fcst.scenarios as sce_code,fcst.target_period as mon_code,fcst.fcst_type as met_code,fcst.bg_code,fcst.oversea_code as geo_code,fcst.lv2_code || '_' || fcst.dimension_group_code as div_code,fcst.dimension_group_code,snop_quantity*carryover_ratio_fcst amount,phase_date,version_code,DECODE(instr(phase_date, '-'), 0,'FCST','BUDGET') sop_type,fcst.lv2_code,snop_quantity,fcst.period_id FROM (SELECT * FROM dm_fop_dimension_sop_plan_sum_t WHERE version_code=(select max(version_code) from dm_fop_dimension_sop_plan_sum_t where version_code like ''202408''||'%') AND del_flag='N') sop,
(SELECT * FROM dm_fop_dimension_fcst_t WHERE period_id=''202408'' AND fcst_type='YTD法' AND lv1_name in ('数据通信','计算','光') and currency='CNY' and del_flag='N') fcst
WHERE sop.period_id=fcst.target_period AND sop.scenarios=fcst.scenarios AND sop.bg_code=fcst.bg_code AND sop.oversea_code=fcst.oversea_code AND sop.lv1_prod_rnd_team_code=fcst.lv1_code AND sop.lv2_prod_rnd_team_code=fcst.lv2_code AND sop.dimension_group_code=fcst.dimension_group_code;


-- 期次转化
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_sop_phase_convert_t_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  DELETE FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'';
  
  INSERT INTO dm_fop_dimension_sop_phase_convert_t(period_id,phase_date,sop_code,sop_type)
  SELECT ''202408'',phase_date,'预测期次' || ROW_NUMBER() OVER (ORDER BY phase_date),'FCST' FROM (
  SELECT DISTINCT phase_date FROM dm_fop_dimension_sop_plan_sum_t WHERE instr(phase_date, '-') = 0 and version_code=(select max(version_code) from dm_fop_dimension_sop_plan_sum_t where version_code like ''202408''||'%') AND del_flag='N');
  
  INSERT INTO dm_fop_dimension_sop_phase_convert_t(period_id,phase_date,sop_code,sop_type)
  SELECT ''202408'',phase_date,'预算期次' || ROW_NUMBER() OVER (ORDER BY phase_date),'BUDGET' FROM (
  SELECT DISTINCT phase_date FROM dm_fop_dimension_sop_plan_sum_t WHERE instr(phase_date, '-') != 0 and version_code=(select max(version_code) from dm_fop_dimension_sop_plan_sum_t where version_code like ''202408''||'%') AND del_flag='N');
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;


CREATE OR REPLACE FUNCTION f_dm_fop_dimension_lv2_fcst_t_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$

DECLARE
  cnt  INT := 0;
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,equip_rev_after_fcst,'损益口径设备收入' from dm_fop_dimension_lv2_fcst_t_v WHERE equip_rev_after_fcst IS NOT NULL;
  f_check_lic_code('损益口径设备收入');
  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,mgp_rate_after_fcst,'损益口径制毛率' from dm_fop_dimension_lv2_fcst_t_v WHERE mgp_rate_after_fcst IS NOT NULL;
  f_check_lic_code('损益口径制毛率');

  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,mca_adjust_ratio_fcst,'对价等转化系数' from dm_fop_dimension_lv2_fcst_t_v WHERE mca_adjust_ratio_fcst IS NOT NULL;
  f_check_lic_code('对价等转化系数');
 
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,mgp_adjust_ratio_fcst,'量价到损益制毛调整率' from dm_fop_dimension_lv2_fcst_t_v WHERE mgp_adjust_ratio_fcst IS NOT NULL;
  f_check_lic_code('量价到损益制毛调整率');
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;


-- 量纲子类
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_t_1_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_cost_fcst,'均本（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_1_v WHERE sce_code!='LV2' and unit_cost_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;
  f_check_lic_code('均本（分摊后）');

  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_price_fcst,'均价（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_1_v WHERE sce_code!='LV2' and unit_price_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;
  f_check_lic_code('均价（分摊后）');
  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,rev_percent_fcst,'占比' from (SELECT * from dm_fop_dimension_fcst_t_1_v WHERE sce_code!='LV2' and dimension_subcategory_code='NOSUB' AND rev_percent_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;
  f_check_lic_code('占比');
  
  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,mgp_rate_before_fcst,'量价制毛率' from (SELECT * from dm_fop_dimension_fcst_t_1_v WHERE sce_code!='LV2' and dimension_subcategory_code='NOSUB' AND mgp_rate_before_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;
  f_check_lic_code('量价制毛率');
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;


-- 量纲分组
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_t_2_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_cost_fcst,'均本（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_2_v WHERE sce_code!='LV2' AND unit_cost_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;
  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_price_fcst,'均价（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_2_v WHERE sce_code!='LV2' AND unit_price_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;

  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,rev_percent_fcst,'占比' from (SELECT * from dm_fop_dimension_fcst_t_2_v WHERE sce_code!='LV2' and dimension_group_code='NODIM' AND rev_percent_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;
  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,mgp_rate_before_fcst,'量价制毛率' from (SELECT * from dm_fop_dimension_fcst_t_2_v WHERE sce_code!='LV2' and dimension_group_code='NODIM' AND mgp_rate_before_fcst IS NOT NULL) fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc;
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;

-- 量纲子类
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_sop_1_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),amount,'结转量' from dm_fop_dimension_fcst_sop_1_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date AND amount IS NOT NULL;
  f_check_lic_code('结转量');
  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),snop_quantity,'SOP计划量' from dm_fop_dimension_fcst_sop_1_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date AND snop_quantity IS NOT NULL;
  f_check_lic_code('SOP计划量');
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;

-- 量纲分组
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_sop_2_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),amount,'结转量' from dm_fop_dimension_fcst_sop_2_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date AND amount IS NOT NULL;
  
  INSERT INTO dm_fop_dimension_articulation_t(workspace_id,ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT 1932406953427709952,period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),snop_quantity,'SOP计划量' from dm_fop_dimension_fcst_sop_2_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE period_id=''202408'') spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date AND snop_quantity IS NOT NULL;
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;


-- 检查数据
CREATE OR REPLACE FUNCTION f_check_lic_code(lic_code VARCHAR)
  RETURNS void
  LANGUAGE plpgsql
AS $BODY$

DECLARE
  cnt  INT := 0;
  V_SQL  TEXT;
  
BEGIN
  V_SQL := 'SELECT count(*) FROM dm_fop_dimension_articulation_t WHERE lic_code = ''' || lic_code || ''' and ver_code=''202408'');';
  EXECUTE V_SQL INTO cnt;
  
  IF cnt = 0 THEN
    RAISE EXCEPTION '%,数据不存在', lic_code;
  END IF;
END; $BODY$;


-- 执行顺序
-- 量本价勾稽推指标数据前，删除当前月的数据（可能指标中已有计算结果）
DELETE FROM dm_fop_dimension_articulation_t WHERE ver_code=''202408'';

SELECT f_dm_fop_dimension_sop_phase_convert_t_v();
SELECT f_dm_fop_dimension_lv2_fcst_t_v();
SELECT f_dm_fop_dimension_fcst_t_1_v();
SELECT f_dm_fop_dimension_fcst_t_2_v();
SELECT f_dm_fop_dimension_fcst_sop_1_v();
SELECT f_dm_fop_dimension_fcst_sop_2_v();


