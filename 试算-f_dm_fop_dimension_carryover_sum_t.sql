CREATE OR REPLACE FUNCTION fin_dm_opt_fop.f_dm_fop_dimension_carryover_sum_t(p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS pg_catalog.text AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：盈利量纲结转率汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_carryover_sum_t();
*/
 
 declare
	v_sp_name varchar(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_carryover_sum_t('''||p_version_code||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t';
	v_version_code varchar(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   bigint; --步骤号
	v_dml_row_count  number default 0 ;


begin
	x_success_flag := '1';                                 --1表示成功
	
	       -- 如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select to_char(current_date,'yyyymm') as version_code into v_version_code ;	
        end if 
        ;	
	
	 --1.开始日志
  v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '盈利量纲结转率汇总表'||v_tbl_name||',目标表中'||to_char(current_date,'yyyymm')||'日期对应的最大版本编码:'||v_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  

				  		  
		drop table if exists carryover_tmp;
	    create temporary table  carryover_tmp
		          as 
		select                        
          t1.attr_group_code  
         ,t1.time_window_code               
         ,t1.stat_period_id                 
         ,t1.start_period_id                
         ,t1.end_period_id      		 
         ,case when attr_group_code = '006' or  attr_group_code = '008'   or  attr_group_code = '014'  or  attr_group_code = '016' 
			   then 'RICT001' 
			   else t1.lv0_prod_list_code        
			   end as bg_code       
         ,case when attr_group_code = '006' or  attr_group_code = '008'   or  attr_group_code = '014'  or  attr_group_code = '016' 
			   then 'ICT' 
			   else t1.lv0_prod_list_cn_name     
			   end as bg_name           
         ,t1.lv1_prod_rnd_team_code         
         ,t1.lv1_prod_rd_team_en_name       
         ,t1.lv1_prod_rd_team_cn_name       
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name  	 
         ,case when attr_group_code = '017' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			   then t1.dimension_group_code_l2  
               when attr_group_code in ( '006','007','008','009')	
               then null    			   
      		   else t1.product_dimension_group_code
               end as dimension_group_code    /*量纲分组*/                                      
         ,case when attr_group_code = '017' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			   then t1.dimension_group_l2_cn_name   
               when attr_group_code in ( '006','007','008','009')	
               then null 			   
      		   else t1.product_dimension_group         
			    end as dimension_group_cn_name    /*量纲中文名*/                                   
         ,case when attr_group_code = '017' and t1.lv0_prod_list_code = 'PDCG901159' and t1.domestic_or_oversea_code = 'GH0002' 
			   then t1.dimension_group_l2_en_name 
               when attr_group_code in ( '006','007','008','009')	
               then null 			   
      		   else t1.product_dimension_group_en_name 
			   end as dimension_group_en_name 	  /*量纲英文名*/   
         ,case when attr_group_code in ( '006','007','008','009') 
			   then t1.dimension_subcategory_code  
               else null
               end as	dimension_subcategory_code	  /*量纲子类*/	   
         ,case when attr_group_code in ( '006','007','008','009') 
			   then t1.dimension_subcategory_cn_name  
               else null
               end as dimension_subcategory_cn_name  /*量纲子类中文名*/
         ,case when attr_group_code in ( '006','007','008','009') 
			   then t1.dimension_subcategory_en_name  
               else null
               end as dimension_subcategory_en_name  /*量纲子类英文名*/
         ,case when attr_group_code = '006' or  attr_group_code = '007'   or  attr_group_code = '014'  or  attr_group_code = '015' 
			   then 'GH0001' 
			   else t1.domestic_or_oversea_code        
			   end as oversea_code    /*区域*/
         ,case when attr_group_code = '006' or  attr_group_code = '007'   or  attr_group_code = '014'  or  attr_group_code = '015' 
			   then '全球' 
			   else t1.domestic_or_oversea_cname       
			   end as oversea_cname   /*区域中文名*/
         ,case when attr_group_code = '006' or  attr_group_code = '007'   or  attr_group_code = '014'  or  attr_group_code = '015' 
			   then 'GH0001' 
			   else t1.domestic_or_oversea_ename       
			   end as oversea_ename        /*区域英文名*/                                                                           
         ,t1.bs_value                       
         ,t1.bs_denominator_value     /*分母值 (期初存货+本期发货）*/             
         ,t1.bs_numerator_value      /*分子值 收入量也叫结转量（期初存货+本期发货-期末存货） */   
         ,t1.supply_center_ship      /*发货时点业务量 */   		 
		from  fin_dm_opt_fop.dwk_bs_ps_bv_carryover_rate_i t1
		where 1=1
		and bs_code = 'BS_NPS_00000001'
		and attr_group_code in ( '006','007','008','009','014','015','016','017')	
        and time_window_code = 'YTD'			
		;

          drop table if exists scenarios_tmp;
	    create temporary table  scenarios_tmp
		          as 
         select t1.time_window_code                                       
         ,t1.end_period_id    as period_id     		 
         ,t1.bg_code       
         ,t1.bg_name 
         ,t1.oversea_code
         ,t1.oversea_cname as oversea_desc
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 
         ,t1.lv1_prod_rd_team_en_name 		 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name                                                           			   
         ,'量纲分组' as scenarios	                      /*场景*/		 
         ,t1.dimension_group_code            /*量纲分组*/                                      
         ,t1.dimension_group_cn_name         /*量纲中文名*/                                   
         ,t1.dimension_group_en_name 	      /*量纲英文名*/   
         ,t1.dimension_subcategory_code	  /*量纲子类*/	   
         ,t1.dimension_subcategory_cn_name   /*量纲子类中文名*/
         ,t1.dimension_subcategory_en_name   /*量纲子类英文名*/                                                                          
         ,t1.bs_value  as carryover_rate     
         ,t1.bs_denominator_value  /*分母值 (期初存货+本期发货）*/   
         ,t1.bs_numerator_value    as rev_qty  /*分子值 收入量也叫结转量（历史）*/ 
         ,t1.supply_center_ship    as ship_qty /*发货量（历史） */ 	 
        from carryover_tmp t1	 
		where t1.lv1_prod_rnd_team_code in ('134557','133277','137565')	   /*光、计算、数据通信*/
		union all
		 select t1.time_window_code                                       
         ,t1.end_period_id    as period_id     		 
         ,t1.bg_code       
         ,t1.bg_name 
         ,t1.oversea_code
         ,t1.oversea_cname as oversea_desc
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 
         ,t1.lv1_prod_rd_team_en_name 		 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name                                                           			   
         ,'量纲子类' as scenarios	                      /*场景*/		 
         ,t1.dimension_group_code            /*量纲分组*/                                      
         ,t1.dimension_group_cn_name         /*量纲中文名*/                                   
         ,t1.dimension_group_en_name 	      /*量纲英文名*/   
         ,t1.dimension_subcategory_code	  /*量纲子类*/	   
         ,t1.dimension_subcategory_cn_name   /*量纲子类中文名*/
         ,t1.dimension_subcategory_en_name   /*量纲子类英文名*/                                                                          
         ,t1.bs_value  as carryover_rate     
         ,t1.bs_denominator_value  /*分母值 (期初存货+本期发货）*/   
         ,t1.bs_numerator_value    as rev_qty  /*分子值 收入量也叫结转量（历史）*/ 
         ,t1.supply_center_ship    as ship_qty /*发货量（历史） */ 	 		 
        from carryover_tmp t1	 
	    where t1.lv1_prod_rnd_team_code in ('100001','101775')	   /*无线、数据存储*/
		union all
		select t1.time_window_code                                       
         ,t1.end_period_id    as period_id     		 
         ,t1.bg_code       
         ,t1.bg_name 
         ,t1.oversea_code
         ,t1.oversea_cname as oversea_desc
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 
         ,t1.lv1_prod_rd_team_en_name 		 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name       
         ,t1.lv2_prod_rd_team_en_name                                                           			   
         ,'LV2' as scenarios	                      /*场景*/		 
         ,t1.dimension_group_code            /*量纲分组*/                                      
         ,t1.dimension_group_cn_name         /*量纲中文名*/                                   
         ,t1.dimension_group_en_name 	      /*量纲英文名*/   
         ,t1.dimension_subcategory_code	  /*量纲子类*/	   
         ,t1.dimension_subcategory_cn_name   /*量纲子类中文名*/
         ,t1.dimension_subcategory_en_name   /*量纲子类英文名*/                                                                          
         ,t1.bs_value  as carryover_rate     
         ,t1.bs_denominator_value  /*分母值 (期初存货+本期发货）*/   
         ,t1.bs_numerator_value    as rev_qty  /*分子值 收入量也叫结转量（历史）*/ 
         ,t1.supply_center_ship    as ship_qty /*发货量（历史） */ 	 		 
        from carryover_tmp t1	 
	    where t1.lv1_prod_rnd_team_code in ('100011')	   /*云核心网*/
		;
		
		-- 删除最大版本数据
      delete fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t where version_code = v_version_code;
	  
	  insert into fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t(	
	   version_code
	  ,time_window_code
      ,period_id
      ,bg_code
      ,bg_name
      ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv1_prod_rd_team_en_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
      ,lv2_prod_rd_team_en_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,carryover_rate
	  ,ship_qty /*发货量（历史） */  
	  ,rev_qty /*收入量（历史）*/ 	
      ,source_table
      ,remark
      ,created_by
      ,creation_date
      ,last_updated_by
      ,last_update_date
      ,del_flag
	  )
	  select v_version_code   as version_code
	     ,time_window_code                                       
         ,period_id     		 
         ,bg_code       
         ,bg_name 
         ,oversea_code
          ,case when oversea_code	= 'GH0002'  
		       then '国内' 
			   else oversea_desc
			   end as oversea_desc
         ,lv1_prod_rnd_team_code                       
         ,lv1_prod_rd_team_cn_name 
         ,lv1_prod_rd_team_en_name 		 
         ,lv2_prod_rnd_team_code         
         ,lv2_prod_rd_team_cn_name       
         ,lv2_prod_rd_team_en_name                                                           			   
         ,scenarios	                      /*场景*/		 
         ,dimension_group_code            /*量纲分组*/                                      
         ,dimension_group_cn_name         /*量纲中文名*/                                   
         ,dimension_group_en_name 	      /*量纲英文名*/   
         ,dimension_subcategory_code	  /*量纲子类*/	   
         ,dimension_subcategory_cn_name   /*量纲子类中文名*/
         ,dimension_subcategory_en_name   /*量纲子类英文名*/                                                                          
         ,carryover_rate               
         ,ship_qty /*发货量（历史） */  
	     ,rev_qty /*收入量（历史）*/ 
		 ,'dwk_bs_ps_bv_carryover_rate_i' as source_table
         ,'' as remark
 	     , -1 as created_by
 	     , current_timestamp as creation_date
 	     , -1 as last_updated_by
 	     , current_timestamp as last_update_date
 	     , 'N' as del_flag	
        from scenarios_tmp t1	 
		where oversea_code not in ('GH0004')	-- 只取 中国区 和 海外的数据，剔除其他的数据	
		;
		
		
		-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码:'||v_version_code||',dm_fop_dimension_carryover_sum_t 目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
			) ;
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t;
	
		
 end;
 $BODY$
 LANGUAGE plpgsql VOLATILE
  COST 100

