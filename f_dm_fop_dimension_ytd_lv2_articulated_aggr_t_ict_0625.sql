CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_dimension_ytd_lv2_articulated_aggr_t_ict"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
 /*
创建时间：2025-06-23
创建人  ：wwx1077703
背景描述：将量纲层的量本价勾稽后数据处理中的cnbg,ebg聚合到ict的逻辑


参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_ytd_dimension_articulated_aggr_t_ict();
*/
 
 declare
	v_sp_name VARCHAR(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_ytd_lv2_articulated_aggr_t_ict('||p_version_code||')';
	v_tbl_name VARCHAR(100) := 'fin_dm_opt_fop.dm_fop_dimension_ytd_lv2_articulated_aggr_t';
	v_version_code VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   BIGINT; --步骤号
	v_dml_row_count  NUMBER DEFAULT 0 ;
  v_max_ytd  VARCHAR(50);  -- 最大期次的上一年
  v_ict_code VARCHAR(50) := 'PROD0002';
	


begin
	x_success_flag := '1';                                 --1表示成功
  

      
	
	       -- 获取版本号，如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select max(version_code)  into v_version_code
        from fin_dm_opt_fop.dm_fop_dimension_ytd_lv2_articulated_aggr_t ;	 -- 目标表和来源表是同一个表
        end if 
        ;	
        
        select  ((left(max(period_id)::VARCHAR,4)::numeric-1)::VARCHAR)||'12'  into  v_max_ytd  
        from fin_dm_opt_fop.dm_fop_dimension_ytd_lv2_articulated_aggr_t 
         where  version_code= v_version_code
        and  del_flag = 'N' 
        and  bg_code = any(ARRAY['PDCG901160','PDCG901159']) 
        ;
        
	
-- 	 --1.开始日志
--   v_step_mum := 1;
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '量本勾稽处理后量纲表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',获取最大版本和上年',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  

--       1、将cnbg,ebg的收入、成本、结转量、发货量拿过来，用于在ict计算指标的基础数据。
          drop table if exists dm_fop_ytd_dimension_lv2_articulated_aggr_ict_tmp;
          create temporary table  dm_fop_ytd_dimension_lv2_articulated_aggr_ict_tmp
          as
          SELECT
            agt.version_code,
            agt.period_id,
            agt.target_period,
            agt.scenarios,
            agt.phase_date,
            v_ict_code  as  bg_code, --
            'ICT'  as  bg_name,
            agt.oversea_code,
            agt.oversea_desc,
            agt.lv1_code,
            agt.lv1_name,
            agt.lv2_code,
            agt.lv2_name,
            agt.currency,
            agt.fcst_type,
            agt.sop_type,
            sum(agt.equip_rev_cons_before_amt)  as  equip_rev_cons_before_amt,            -- 设备收入额（对价前）
            sum(agt.equip_cost_cons_before_amt)  as  equip_cost_cons_before_amt,           -- 设备成本额（对价前）
            sum(agt.equip_rev_cons_after_amt)  as  equip_rev_cons_after_amt,            -- 设备收入额（对价后）
            sum(agt.equip_cost_cons_after_amt)  as  equip_cost_cons_after_amt,           -- 设备成本额（对价后）
            'dm_fop_ytd_dimension_articulated_aggr_t'  as source_table,                         -- 来源表
            '1.预测数把cnbg,ebg的收入、成本、发货量、收入量卷积到ICT'  as  remark
          FROM
            fin_dm_opt_fop.dm_fop_dimension_ytd_lv2_articulated_aggr_t  agt 
         where  agt.del_flag = 'N'  and  agt.version_code =  v_version_code
         and  agt.bg_code = any(ARRAY['PDCG901160','PDCG901159']) -- limit 100
         group by  
            agt.version_code,
            agt.period_id,
            agt.target_period,
            agt.scenarios,
            agt.phase_date,
            agt.oversea_code,
            agt.oversea_desc,
            agt.lv1_code,
            agt.lv1_name,
            agt.lv2_code,
            agt.lv2_name,
            agt.currency,
            agt.fcst_type,
            agt.sop_type 
         union  all 
         select   
            tpt.version_code,
            tpt.period_id,
            tpt.target_period,
            tpt.scenarios,
            'TGT'  as phase_date,
            tpt.bg_code, --v_ict_code
            tpt.bg_name,
            tpt.oversea_code,
            tpt.oversea_desc,
            tpt.lv1_code,
            tpt.lv1_name,
            tpt.lv2_code,
            tpt.lv2_name,
            tpt.currency,
            'TGT'  as fcst_type,
            'TGT'  as sop_type,  
            tpt.equip_rev_cons_before_amt,            -- 设备收入额（对价前）
            tpt.equip_cost_cons_before_amt,           -- 设备成本额（对价前）
            tpt.equip_rev_cons_after_amt,            -- 设备收入额（对价后）
            tpt.equip_cost_cons_after_amt,           -- 设备成本额（对价后）
            'dm_fop_dimension_tgt_period_t'  as source_table,                         -- 来源表
            '1.实际数把ict的收入、成本、拿过来'  as  remark -- SELECT   *
        from   fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t  tpt
        where   tpt.del_flag = 'N' 
        and     RIGHT(tpt.target_period,3) = 'YTD' and  tpt.period_id = v_max_ytd
        and     tpt.bg_code = v_ict_code
        and     tpt.scenarios != 'LV1'
         ;
            
       
       v_dml_row_count := sql%rowcount;  -- 收集数据量
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '把ebg、cnbg上年的数据和当年的数据一起灌倒临时表中',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
--       2、通过上一步中获取到的收入、成本来计算率指标
          drop table if exists dm_fop_ytd_dimension_lv2_articulated_aggr_ict_all_item_tmp;
          create temporary table  dm_fop_ytd_dimension_lv2_articulated_aggr_ict_all_item_tmp
          as
          select   
          ait.version_code,
            ait.period_id,
            ait.target_period,
            ait.scenarios,
            ait.phase_date,
            ait.bg_code,
            ait.bg_name,
            ait.oversea_code,
            ait.oversea_desc,
            ait.lv1_code,
            ait.lv1_name,
            ait.lv2_code,
            ait.lv2_name,
            ait.currency,
            ait.fcst_type,
            ait.sop_type,
            ait.equip_rev_cons_before_amt,            -- 设备收入额（对价前）
            ait.equip_cost_cons_before_amt,           -- 设备成本额（对价前）
            ait.equip_rev_cons_after_amt,            -- 设备收入额（对价前）
            ait.equip_cost_cons_after_amt,           -- 设备成本额（对价前）
            case when  ait.scenarios='LV2' then null 
              else 
                case when ait.equip_rev_cons_before_amt=0 then -999999
                     else (ait.equip_rev_cons_before_amt -ait.equip_cost_cons_before_amt )/ait.equip_rev_cons_before_amt end
             end   as  mgp_ratio_before,    -- 制毛率对价前
            case when ait.equip_rev_cons_after_amt=0 then -999999
                     else (ait.equip_rev_cons_after_amt -ait.equip_cost_cons_after_amt )/ait.equip_rev_cons_after_amt end  as  mgp_ratio_after,    -- 制毛率对价后                        
            case when  ait.scenarios='LV2' then null 
              else 
                case when ait.equip_rev_cons_before_amt=0 then -999999
                     else (ait.equip_rev_cons_before_amt -ait.equip_rev_cons_after_amt )/ait.equip_rev_cons_before_amt end
             end   as    mca_adjust_ratio,   -- MCA调整率 
            case when  ait.scenarios='LV2' then null 
              else 
               (case when ait.equip_rev_cons_after_amt=0 then -999999
                     else (ait.equip_rev_cons_after_amt -ait.equip_cost_cons_after_amt )/ait.equip_rev_cons_after_amt end) - 
               ( case when ait.equip_rev_cons_before_amt=0 then -999999
               else (ait.equip_rev_cons_before_amt -ait.equip_cost_cons_before_amt )/ait.equip_rev_cons_before_amt end) 
             end    as    mgp_adjust_rate,                -- 制毛调整率
            ait.source_table,                         -- 来源表
            ait.remark||'2.'||'率和占比的处理。'  as  remark
     from   dm_fop_ytd_dimension_lv2_articulated_aggr_ict_tmp  ait  ;
     
      v_dml_row_count := sql%rowcount;  -- 收集数据量
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '率指标和占比指标处理之后',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
     
--         更新制毛率的异常值，取最大期次的上年          
                  
        drop table if exists max_ytd_mgp_ratio_tmp;
        create temporary table  max_ytd_mgp_ratio_tmp
        as 
        SELECT
          version_code,
          period_id,
          target_period,
          scenarios,
          phase_date,
          bg_code,
          oversea_code,
          lv1_code,
          lv2_code,
          currency,
          fcst_type,
          sop_type,
          mgp_ratio_after 
        FROM
          dm_fop_ytd_dimension_lv2_articulated_aggr_ict_all_item_tmp
        where   period_id::varchar =  v_max_ytd 
         ;
        
      v_dml_row_count := sql%rowcount;  -- 收集数据量
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '上年的制毛率对价后',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
        
        update   dm_fop_ytd_dimension_lv2_articulated_aggr_ict_all_item_tmp  tmp    
        SET tmp.mgp_ratio_after=  upd.mgp_ratio_after ,
        tmp.remark = tmp.remark||'，制毛率取上年值' 
        from  max_ytd_mgp_ratio_tmp upd 
        where    ( tmp.mgp_ratio_after > 1 or tmp.mgp_ratio_after < -1 )
        and    tmp.mgp_ratio_after != -999999
        and    tmp.phase_date !='TGT' 
        and  tmp.scenarios = upd.scenarios  
        and tmp.bg_code = upd.bg_code
        and tmp.oversea_code = upd.oversea_code 
        and tmp.lv1_code = upd.lv1_code  
        and  coalesce(tmp.lv2_code,'SNULLC')  = coalesce(upd.lv2_code ,'SNULLC')
        and tmp.currency = upd.currency ; 
        
        
        update   dm_fop_ytd_dimension_lv2_articulated_aggr_ict_all_item_tmp  tmp    
        SET tmp.mgp_ratio_after=  0 ,
        tmp.remark = tmp.remark||'，制毛率取上年值之后的兜底处理' 
        where    ( tmp.mgp_ratio_after > 1 or tmp.mgp_ratio_after < -1 )
        and    tmp.mgp_ratio_after != -999999
        and    tmp.phase_date !='TGT' ;
        
        
        delete   from  fin_dm_opt_fop.dm_fop_dimension_ytd_lv2_articulated_aggr_t  where  version_code = v_version_code  and  bg_code = v_ict_code  ;  --v_ict_code
        
        INSERT INTO fin_dm_opt_fop.dm_fop_dimension_ytd_lv2_articulated_aggr_t (
          version_code,
          period_id,
          target_period,
          scenarios,
          phase_date,
          bg_code,
          bg_name,
          oversea_code,
          oversea_desc,
          lv1_code,
          lv1_name,
          lv2_code,
          lv2_name,
          currency,
          fcst_type,
          sop_type,
          equip_rev_cons_before_amt,
          equip_cost_cons_before_amt,
          equip_rev_cons_after_amt,
          equip_cost_cons_after_amt,
          mgp_ratio_before,
          mgp_ratio_after,
          mca_adjust_ratio,
          mgp_adjust_rate,
          source_table,
          remark,
          created_by,
          creation_date,
          last_updated_by,
          last_update_date,
          del_flag
        )
        select 
        version_code,
          period_id,
          target_period,
          scenarios,
          phase_date,
          bg_code,
          bg_name,
          oversea_code,
          oversea_desc,
          lv1_code,
          lv1_name,
          lv2_code,
          lv2_name,
          currency,
          fcst_type,
          sop_type,
          equip_rev_cons_before_amt,
          equip_cost_cons_before_amt,
          equip_rev_cons_after_amt,
          equip_cost_cons_after_amt,
          mgp_ratio_before,
          mgp_ratio_after,
          mca_adjust_ratio,
          mgp_adjust_rate,
          source_table,
          remark,
          -1  as created_by,
          now()  as creation_date,
          -1  as last_updated_by,
          now()  as last_update_date,
          'N'  as  del_flag
          from  dm_fop_ytd_dimension_lv2_articulated_aggr_ict_all_item_tmp 
          where   phase_date !='TGT' ;

        

  	-- 写结束日志
    v_dml_row_count := sql%rowcount;  -- 收集数据量
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 5,
        p_log_cal_log_desc => '量本勾稽后的LV2表的ICT汇总'||v_tbl_name||',目标表中最大版本编码:'||v_version_code,--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
			) ;
	x_success_flag := '2001';
	
	
		
 end;
 $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100 ;