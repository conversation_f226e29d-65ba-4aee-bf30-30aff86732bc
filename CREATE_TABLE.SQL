DROP TABLE IF EXISTS dm_fop_dimension_articulated_aggr_t;
SET search_path = fin_dm_opt_fop;
CREATE  TABLE dm_fop_dimension_articulated_aggr_t (
    version_code character varying(100),
    period_id numeric,
    target_period varchar(50),
    scenarios character varying(50),
    phase_date character varying(100),
    bg_code character varying(50),
    bg_name character varying(200),
    oversea_code character varying(50),
    oversea_desc character varying(50),
    lv1_code character varying(50),
    lv1_name character varying(600),
    lv2_code character varying(50),
    lv2_name character varying(600),
    dimension_group_code character varying(50),
    dimension_group_cn_name character varying(600),
    dimension_group_en_name character varying(600),
    dimension_subcategory_code character varying(50),
    dimension_subcategory_cn_name character varying(600),
    dimension_subcategory_en_name character varying(600),
    currency_code character varying(50),
    fcst_type character varying(100),
    equip_rev_cons_before_amt numeric(38,10),
    equip_cost_cons_before_amt numeric(38,10),
    carryover_qty numeric(38,10),
    ship_qty numeric(38,10), 
    rev_percent numeric(38,10),
    unit_price numeric(38,10),
    unit_cost numeric(38,10),
    mgp_ratio numeric(38,10),
    carryover_rate numeric(38,10),
    unit_price_fcst_upper numeric(38,10),
    unit_price_fcst_lower numeric(38,10),
    unit_cost_fcst_upper numeric(38,10),
    unit_cost_fcst_lower numeric(38,10),
    source_table character varying(100),
    remark character varying(500),
    created_by bigint,
    creation_date timestamp without time zone,
    last_updated_by bigint,
    last_update_date timestamp without time zone,
    del_flag character varying(10)
)
WITH (orientation=column, compression=low, colversion=2.0, enable_delta=false)
DISTRIBUTE BY HASH(version_code, target_period)
TO GROUP group_version1;
COMMENT ON TABLE dm_fop_dimension_articulated_aggr_t IS '量纲层级勾稽后聚合表';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.version_code IS '版本编码';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.period_id IS '会计期';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.target_period IS '预测时点';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.phase_date IS 'SOP期次';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.bg_code IS 'BG编码';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.bg_name IS 'BG名称';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.oversea_code IS '区域编码';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.oversea_desc IS '区域名称';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.lv1_code IS '重量级团队LV1编码';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.lv1_name IS '重量级团队LV1中文描述';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.lv2_code IS '重量级团队LV2编码';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.lv2_name IS '重量级团队LV2中文描述';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.scenarios IS '场景';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.dimension_group_code IS '量纲分组编码';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.dimension_group_cn_name IS '量纲分组中文名';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.dimension_group_en_name IS '量纲分组英文名称';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.dimension_subcategory_code IS '量纲子类编码';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.dimension_subcategory_cn_name IS '量纲子类中文名称';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.dimension_subcategory_en_name IS '量纲子类英文名称';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.equip_rev_cons_before_amt IS '设备收入额（对价前）';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.equip_cost_cons_before_amt IS '设备成本额（对价前）';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.fcst_type IS '预测方法';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.carryover_qty IS '结转量';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.ship_qty IS '发货量';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.rev_percent IS '收入占比';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.unit_price IS '单位价格';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.unit_cost IS '单位成本';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.mgp_ratio IS '制毛率';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.carryover_rate IS '结转率';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.unit_price_fcst_upper IS '单位价格置信上界';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.unit_price_fcst_lower IS '单位价格置信下界';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.unit_cost_fcst_upper IS '单位成本置信上界';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.unit_cost_fcst_lower IS '单位成本置信下界';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.currency_code IS '币种';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.source_table IS '来源表';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.remark IS '备注';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.created_by IS '创建人';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.creation_date IS '创建时间';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.last_updated_by IS '修改人';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.last_update_date IS '修改时间';
COMMENT ON COLUMN dm_fop_dimension_articulated_aggr_t.del_flag IS '是否删除';

DROP TABLE IF EXISTS dm_fop_lv2_articulated_aggr_t;
SET search_path = fin_dm_opt_fop;
CREATE  TABLE dm_fop_lv2_articulated_aggr_t (
    version_code character varying(100),
    period_id numeric,
    target_period varchar(50),
    scenarios character varying(50),
    phase_date character varying(100),
    bg_code character varying(50),
    bg_name character varying(200),
    oversea_code character varying(50),
    oversea_desc character varying(50),
    lv1_code character varying(50),
    lv1_name character varying(600),
    lv1_prod_rd_team_en_name character varying(600),
    lv2_code character varying(50),
    lv2_name character varying(600),
    lv2_prod_rd_team_en_name character varying(600),
    currency_code character varying(50),
    fcst_type character varying(100),
    equip_rev_cons_before_amt numeric(38,10),
    equip_cost_cons_before_amt numeric(38,10),
    equip_rev_cons_AFTER_amt numeric(38,10),
    equip_cost_cons_AFTER_amt numeric(38,10),
    mgp_ratio_BEFORE numeric(38,10),
    mgp_ratio_AFTER numeric(38,10),
    mca_adjust_ratio numeric(38,10),
    mgp_adjust_rate numeric(38,10),
    source_table character varying(100),
    remark character varying(500),
    created_by bigint,
    creation_date timestamp without time zone,
    last_updated_by bigint,
    last_update_date timestamp without time zone,
    del_flag character varying(10)
)
WITH (orientation=column, compression=low, colversion=2.0, enable_delta=false)
DISTRIBUTE BY HASH(version_code, target_period)
TO GROUP group_version1;
COMMENT ON TABLE dm_fop_lv2_articulated_aggr_t IS 'LV2勾稽后聚合表';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.version_code IS '版本编码';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.period_id IS '会计期';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.target_period IS '预测时点';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.phase_date IS 'SOP期次';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.bg_code IS 'BG编码';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.bg_name IS 'BG名称';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.oversea_code IS '区域编码';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.oversea_desc IS '区域名称';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.lv1_code IS '重量级团队LV1编码';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.lv1_name IS '重量级团队LV1中文描述';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.lv2_code IS '重量级团队LV2编码';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.lv2_name IS '重量级团队LV2中文描述';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.scenarios IS '场景';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.equip_rev_cons_before_amt IS '设备收入额（对价前）';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.equip_cost_cons_before_amt IS '设备成本额（对价前）';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.equip_rev_cons_AFTER_amt IS '设备收入额（对价后）';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.equip_cost_cons_AFTER_amt IS '设备成本额（对价后）';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.fcst_type IS '预测方法';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.mgp_ratio_BEFORE IS '制毛率（对价前)';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.mgp_ratio_AFTER IS '制毛率（对价后)';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.mca_adjust_ratio IS 'MCA调整率';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.mgp_adjust_rate IS '制毛调整率';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.currency_code IS '币种';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.source_table IS '来源表';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.remark IS '备注';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.created_by IS '创建人';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.creation_date IS '创建时间';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.last_updated_by IS '修改人';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.last_update_date IS '修改时间';
COMMENT ON COLUMN dm_fop_lv2_articulated_aggr_t.del_flag IS '是否删除';

DROP TABLE IF EXISTS dm_fop_lv1_articulated_aggr_t;
SET search_path = fin_dm_opt_fop;
CREATE  TABLE dm_fop_lv1_articulated_aggr_t (
    version_code character varying(100),
    period_id numeric,
    target_period varchar(50),
    scenarios character varying(50),
    phase_date character varying(100),
    bg_code character varying(50),
    bg_name character varying(200),
    oversea_code character varying(50),
    oversea_desc character varying(50),
    lv1_code character varying(50),
    lv1_name character varying(600),
    lv1_prod_rd_team_en_name character varying(600),
    currency_code character varying(50),
    fcst_type character varying(100),
    equip_rev_cons_AFTER_amt numeric(38,10),
    equip_cost_cons_AFTER_amt numeric(38,10),
    mgp_ratio_AFTER numeric(38,10),
    source_table character varying(100),
    remark character varying(500),
    created_by bigint,
    creation_date timestamp without time zone,
    last_updated_by bigint,
    last_update_date timestamp without time zone,
    del_flag character varying(10)
)
WITH (orientation=column, compression=low, colversion=2.0, enable_delta=false)
DISTRIBUTE BY HASH(version_code, target_period)
TO GROUP group_version1;
COMMENT ON TABLE dm_fop_lv1_articulated_aggr_t IS 'LV2勾稽后聚合表';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.version_code IS '版本编码';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.period_id IS '会计期';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.target_period IS '预测时点';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.phase_date IS 'SOP期次';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.bg_code IS 'BG编码';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.bg_name IS 'BG名称';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.oversea_code IS '区域编码';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.oversea_desc IS '区域名称';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.lv1_code IS '重量级团队LV1编码';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.lv1_name IS '重量级团队LV1中文描述';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.scenarios IS '场景';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.equip_rev_cons_AFTER_amt IS '设备收入额（对价后）';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.equip_cost_cons_AFTER_amt IS '设备成本额（对价后）';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.fcst_type IS '预测方法';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.mgp_ratio_AFTER IS '制毛率（对价后)';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.currency_code IS '币种';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.source_table IS '来源表';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.remark IS '备注';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.created_by IS '创建人';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.creation_date IS '创建时间';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.last_updated_by IS '修改人';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.last_update_date IS '修改时间';
COMMENT ON COLUMN dm_fop_lv1_articulated_aggr_t.del_flag IS '是否删除';

DROP TABLE IF EXISTS dm_fop_ytd_dimension_articulated_aggr_t;
SET search_path = fin_dm_opt_fop;
CREATE  TABLE dm_fop_ytd_dimension_articulated_aggr_t (
    version_code character varying(100),
    period_id numeric,
    target_period varchar(50),
    scenarios character varying(50),
    phase_date character varying(100),
    bg_code character varying(50),
    bg_name character varying(200),
    oversea_code character varying(50),
    oversea_desc character varying(50),
    lv1_code character varying(50),
    lv1_name character varying(600),
    lv1_prod_rd_team_en_name character varying(600),
    lv2_code character varying(50),
    lv2_name character varying(600),
    lv2_prod_rd_team_en_name character varying(600),
    dimension_group_code character varying(50),
    dimension_group_cn_name character varying(600),
    dimension_group_en_name character varying(600),
    dimension_subcategory_code character varying(50),
    dimension_subcategory_cn_name character varying(600),
    dimension_subcategory_en_name character varying(600),
    currency_code character varying(50),
    fcst_type character varying(100),
    equip_rev_cons_before_amt numeric(38,10),
    equip_cost_cons_before_amt numeric(38,10),
    carryover_qty numeric(38,10),
    ship_qty numeric(38,10), 
    rev_percent numeric(38,10),
    unit_price numeric(38,10),
    unit_cost numeric(38,10),
    mgp_ratio numeric(38,10),
    carryover_rate numeric(38,10),
    unit_price_fcst_upper numeric(38,10),
    unit_price_fcst_lower numeric(38,10),
    unit_cost_fcst_upper numeric(38,10),
    unit_cost_fcst_lower numeric(38,10),
    source_table character varying(100),
    remark character varying(500),
    created_by bigint,
    creation_date timestamp without time zone,
    last_updated_by bigint,
    last_update_date timestamp without time zone,
    del_flag character varying(10)
)
WITH (orientation=column, compression=low, colversion=2.0, enable_delta=false)
DISTRIBUTE BY HASH(version_code, target_period)
TO GROUP group_version1;
COMMENT ON TABLE dm_fop_ytd_dimension_articulated_aggr_t IS '量纲层级勾稽后聚合表';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.version_code IS '版本编码';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.period_id IS '会计期';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.target_period IS '预测时点';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.phase_date IS 'SOP期次';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.bg_code IS 'BG编码';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.bg_name IS 'BG名称';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.oversea_code IS '区域编码';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.oversea_desc IS '区域名称';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.lv1_code IS '重量级团队LV1编码';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.lv1_name IS '重量级团队LV1中文描述';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.lv2_code IS '重量级团队LV2编码';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.lv2_name IS '重量级团队LV2中文描述';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.scenarios IS '场景';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.dimension_group_code IS '量纲分组编码';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.dimension_group_cn_name IS '量纲分组中文名';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.dimension_group_en_name IS '量纲分组英文名称';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.dimension_subcategory_code IS '量纲子类编码';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.dimension_subcategory_cn_name IS '量纲子类中文名称';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.dimension_subcategory_en_name IS '量纲子类英文名称';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.equip_rev_cons_before_amt IS '设备收入额（对价前）';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.equip_cost_cons_before_amt IS '设备成本额（对价前）';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.fcst_type IS '预测方法';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.carryover_qty IS '结转量';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.ship_qty IS '发货量';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.rev_percent IS '收入占比';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.unit_price IS '单位价格';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.unit_cost IS '单位成本';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.mgp_ratio IS '制毛率';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.carryover_rate IS '结转率';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.unit_price_fcst_upper IS '单位价格置信上界';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.unit_price_fcst_lower IS '单位价格置信下界';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.unit_cost_fcst_upper IS '单位成本置信上界';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.unit_cost_fcst_lower IS '单位成本置信下界';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.currency_code IS '币种';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.source_table IS '来源表';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.remark IS '备注';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.created_by IS '创建人';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.creation_date IS '创建时间';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.last_updated_by IS '修改人';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.last_update_date IS '修改时间';
COMMENT ON COLUMN dm_fop_ytd_dimension_articulated_aggr_t.del_flag IS '是否删除';

DROP TABLE IF EXISTS dm_fop_ytd_lv2_articulated_aggr_t;
SET search_path = fin_dm_opt_fop;
CREATE  TABLE dm_fop_ytd_lv2_articulated_aggr_t (
    version_code character varying(100),
    period_id numeric,
    target_period varchar(50),
    scenarios character varying(50),
    phase_date character varying(100),
    bg_code character varying(50),
    bg_name character varying(200),
    oversea_code character varying(50),
    oversea_desc character varying(50),
    lv1_code character varying(50),
    lv1_name character varying(600),
    lv1_prod_rd_team_en_name character varying(600),
    lv2_code character varying(50),
    lv2_name character varying(600),
    lv2_prod_rd_team_en_name character varying(600),
    currency_code character varying(50),
    fcst_type character varying(100),
    equip_rev_cons_before_amt numeric(38,10),
    equip_cost_cons_before_amt numeric(38,10),
    equip_rev_cons_AFTER_amt numeric(38,10),
    equip_cost_cons_AFTER_amt numeric(38,10),
    mgp_ratio_BEFORE numeric(38,10),
    mgp_ratio_AFTER numeric(38,10),
    mca_adjust_ratio numeric(38,10),
    mgp_adjust_rate numeric(38,10),
    source_table character varying(100),
    remark character varying(500),
    created_by bigint,
    creation_date timestamp without time zone,
    last_updated_by bigint,
    last_update_date timestamp without time zone,
    del_flag character varying(10)
)
WITH (orientation=column, compression=low, colversion=2.0, enable_delta=false)
DISTRIBUTE BY HASH(version_code, target_period)
TO GROUP group_version1;
COMMENT ON TABLE dm_fop_ytd_lv2_articulated_aggr_t IS 'LV2勾稽后聚合表';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.version_code IS '版本编码';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.period_id IS '会计期';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.target_period IS '预测时点';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.phase_date IS 'SOP期次';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.bg_code IS 'BG编码';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.bg_name IS 'BG名称';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.oversea_code IS '区域编码';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.oversea_desc IS '区域名称';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.lv1_code IS '重量级团队LV1编码';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.lv1_name IS '重量级团队LV1中文描述';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.lv2_code IS '重量级团队LV2编码';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.lv2_name IS '重量级团队LV2中文描述';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.scenarios IS '场景';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.equip_rev_cons_before_amt IS '设备收入额（对价前）';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.equip_cost_cons_before_amt IS '设备成本额（对价前）';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.equip_rev_cons_AFTER_amt IS '设备收入额（对价后）';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.equip_cost_cons_AFTER_amt IS '设备成本额（对价后）';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.fcst_type IS '预测方法';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.mgp_ratio_BEFORE IS '制毛率（对价前)';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.mgp_ratio_AFTER IS '制毛率（对价后)';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.mca_adjust_ratio IS 'MCA调整率';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.mgp_adjust_rate IS '制毛调整率';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.currency_code IS '币种';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.source_table IS '来源表';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.remark IS '备注';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.created_by IS '创建人';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.creation_date IS '创建时间';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.last_updated_by IS '修改人';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.last_update_date IS '修改时间';
COMMENT ON COLUMN dm_fop_ytd_lv2_articulated_aggr_t.del_flag IS '是否删除';

DROP TABLE IF EXISTS dm_fop_ytd_lv1_articulated_aggr_t;
SET search_path = fin_dm_opt_fop;
CREATE  TABLE dm_fop_ytd_lv1_articulated_aggr_t (
    version_code character varying(100),
    period_id numeric,
    target_period varchar(50),
    scenarios character varying(50),
    phase_date character varying(100),
    bg_code character varying(50),
    bg_name character varying(200),
    oversea_code character varying(50),
    oversea_desc character varying(50),
    lv1_code character varying(50),
    lv1_name character varying(600),
    lv1_prod_rd_team_en_name character varying(600),
    currency_code character varying(50),
    fcst_type character varying(100),
    equip_rev_cons_AFTER_amt numeric(38,10),
    equip_cost_cons_AFTER_amt numeric(38,10),
    mgp_ratio_AFTER numeric(38,10),
    source_table character varying(100),
    remark character varying(500),
    created_by bigint,
    creation_date timestamp without time zone,
    last_updated_by bigint,
    last_update_date timestamp without time zone,
    del_flag character varying(10)
)
WITH (orientation=column, compression=low, colversion=2.0, enable_delta=false)
DISTRIBUTE BY HASH(version_code, target_period)
TO GROUP group_version1;
COMMENT ON TABLE dm_fop_ytd_lv1_articulated_aggr_t IS 'LV2勾稽后聚合表';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.version_code IS '版本编码';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.period_id IS '会计期';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.target_period IS '预测时点';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.phase_date IS 'SOP期次';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.bg_code IS 'BG编码';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.bg_name IS 'BG名称';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.oversea_code IS '区域编码';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.oversea_desc IS '区域名称';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.lv1_code IS '重量级团队LV1编码';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.lv1_name IS '重量级团队LV1中文描述';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.scenarios IS '场景';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.equip_rev_cons_AFTER_amt IS '设备收入额（对价后）';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.equip_cost_cons_AFTER_amt IS '设备成本额（对价后）';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.fcst_type IS '预测方法';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.mgp_ratio_AFTER IS '制毛率（对价后)';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.currency_code IS '币种';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.source_table IS '来源表';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.remark IS '备注';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.created_by IS '创建人';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.creation_date IS '创建时间';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.last_updated_by IS '修改人';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.last_update_date IS '修改时间';
COMMENT ON COLUMN dm_fop_ytd_lv1_articulated_aggr_t.del_flag IS '是否删除';

