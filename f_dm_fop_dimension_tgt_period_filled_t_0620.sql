CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_dimension_tgt_period_filled_t"( OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
 /*
创建时间：2025-06-7
创建人  ：wwx1077703
背景描述：将汇总层的数据插入到TGT接口填充表，具体逻辑如下
以上序处理后的TGT量纲表进一步做填充，和占比的处理计算
由于无量纲会在子类和分组做空处理，所以在后面的筛选条件中也是需要做相应的非空处理
率（制毛、收入占比、结转率、均本、均价）：
制毛=1-上面的成本/上面的收入
收入占比 =上面的量纲收入/上面的LV2收入
均价=上面的收入/上面的收入量
整体需要计算ytd、季度、半年度、年度的数据
处理逻辑以dm_fop_dimension_tgt_period_t作为主表来关联对应的数据进行处理



参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_filled_t();
*/
 
 declare
	v_sp_name VARCHAR(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_tgt_period_filled_t()';
	v_tbl_name VARCHAR(100) := 'fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t';
	v_version_code VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   BIGINT; --步骤号
	v_dml_row_count  NUMBER DEFAULT 0 ;
  v_max_ytd  VARCHAR(50);  -- 最大期次的上一年
  dimens_fields TEXT[] := ARRAY['unit_cost','unit_price','rev_percent','carryover_rate','mgp_ratio']; -- 量纲需要调整的字段数组
  dimens_fix_cd TEXT :=' left(tmp.scenarios,2) = ''量纲''  and  coalesce(tmp.dimension_group_code,''SNULLC'') != ''NODIM''  and  coalesce(tmp.dimension_subcategory_code,''SUNLLC'') !=''NOSUB'' '; -- 量纲需要拼接的添加
  undimens_fix_cd TEXT :=' left(tmp.scenarios,2) = ''量纲''  and (  coalesce(tmp.dimension_group_code,''SNULLC'') = ''NODIM''  or  coalesce(tmp.dimension_subcategory_code,''SUNLLC'') =''NOSUB'' ) '; 
  lv2_fix_cd  TEXT := '  left(tmp.scenarios,2) = ''LV''  ';
  dimens_upd TEXT; -- 量纲的更新
  lv2_upd TEXT; -- LV2的更新
  field   VARCHAR(50);
  dimens_query TEXT; -- 量纲的查询，用于更新填充空置
  
  
	


begin
	x_success_flag := '1';                                 --1表示成功
  
  -- 	 --1.开始日志
--   v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
	
	       -- 获取版本号，如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
    
        
        select  (left(max(period_id)::VARCHAR,4)::numeric-1)::VARCHAR  into  v_max_ytd  from fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t ;
	
-- 	 --1.开始日志
--   v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '量纲层级数据TGT表'||v_tbl_name||',获取最大期次的上年',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  

      
      -- 1.把量纲表的TGT表的数据拿过来插入到临时表，用于后续的计算,只拿YTD的数据
      -- 同纬度下，取最大的一条数据  按照收入最大来取
      drop table if exists dm_fop_dimension_tgt_period_tmp;
	    create temporary table  dm_fop_dimension_tgt_period_tmp
		  as
      SELECT
        tgt.version_code,
        tgt.scenarios,
        tgt.time_window_code,
        tgt.period_id,
        tgt.target_period,
        tgt.bg_code,
        tgt.bg_name,
        tgt.oversea_code,
        tgt.oversea_desc,
        tgt.lv1_code,
        tgt.lv1_name,
        tgt.lv2_code,
        tgt.lv2_name,
        tgt.dimension_group_code,
        tgt.dimension_group_cn_name,
        tgt.dimension_group_en_name,
        tgt.dimension_subcategory_code,
        tgt.dimension_subcategory_cn_name,
        tgt.dimension_subcategory_en_name,
        tgt.currency,
        tgt.equip_rev_cons_before_amt,  -- 对价前收入
        tgt.equip_cost_cons_before_amt, -- 对价前成本
        tgt.ship_qty,    -- 收入量
        tgt.rev_qty,     -- 发货量
        tgt.unit_cost,   -- 均本
        tgt.unit_price,  -- 均价
        case when  coalesce(tgt.equip_rev_cons_before_amt,0) = 0 then null else   tgt.equip_cost_cons_before_amt/tgt.equip_rev_cons_before_amt end as   rev_percent, -- 收入占比
        case when  coalesce(tgt.rev_qty,0) = 0 then null else   tgt.ship_qty/tgt.rev_qty end as  carryover_rate,  -- 结转率
        case when  coalesce(tgt.equip_rev_cons_before_amt,0) = 0 then null else ( tgt.equip_rev_cons_before_amt - tgt.equip_cost_cons_before_amt)/tgt.equip_rev_cons_before_amt end as   mgp_ratio,   -- 制毛率
        ''  as  remark 
      FROM
        fin_dm_opt_fop.dm_fop_dimension_tgt_period_t tgt
      WHERE
        tgt.del_flag = 'N'  and  right(target_period,3) = 'YTD' ;
      
      
      
      
      
      
      
       -- 单独处理lv2层的收入
      drop table if exists dm_fop_dimension_upd_rev_percent_process_tmp;
	    create temporary table  dm_fop_dimension_upd_rev_percent_process_tmp
		  as
      with   lv2_sum  as (
      select   
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  as lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency	                      -- 币种 
          ,sum(base.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt 
        from   dm_fop_dimension_tgt_period_tmp  base 
        where  1=1  
        group by 
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency	
      ), lv2_ran_sum
        as (
        select   
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  as lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.dimension_subcategory_code	                      -- 币种 
          ,base.currency	                      -- 币种 
          ,sum(base.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt 
          ,sum(base.equip_cost_cons_before_amt)  as equip_cost_cons_before_amt 
        from   dm_fop_dimension_tgt_period_tmp  base 
        where   base.lv2_code in   ('133661', '101212', '153326', '153324') 
        and   dimension_subcategory_code='NOSUB'
        group by 
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.dimension_subcategory_code	   
          ,base.currency	   
        )
                  
         select   
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.time_window_code	              -- 统计时间窗 
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,base.lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency	                      -- 币种 
          ,base.equip_rev_cons_before_amt 
          ,lrs.equip_rev_cons_before_amt  as ran_rev_cons_before_amt
          ,lrs.equip_cost_cons_before_amt  as ran_cost_cons_before_amt
        from   lv2_sum  base 
        left join lv2_ran_sum lrs 
        on  base.version_code = lrs.version_code	                  -- 版本编码
         and  base.scenarios=	lrs.scenarios                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
         and  base.time_window_code=	 lrs.time_window_code             -- 统计时间窗 
         and  base.period_id=	  lrs.period_id                    -- 会计期
         and  base.target_period=	 lrs.target_period                 -- 目标时点 无 我来计算的
         and  base.bg_code=	  lrs.bg_code                        -- BG编码
         and  base.oversea_code=	lrs.oversea_code                  -- 区域编码
         and  base.lv1_code=	lrs.lv1_code                      -- 重量级团队LV1编码
         and  base.lv2_vir= lrs.lv2_vir   -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
         and  base.currency=	lrs.currency 
          ;              -- 币种  ;
      
       -- 更新收入占比，制毛率,RAN里面的无量纲是相同的一个值，所以先聚合，在关联
        update   dm_fop_dimension_tgt_period_tmp  tmp    
        SET tmp.rev_percent=  case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324') and  tmp.dimension_subcategory_code='NOSUB' 
                                  then  
                                     case when coalesce(upd.equip_rev_cons_before_amt , 0 )=0 then null 
                                         else  upd.ran_rev_cons_before_amt/upd.equip_rev_cons_before_amt end
                                else
                                    case when coalesce(upd.equip_rev_cons_before_amt , 0 )=0 then null  
                                         else  tmp.equip_rev_cons_before_amt/upd.equip_rev_cons_before_amt end 
                                 end,
              tmp.mgp_ratio= case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324') and  tmp.dimension_subcategory_code='NOSUB' 
                                  then
                                     case when coalesce(upd.equip_rev_cons_before_amt , 0 )=0 then null 
                                      else  (1-upd.ran_cost_cons_before_amt/upd.ran_rev_cons_before_amt) end 
                                  else 
                                       tmp.mgp_ratio  
                                       end  ,
             tmp.remark = '计算占比和制毛率'
        from  dm_fop_dimension_upd_rev_percent_process_tmp upd 
        where   1=1   
        and   tmp.version_code = upd.version_code  
        and  tmp.scenarios = upd.scenarios  
        and tmp.time_window_code = upd.time_window_code 
        and tmp.period_id=upd.period_id  
        and tmp.target_period=upd.target_period  
        and tmp.bg_code = upd.bg_code
        and tmp.oversea_code = upd.oversea_code 
        and tmp.lv1_code = upd.lv1_code  
        and  case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324')  then 'RAN' else tmp.lv2_code  end = upd.lv2_vir 
        and tmp.currency = upd.currency ;
          

-- 开始填充
-- drop table if exists dm_fop_dimension_upd_tmp; 
-- create temporary table dm_fop_dimension_upd_tmp
-- 				 as
-- 				 select   
-- 					version_code, 
-- 					scenarios, 
-- 					time_window_code, 
-- 					period_id, 
-- 					bg_code,  
-- 					oversea_code,
-- 					lv1_code, 
-- 					lv2_code, 
-- 					dimension_group_code,
-- 					dimension_subcategory_code, 
-- 					currency,
-- 					data_upt  as  unit_cost
-- 					from   (
-- 			 select   
-- 					  wnf.version_code, 
-- 					  wnf.scenarios, 
-- 					  wnf.time_window_code, 
-- 					  wnf.period_id, 
-- 					  wnf.bg_code, 
-- 					  wnf.oversea_code, 
-- 					  wnf.lv1_code, 
-- 					  wnf.lv2_code, 
-- 					  wnf.dimension_group_code,  
-- 					  wnf.dimension_subcategory_code, 
-- 					  wnf.currency, 
-- 				   ROW_NUMBER()over(partition by wnf.version_code,wnf.scenarios,wnf.time_window_code,wnf.bg_code,wnf.oversea_code,wnf.lv1_code,wnf.lv2_code,wnf.dimension_group_code,
-- 					 wnf.dimension_subcategory_code,wnf.currency,wnf.period_id order by (wnf.period_id - inf.period_id)  )  as  rn ,
-- 					 coalesce(inf.unit_cost,0)  as   data_upt
-- 							 from   (  -- 321
-- 							select version_code, scenarios, time_window_code, period_id, bg_code, oversea_code,lv1_code, lv2_code, dimension_group_code,dimension_subcategory_code,currency
-- 							from   dm_fop_dimension_tgt_period_tmp    
-- 							where   unit_cost is null
-- 							) wnf 
-- 							left  join dm_fop_dimension_tgt_period_tmp inf 
-- 							on inf.version_code = wnf.version_code  
--                  and  inf.scenarios = wnf.scenarios  
--                  and inf.time_window_code = wnf.time_window_code 
--                  and inf.bg_code=wnf.bg_code  
-- 								 and inf.lv1_code = wnf.lv1_code  
--                  and inf.oversea_code = wnf.oversea_code  
--                  and inf.lv2_code = wnf.lv2_code  
-- 								 and coalesce(inf.dimension_group_code,'SNULLC')=coalesce(wnf.dimension_group_code,'SNULLC') 
--                  and coalesce(inf.dimension_subcategory_code,'SNULLC')=coalesce(wnf.dimension_subcategory_code,'SNULLC') 
--                  and inf.currency = wnf.currency  
-- 								 and inf.period_id < wnf.period_id  
--                  and  inf.unit_cost  is  not  null 
-- 							)a 
-- 							where  a.rn=1  ;
--               
--               update   dm_fop_dimension_tgt_period_tmp  tmp    
--                   SET tmp.unit_cost=upd.unit_cost
--                   from  dm_fop_dimension_upd_tmp upd 
--                   where   left(tmp.scenarios,2) = '量纲'  and  tmp.dimension_group_code != 'NODIM'  and  tmp.dimension_subcategory_code !='NOSUB' 
--                   and   tmp.version_code = upd.version_code  
--                   and  tmp.scenarios = upd.scenarios  
--                   and tmp.time_window_code = upd.time_window_code 
--                   and tmp.bg_code=upd.bg_code  
--                   and tmp.lv1_code = upd.lv1_code  
--                   and tmp.oversea_code = upd.oversea_code  
--                   and tmp.lv2_code = upd.lv2_code  
--                   and coalesce(tmp.dimension_group_code,'SNULLC')=coalesce(upd.dimension_group_code,'SNULLC') 
--                   and coalesce(tmp.dimension_subcategory_code,'SNULLC')=coalesce(upd.dimension_subcategory_code,'SNULLC') 
--                   and tmp.currency = wnf.currency  
--                   and tmp.period_id = upd.period_id ;

BEGIN
			-- 有量纲的填充
-- 			FOR field  IN SELECT * FROM unnest(dimens_fields) LOOP
			FOR idx IN 1..array_length(dimens_fields, 1) LOOP
			field := replace(dimens_fields[idx], E'\\{', ''); -- 去除括号
			drop table if exists dm_fop_dimension_upd_tmp;    
			-- 动态拼接 SQL 查询，替换字段名			更新表的语句用一个范围，更新动作分别处理
			dimens_query := '
				create temporary table dm_fop_dimension_upd_tmp
				 as
				 select   
					version_code, 
					scenarios, 
					time_window_code, 
					period_id, 
					bg_code,  
					oversea_code,
					lv1_code, 
					lv2_code, 
					dimension_group_code,
					dimension_subcategory_code, 
					currency,
					data_upt  as  '|| field ||'
					from   (
			 select   
					  wnf.version_code, 
					  wnf.scenarios, 
					  wnf.time_window_code, 
					  wnf.period_id, 
					  wnf.bg_code, 
					  wnf.oversea_code, 
					  wnf.lv1_code, 
					  wnf.lv2_code, 
					  wnf.dimension_group_code,  
					  wnf.dimension_subcategory_code, 
					  wnf.currency, 
				   ROW_NUMBER()over(partition by wnf.version_code,wnf.scenarios,wnf.time_window_code,wnf.bg_code,wnf.oversea_code,wnf.lv1_code,wnf.lv2_code,wnf.dimension_group_code,
					 wnf.dimension_subcategory_code,wnf.currency,wnf.period_id order by (wnf.period_id - inf.period_id)  )  as  rn ,
					 coalesce(inf.'|| field ||',0)  as   data_upt
							 from   (  -- 321
							select version_code, scenarios, time_window_code, period_id, bg_code, oversea_code,lv1_code, lv2_code, dimension_group_code,dimension_subcategory_code,currency
							from   dm_fop_dimension_tgt_period_tmp    
							where   '|| field || ' is null
							) wnf 
							left  join dm_fop_dimension_tgt_period_tmp inf 
							on inf.version_code = wnf.version_code  
                 and  inf.scenarios = wnf.scenarios  
                 and inf.time_window_code = wnf.time_window_code 
                 and inf.bg_code=wnf.bg_code  
								 and inf.lv1_code = wnf.lv1_code  
                 and inf.oversea_code = wnf.oversea_code  
                 and inf.lv2_code = wnf.lv2_code  
								 and coalesce(inf.dimension_group_code,''SNULLC'')=coalesce(wnf.dimension_group_code,''SNULLC'') 
                 and coalesce(inf.dimension_subcategory_code,''SNULLC'')=coalesce(wnf.dimension_subcategory_code,''SNULLC'') 
                 and inf.currency = wnf.currency  
								 and inf.period_id < wnf.period_id  
                 and  inf.'|| field ||'  is  not  null 
							)a 
							where  a.rn=1  ' ;
              
              IF field IN ('unit_cost','unit_price','carryover_rate') THEN
                  -- 有量刚，均本均价 结转率填充
                  dimens_upd := '
                  update   dm_fop_dimension_tgt_period_tmp  tmp    
                  SET tmp.'||field|| '=upd.'||field|| '
                  from  dm_fop_dimension_upd_tmp upd 
                  where   '||dimens_fix_cd||'
                  and   tmp.'|| field ||'  is     null 
                  and   tmp.version_code = upd.version_code  
                  and  tmp.scenarios = upd.scenarios  
                  and tmp.time_window_code = upd.time_window_code 
                  and tmp.bg_code=upd.bg_code  
                  and tmp.lv1_code = upd.lv1_code  
                  and tmp.oversea_code = upd.oversea_code  
                  and tmp.lv2_code = upd.lv2_code  
                  and coalesce(tmp.dimension_group_code,''SNULLC'')=coalesce(upd.dimension_group_code,''SNULLC'') 
                  and coalesce(tmp.dimension_subcategory_code,''SNULLC'')=coalesce(upd.dimension_subcategory_code,''SNULLC'') 
                  and tmp.currency = upd.currency  
                  and tmp.period_id = upd.period_id ' ;
                  
                  
                  EXECUTE dimens_query;
                  EXECUTE dimens_upd;
                  drop table if exists dm_fop_dimension_upd_tmp;
              END IF;
              
              IF field IN ('rev_percent') THEN
                  -- 无量刚，占比填充
                  dimens_upd := '
                  update   dm_fop_dimension_tgt_period_tmp  tmp    
                  SET tmp.'||field|| '='||0|| '
                  from  dm_fop_dimension_upd_tmp upd 
                  where   '||undimens_fix_cd||'
                  and   tmp.'|| field ||'  is     null 
                  and   tmp.version_code = upd.version_code  
                  and  tmp.scenarios = upd.scenarios  
                  and tmp.time_window_code = upd.time_window_code 
                  and tmp.bg_code=upd.bg_code  
                  and tmp.lv1_code = upd.lv1_code  
                  and tmp.oversea_code = upd.oversea_code  
                  and tmp.lv2_code = upd.lv2_code  
                  and coalesce(tmp.dimension_group_code,''SNULLC'')=coalesce(upd.dimension_group_code,''SNULLC'') 
                  and coalesce(tmp.dimension_subcategory_code,''SNULLC'')=coalesce(upd.dimension_subcategory_code,''SNULLC'') 
                  and tmp.currency = upd.currency  
                  and tmp.period_id = upd.period_id ' ;
                  
                  
                  EXECUTE dimens_query;
                  EXECUTE dimens_upd;
                  drop table if exists dm_fop_dimension_upd_tmp;
              END IF;
              
              IF field IN ('rev_percent') THEN
                  -- 无量刚，LV2的制毛率填充
                  dimens_upd := '
                  update   dm_fop_dimension_tgt_period_tmp  tmp    
                  SET tmp.'||field|| '=upd.'||field|| '
                  from  dm_fop_dimension_upd_tmp upd 
                  where   '||undimens_fix_cd||'
                  and   tmp.'|| field ||'  is     null 
                  and   tmp.version_code = upd.version_code  
                  and  tmp.scenarios = upd.scenarios  
                  and tmp.time_window_code = upd.time_window_code 
                  and tmp.bg_code=upd.bg_code  
                  and tmp.lv1_code = upd.lv1_code  
                  and tmp.oversea_code = upd.oversea_code  
                  and tmp.lv2_code = upd.lv2_code  
                  and coalesce(tmp.dimension_group_code,''SNULLC'')=coalesce(upd.dimension_group_code,''SNULLC'') 
                  and coalesce(tmp.dimension_subcategory_code,''SNULLC'')=coalesce(upd.dimension_subcategory_code,''SNULLC'') 
                  and tmp.currency = upd.currency  
                  and tmp.period_id = upd.period_id ' ;
                  
                  lv2_upd := '
                  update   dm_fop_dimension_tgt_period_tmp  tmp    
                  SET tmp.'||field|| '=upd.'||field|| '
                  from  dm_fop_dimension_upd_tmp upd 
                  where   '||lv2_fix_cd||'
                  and   tmp.'|| field ||'  is     null 
                  and   tmp.version_code = upd.version_code  
                  and  tmp.scenarios = upd.scenarios  
                  and tmp.time_window_code = upd.time_window_code 
                  and tmp.bg_code=upd.bg_code  
                  and tmp.lv1_code = upd.lv1_code  
                  and tmp.oversea_code = upd.oversea_code  
                  and tmp.lv2_code = upd.lv2_code  
                  and coalesce(tmp.dimension_group_code,''SNULLC'')=coalesce(upd.dimension_group_code,''SNULLC'') 
                  and coalesce(tmp.dimension_subcategory_code,''SNULLC'')=coalesce(upd.dimension_subcategory_code,''SNULLC'') 
                  and tmp.currency = upd.currency  
                  and tmp.period_id = upd.period_id ' ;
                  
                  
                  EXECUTE dimens_query;
                  EXECUTE dimens_upd;
                  EXECUTE lv2_upd;
                  drop table if exists dm_fop_dimension_upd_tmp;
              END IF;

							
							
							

			-- 执行动态 SQL
-- 			EXECUTE dimens_query;
-- 			EXECUTE dimens_upd;
			END LOOP;
			END;
       
            
   
        
        -- 这里不在计算上一年的均值了
--     drop table if exists max_period_rate_ytd_data_tmp;
-- 	    create temporary table  max_period_rate_ytd_data_tmp  -- 3124
-- 		  as  
--         select  
--         'YTD'  as   data_flag,version_code, scenarios, time_window_code, left(period_id,4) as y_code,   bg_code,   oversea_code,   lv1_code,   lv2_code,   dimension_group_code,  dimension_subcategory_code,  currency,  
--          avg(coalesce(unit_cost,0)) as  unit_cost_ytd_data, avg(coalesce(unit_price,0)) as  unit_price_ytd_data,avg(coalesce(carryover_rate,0)) as  carryover_rate_ytd_data,avg(coalesce(mgp_ratio,0)) as  mgp_ratio_ytd_data ,avg(coalesce(rev_percent,0)) as  rev_percent_ytd_data   ,  ',计算5个率指标的最大期次的上一年的均值'  remark
--         from   dm_fop_dimension_all_item_process_tmp  
--         where  right(target_period,3)='YTD'
--         and   left(period_id,4) = v_max_ytd
--         group by  version_code, scenarios, time_window_code, left(period_id,4)  ,   bg_code,  oversea_code, lv1_code,  lv2_code,   dimension_group_code, dimension_subcategory_code,   currency ;
-- 
     
--       select   *   from     fin_dm_opt_fop.dm_fop_dimension_tgt_period_t ;
      -- 更新插入TGT接口表 select   *  from   fin_dm_opt_fop.dm_fop_dimension_tgt_period_t ;
      truncate  table   fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t ;
      insert  into fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t (
      version_code	                  -- 版本编码
      ,scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,time_window_code	              -- 统计时间窗          
      ,period_id	                      -- 会计期
      ,target_period	                  -- 目标时点 无 我来计算的
      ,bg_code	                          -- BG编码
      ,bg_name	                          -- BG名称
      ,oversea_code	                  -- 区域编码
      ,oversea_desc	                  -- 区域
      ,lv1_code	                      -- 重量级团队LV1编码
      ,lv1_name	                      -- 重量级团队LV1描述
      ,lv2_code	                      -- 重量级团队LV2编码
      ,lv2_name	                      -- 重量级团队LV2名称
      ,dimension_group_code   	          -- 量纲分组编码            
      ,dimension_group_cn_name        	  -- 量纲分组中文名                
      ,dimension_group_en_name	          -- 量纲分组英文名称        
      ,dimension_subcategory_code    	  -- 量纲子类编码               
      ,dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      ,currency	                      -- 币种
      ,equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,unit_cost	                      -- 单位成本  dm_fop_dimension_unit_cost_price_sum_t 有数据   cost_unit_price
      ,unit_price	                      -- 单位价格  dm_fop_dimension_unit_cost_price_sum_t 有数据   rev_unit_price
      ,rev_percent	                      -- 收入占比 
      ,carryover_rate	                  -- 结转率   dm_fop_dimension_carryover_sum_t
      ,mgp_ratio	                      -- 制毛率
--       ,unit_cost_ytd_data	                      -- 单位成本  dm_fop_dimension_unit_cost_price_sum_t 有数据   cost_unit_price
--       ,unit_price_ytd_data                      -- 单位价格  dm_fop_dimension_unit_cost_price_sum_t 有数据   rev_unit_price
--       ,rev_percent_ytd_data	                      -- 收入占比 
--       ,carryover_rate_ytd_data	                  -- 结转率   dm_fop_dimension_carryover_sum_t
--       ,mgp_ratio_ytd_data	                      -- 制毛率
      ,remark	                          -- 备注
      ,created_by	                      -- 创建人
      ,creation_date	                  -- 创建时间
      ,last_updated_by	                  -- 修改人
      ,last_update_date	              -- 修改时间
      ,del_flag	                      -- 是否删除
      )
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.dimension_group_code   	          -- 量纲分组编码            
      ,base.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,base.dimension_group_en_name	          -- 量纲分组英文名称        
      ,base.dimension_subcategory_code    	  -- 量纲子类编码               
      ,base.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,base.dimension_subcategory_en_name 	  -- 量纲子类英文名称 
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.ship_qty	                      -- 发货量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.rev_qty	                          -- 收入量（历史） dm_fop_dimension_cost_price_sum_t 无数据
      ,base.unit_cost -- 单位成本 设备成本/收入量
      ,base.unit_price  -- 单位价格 设备收入/收入量
      ,base.rev_percent -- 收入占比待更新 量纲的收入/LV2的收入
      ,base.carryover_rate -- 结转率  收入量/发货量
      ,base.mgp_ratio
--       ,max_ytd.unit_cost_ytd_data -- 单位成本 设备成本/收入量
--       ,max_ytd.unit_price_ytd_data  -- 单位价格 设备收入/收入量
--       ,max_ytd.rev_percent_ytd_data -- 收入占比待更新 量纲的收入/LV2的收入
--       ,max_ytd.carryover_rate_ytd_data -- 结转率  收入量/发货量
--       ,max_ytd.mgp_ratio_ytd_data
--       ,base.remark||max_ytd.remark
      ,base.remark
      ,'-1'  as created_by
      ,now()  as creation_date
      ,'-1'  as  last_updated_by
      ,now()  as  last_update_date
      ,'N'  as  del_flag  
      from   dm_fop_dimension_tgt_period_tmp  base 
--       left join max_period_rate_ytd_data_tmp  max_ytd
--       on   base.version_code = max_ytd.version_code
--       and  base.scenarios = max_ytd.scenarios
--       and  base.time_window_code = max_ytd.time_window_code
--       and  base.bg_code = max_ytd.bg_code
--       and  base.oversea_code = max_ytd.oversea_code
--       and  base.lv1_code = max_ytd.lv1_code
--       and  base.lv2_code = max_ytd.lv2_code
--       and  coalesce(base.dimension_group_code,'SNULL') = coalesce(max_ytd.dimension_group_code,'SNULL') 
--       and  coalesce(base.dimension_subcategory_code,'SNULL')  = coalesce(max_ytd.dimension_subcategory_code,'SNULL') 
--       and  base.currency = max_ytd.currency  and  right(base.target_period,3) = max_ytd.data_flag
      
      ;
			
      -- 	 --1.开始日志
v_dml_row_count := sql%rowcount;  -- 收集数据量
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 7,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',插入接口表',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
 

--处理异常信息
-- 	exception
-- 		when others then
-- 		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
-- 			p_log_sp_name => v_sp_name,    --sp名称
-- 			p_log_step_num  => null,
-- 			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
-- 			) ;
-- 	x_success_flag := '2001';
	
	
		
 end;
 $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100 ;