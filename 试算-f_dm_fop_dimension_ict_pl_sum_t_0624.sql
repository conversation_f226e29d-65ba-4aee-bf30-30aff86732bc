CREATE OR REPLACE FUNCTION fin_dm_opt_fop.f_dm_fop_dimension_ict_pl_sum_t(p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS pg_catalog.text AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：盈利量纲ICT损益汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_ict_pl_sum_t();
*/
 
 declare
	v_sp_name varchar(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_ict_pl_sum_t('''||p_version_code||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t';
	v_version_code varchar(50);  -- 目标表的最大版本编码，格式：当前年月_V1...VN
	v_step_num   bigint; --步骤号
	v_dml_row_count  number default 0 ;


begin
	x_success_flag := '1';                                 --1表示成功
	

  
        -- 如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select to_char(current_date,'yyyymm') as version_code into v_version_code ;	
        end if 
        ;	
		
		 --1.开始日志
  v_step_num := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '盈利量纲ICT损益汇总表'||v_tbl_name||',目标表中'||'版本编码:'||v_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
 
	  
	   drop table if exists ict_pl_tmp;
	   create temporary table  ict_pl_tmp
		          as
	   select  t1.period_id 										    -- 会计期
		      ,sum(case when t8.report_item_l1_code ='PS_PL_91000'
			             and t8.report_item_l2_code ='PS_PL_91050'
					    then t1.rmb_fact_ex_rate_ptd_amt 
					     end) as equip_rev_rmb_amt    -- 设备收入人民币金额   
              ,sum(case when t8.report_item_l1_code ='PS_PL_91000' 
			             and t8.report_item_l2_code ='PS_PL_91050'
					    then t1.usd_fact_ex_rate_ptd_amt 
					    end) as equip_rev_usd_amt     -- 设备收入美元金额     
              ,sum(case when t8.report_item_l1_code ='PS_PL_91200'
			             and t8.report_item_l2_code ='PS_PL_91250'
					    then t1.rmb_fact_ex_rate_ptd_amt 
					    end) as equip_cost_rmb_amt    -- 设备成本人民币金额   
              ,sum(case when t8.report_item_l1_code ='PS_PL_91200'
			             and t8.report_item_l2_code ='PS_PL_91250'
					    then t1.usd_fact_ex_rate_ptd_amt 
					    end) as equip_cost_usd_amt    -- 设备成本美元金额     
		      ,t3.lv0_prod_list_code    as bg_code        			    -- BG编码
		      ,t3.lv0_prod_list_cn_name as bg_name     			        -- BG中文描述
		      ,t3.lv0_prod_list_en_name as bg_en_name                   -- BG英文描述
		      ,t3.lv0_prod_rnd_team_code 	            			    -- 重量级团队LV0编码
		      ,t3.lv0_prod_rd_team_cn_name 	        			        -- 重量级团队LV0中文描述
		      ,t3.lv0_prod_rd_team_en_name 	        			        -- 重量级团队LV0英文描述
		      ,t3.lv1_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv1_prod_rd_team_cn_name 	        			        -- 重量级团队LV1中文描述
		      ,t3.lv1_prod_rd_team_en_name 	        			        -- 重量级团队LV1英文描述
		      ,t3.lv2_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv2_prod_rd_team_cn_name 	        			        -- 重量级团队LV2中文描述
		      ,t3.lv2_prod_rd_team_en_name 	        			        -- 重量级团队LV2中文描述
		      ,t4.domestic_or_oversea_code                              --ICT国内或海外编码     
			  ,t4.domestic_or_oversea_cname   	                        -- ICT国内或海外中文名称 
 	    from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	-- ict损益表 
 	    left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	      on t1.major_prod_key = t3.prod_key
       and t3.del_flag = 'N'
 	    left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	      on t1.geo_pc_key = t4.geo_pc_key
 	     and t4.del_flag = 'N'
 	    join dmdim.dm_dim_data_rep_category_b t5    			        ---财经报告口径与数据口径关系维
        on t1.data_category_id = t5.data_category_id
	     and t5.report_category_id = '11204'                            ---11204: 经营双算; 11202: 经营报告
 	    join dmdim.dm_dim_report_item_level_d t8     			        ---报表项层级标准维
 	      on t1.report_item_id = t8.report_item_l5_id
 	     and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')    -- '净销售收入','销售成本'
 	     and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')    -- '设备成本','设备收入'
 	     and t8.user_group_id = 14										---user_group_cn_name='产品与解决方案'
    where t1.period_id > 202312
       and t1.period_id < (to_char(current_date,'yyyymm'))   -- 取集成表period_id<系统当前年月的数据
	   group by t1.period_id 										    -- 会计期
			  ,t3.lv0_prod_list_code                         		    -- BG编码
		      ,t3.lv0_prod_list_cn_name                      	        -- BG中文描述
		      ,t3.lv0_prod_list_en_name                                 -- BG英文描述
		      ,t3.lv0_prod_rnd_team_code 	            			    -- 重量级团队LV0编码
		      ,t3.lv0_prod_rd_team_cn_name 	        			        -- 重量级团队LV0中文描述
		      ,t3.lv0_prod_rd_team_en_name 	        			        -- 重量级团队LV0英文描述
		      ,t3.lv1_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv1_prod_rd_team_cn_name 	        			        -- 重量级团队LV1中文描述
		      ,t3.lv1_prod_rd_team_en_name 	        			        -- 重量级团队LV1英文描述
		      ,t3.lv2_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv2_prod_rd_team_cn_name 	        			        -- 重量级团队LV2中文描述
		      ,t3.lv2_prod_rd_team_en_name 	        			        -- 重量级团队LV2中文描述
			  ,t4.domestic_or_oversea_code 
              ,t4.domestic_or_oversea_cname
			   union all
			   select t1.period_id										       -- 会计期
		       ,sum(case when t8.report_item_l1_code ='PS_PL_91000'
			             and t8.report_item_l2_code ='PS_PL_91050'
					    then t1.rmb_fact_ex_rate_ptd_amt 
					     end) as equip_rev_rmb_amt                             -- 设备收入人民币金额   
              ,sum(case when t8.report_item_l1_code ='PS_PL_91000' 
			             and t8.report_item_l2_code ='PS_PL_91050'
					    then t1.usd_fact_ex_rate_ptd_amt 
					    end) as equip_rev_usd_amt                              -- 设备收入美元金额     
              ,sum(case when t8.report_item_l1_code ='PS_PL_91200'
			             and t8.report_item_l2_code ='PS_PL_91250'
					    then t1.rmb_fact_ex_rate_ptd_amt 
					    end) as equip_cost_rmb_amt                             -- 设备成本人民币金额   
              ,sum(case when t8.report_item_l1_code ='PS_PL_91200'
			             and t8.report_item_l2_code ='PS_PL_91250'
					    then t1.usd_fact_ex_rate_ptd_amt 
					    end) as equip_cost_usd_amt              -- 设备成本美元金额  
		       ,t3.lv0_prod_list_code as bg_code       			-- BG编码
		       ,t3.lv0_prod_list_cn_name as bg_name    			-- BG中文描述
		       ,t3.lv0_prod_list_en_name as bg_en_name          -- BG英文描述
		       ,t3.lv0_prod_rnd_team_code	            		-- 重量级团队LV0编码
		       ,t3.lv0_prod_rd_team_cn_name	        		    -- 重量级团队LV0中文描述
		       ,t3.lv0_prod_rd_team_en_name        			    -- 重量级团队LV0英文描述
		       ,t3.lv1_prod_rnd_team_code	            		-- 重量级团队LV1编码
		       ,t3.lv1_prod_rd_team_cn_name	        			-- 重量级团队LV1中文描述
		       ,t3.lv1_prod_rd_team_en_name	        			-- 重量级团队LV1英文描述
		       ,t3.lv2_prod_rnd_team_code	            		-- 重量级团队LV1编码
		       ,t3.lv2_prod_rd_team_cn_name        			    -- 重量级团队LV2中文描述
		       ,t3.lv2_prod_rd_team_en_name	        			-- 重量级团队LV2中文描述
		       ,t4.domestic_or_oversea_code                     --ICT国内或海外编码     
			   ,t4.domestic_or_oversea_cname   	                -- ICT国内或海外中文名称 		     
 	    from fin_dm_opt_fop.fop_mr_dm_ps_for_opt_pl_dtl_v t1		 	    -- ict损益（202305月版切的新表）
 	    left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	      on t1.major_prod_key = t3.prod_key
       and t3.del_flag = 'N'
 	    left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	      on t1.geo_pc_key = t4.geo_pc_key
 	     and t4.del_flag = 'N'
 	    join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
        on t1.data_category_id = t5.data_category_id
	     and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	    join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	      on t1.report_item_id = t8.report_item_l5_id
 	     and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	     and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	     and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
     where t1.period_id between 202301 and 202312
       and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'  -- 与上游沟通，新表不用排除
	    group by t1.period_id 										    -- 会计期
			  ,t3.lv0_prod_list_code                         		    -- BG编码
		      ,t3.lv0_prod_list_cn_name                      	        -- BG中文描述
		      ,t3.lv0_prod_list_en_name                                 -- BG英文描述
		      ,t3.lv0_prod_rnd_team_code 	            			    -- 重量级团队LV0编码
		      ,t3.lv0_prod_rd_team_cn_name 	        			        -- 重量级团队LV0中文描述
		      ,t3.lv0_prod_rd_team_en_name 	        			        -- 重量级团队LV0英文描述
		      ,t3.lv1_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv1_prod_rd_team_cn_name 	        			        -- 重量级团队LV1中文描述
		      ,t3.lv1_prod_rd_team_en_name 	        			        -- 重量级团队LV1英文描述
		      ,t3.lv2_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv2_prod_rd_team_cn_name 	        			        -- 重量级团队LV2中文描述
		      ,t3.lv2_prod_rd_team_en_name 	        			        -- 重量级团队LV2中文描述
			  ,t4.domestic_or_oversea_code 
              ,t4.domestic_or_oversea_cname			  
	       union all
	    select t1.period_id										            -- 会计期
		      ,sum(case when t8.report_item_l1_code ='PS_PL_91000'
			             and t8.report_item_l2_code ='PS_PL_91050'
					    then t1.rmb_fact_ex_rate_ptd_amt 
					     end) as equip_rev_rmb_amt                             -- 设备收入人民币金额   
              ,sum(case when t8.report_item_l1_code ='PS_PL_91000' 
			             and t8.report_item_l2_code ='PS_PL_91050'
					    then t1.usd_fact_ex_rate_ptd_amt 
					    end) as equip_rev_usd_amt                              -- 设备收入美元金额     
              ,sum(case when t8.report_item_l1_code ='PS_PL_91200'
			             and t8.report_item_l2_code ='PS_PL_91250'
					    then t1.rmb_fact_ex_rate_ptd_amt 
					    end) as equip_cost_rmb_amt                             -- 设备成本人民币金额   
              ,sum(case when t8.report_item_l1_code ='PS_PL_91200'
			             and t8.report_item_l2_code ='PS_PL_91250'
					    then t1.usd_fact_ex_rate_ptd_amt 
					    end) as equip_cost_usd_amt              -- 设备成本美元金额  
		       ,t3.lv0_prod_list_code as bg_code       			-- BG编码
		       ,t3.lv0_prod_list_cn_name as bg_name   			-- BG中文描述
		       ,t3.lv0_prod_list_en_name as bg_en_name     -- BG英文描述 
		       ,t3.lv0_prod_rnd_team_code	            			-- 重量级团队LV0编码
		       ,t3.lv0_prod_rd_team_cn_name	        			  -- 重量级团队LV0中文描述
		       ,t3.lv0_prod_rd_team_en_name	        			  -- 重量级团队LV0英文描述
		       ,t3.lv1_prod_rnd_team_code	            			-- 重量级团队LV1编码
		       ,t3.lv1_prod_rd_team_cn_name	        			  -- 重量级团队LV1中文描述
		       ,t3.lv1_prod_rd_team_en_name	        			  -- 重量级团队LV1英文描述
		       ,t3.lv2_prod_rnd_team_code	            			-- 重量级团队LV1编码
		       ,t3.lv2_prod_rd_team_cn_name	        			  -- 重量级团队LV2中文描述
		       ,t3.lv2_prod_rd_team_en_name	        			  -- 重量级团队LV2中文描述
		       ,t4.domestic_or_oversea_code                     --ICT国内或海外编码     
			   ,t4.domestic_or_oversea_cname   	                -- ICT国内或海外中文名称
 	    from fin_dm_opt_fop.fop_dwr_fin_rpt_item_ps_fv t1		 	    -- ict损益
 	    left join dmdim.dm_dim_product_d t3               			    ---产品维表
 	      on t1.major_prod_key = t3.prod_key
       and t3.del_flag = 'N'
 	    left join dmdim.dm_dim_region_rc_d t4             			    ---区域维表
 	      on t1.geo_pc_key = t4.geo_pc_key
 	     and t4.del_flag = 'N'
 	    join dmdim.dm_dim_data_rep_category_b t5    			    ---财经报告口径与数据口径关系维
        on t1.data_category_id = t5.data_category_id
	     and t5.report_category_id = '11204'                  ---11204: 经营双算; 11202: 经营报告
 	    join dmdim.dm_dim_report_item_level_d t8     			    ---报表项层级标准维
 	      on t1.report_item_id = t8.report_item_l5_id
 	     and t8.report_item_l1_code in ('PS_PL_91000','PS_PL_91200')  -- '净销售收入','销售成本'
 	     and t8.report_item_l2_code in ('PS_PL_91050','PS_PL_91250')  -- '设备成本','设备收入'
 	     and t8.user_group_id = 14														---user_group_cn_name='产品与解决方案'
     where t1.period_id <= 202212    -- 20230426 update by qwx1110218 2023年5月版切换新表，新表从202301开始取数 
       and t3.lv0_prod_list_code not in ('PDCG805902', 'PDCG802558')  ---BG去掉'数字能源'和'云原生公有云客户'
	    group by t1.period_id 										    -- 会计期
			  ,t3.lv0_prod_list_code                         		    -- BG编码
		      ,t3.lv0_prod_list_cn_name                      	        -- BG中文描述
		      ,t3.lv0_prod_list_en_name                                 -- BG英文描述
		      ,t3.lv0_prod_rnd_team_code 	            			    -- 重量级团队LV0编码
		      ,t3.lv0_prod_rd_team_cn_name 	        			        -- 重量级团队LV0中文描述
		      ,t3.lv0_prod_rd_team_en_name 	        			        -- 重量级团队LV0英文描述
		      ,t3.lv1_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv1_prod_rd_team_cn_name 	        			        -- 重量级团队LV1中文描述
		      ,t3.lv1_prod_rd_team_en_name 	        			        -- 重量级团队LV1英文描述
		      ,t3.lv2_prod_rnd_team_code 	            			    -- 重量级团队LV1编码
		      ,t3.lv2_prod_rd_team_cn_name 	        			        -- 重量级团队LV2中文描述
		      ,t3.lv2_prod_rd_team_en_name 	        			        -- 重量级团队LV2中文描述
			  ,t4.domestic_or_oversea_code 
              ,t4.domestic_or_oversea_cname
			  ;
	
	        -- 币种列转行
			drop table if exists currency_tmp;
	        create temporary table  currency_tmp
		          as
			 select    period_id 										     -- 会计期
		      , 'CNY' as currency_code                               -- 币种
		      , equip_rev_rmb_amt   as equip_rev_cons_after_amt      -- 设备收入(对价后)    
              , equip_cost_rmb_amt  as equip_cost_cons_after_amt     -- 设备成本(对价后)     
		      , bg_code        	                                     -- BG编码
		      , bg_name     			                             -- BG中文描述
		      , bg_en_name                                           -- BG英文描述
		      , lv0_prod_rnd_team_code 	            			     -- 重量级团队LV0编码
		      , lv0_prod_rd_team_cn_name 	        			     -- 重量级团队LV0中文描述
		      , lv0_prod_rd_team_en_name 	        			     -- 重量级团队LV0英文描述
		      , lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
		      , lv2_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv2_prod_rd_team_cn_name 	        			     -- 重量级团队LV2中文描述
		      , lv2_prod_rd_team_en_name 	        			     -- 重量级团队LV2中文描述
		      , domestic_or_oversea_code                              --ICT国内或海外编码     
			  , domestic_or_oversea_cname   	                        -- ICT国内或海外中文名称 
 	    from ict_pl_tmp	 
        where domestic_or_oversea_code in ('GH0002','GH0003')	-- 只取 中国区 和 海外的数据，剔除其他的数据	
		  and bg_code in ('PDCG901159','PDCG901160')
          UNION ALL		  
	  select   period_id 										     -- 会计期
		      , 'USD' as currency_code                               -- 币种 
              , equip_rev_usd_amt   as equip_rev_cons_after_amt      -- 设备收入(对价后)       
              , equip_cost_usd_amt  as equip_cost_cons_after_amt     -- 设备成本(对价后)    
		      , bg_code        	                                     -- BG编码
		      , bg_name     			                             -- BG中文描述
		      , bg_en_name                                           -- BG英文描述
		      , lv0_prod_rnd_team_code 	            			     -- 重量级团队LV0编码
		      , lv0_prod_rd_team_cn_name 	        			     -- 重量级团队LV0中文描述
		      , lv0_prod_rd_team_en_name 	        			     -- 重量级团队LV0英文描述
		      , lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
		      , lv2_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv2_prod_rd_team_cn_name 	        			     -- 重量级团队LV2中文描述
		      , lv2_prod_rd_team_en_name 	        			     -- 重量级团队LV2中文描述
		      , domestic_or_oversea_code                              --ICT国内或海外编码     
			  , domestic_or_oversea_cname   	                      -- ICT国内或海外中文名称 
 	    from ict_pl_tmp	 
		where domestic_or_oversea_code in ('GH0002','GH0003')	-- 只取 中国区 和 海外的数据，剔除其他的数据
          and bg_code in ('PDCG901159','PDCG901160')		
              ;	

               
			   drop table if exists oversea_tmp;
	           create temporary table  oversea_tmp
		          as
				  -- 汇总到全球
			    select    period_id 								       -- 会计期
		      , currency_code                                              -- 币种
		      , sum(equip_rev_cons_after_amt)  as equip_rev_cons_after_amt  -- 设备收入(对价后)     
              , sum(equip_cost_cons_after_amt) as equip_cost_cons_after_amt -- 设备成本(对价后)      
		      , bg_code        	                                     -- BG编码
		      , bg_name     			                             -- BG中文描述
		      , lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
		      , lv2_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv2_prod_rd_team_cn_name 	        			     -- 重量级团队LV2中文描述
		      , lv2_prod_rd_team_en_name 	        			     -- 重量级团队LV2中文描述
		      , 'GH0001' as domestic_or_oversea_code                 --ICT国内或海外编码     
			  , '全球' as domestic_or_oversea_cname   	             -- ICT国内或海外中文名称 
 	          from currency_tmp	             
			  group by period_id 								
		      , currency_code                                   
		      , bg_code        	                                
		      , bg_name     			                                                                   			
		      , lv1_prod_rnd_team_code 	            			
		      , lv1_prod_rd_team_cn_name 	        			
		      , lv1_prod_rd_team_en_name 	        			
		      , lv2_prod_rnd_team_code 	            			
		      , lv2_prod_rd_team_cn_name 	        			
		      , lv2_prod_rd_team_en_name 	
                union all	
				-- 插入国内海外的数据
              select     period_id 										     -- 会计期
		      , currency_code                                        -- 币种 
              , equip_rev_cons_after_amt                             -- 设备收入(对价后)       
              , equip_cost_cons_after_amt                            -- 设备成本(对价后)    
		      , bg_code        	                                     -- BG编码
		      , bg_name     			                             -- BG中文描述                                         -- BG英文描述
		      , lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
		      , lv2_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv2_prod_rd_team_cn_name 	        			     -- 重量级团队LV2中文描述
		      , lv2_prod_rd_team_en_name 	        			     -- 重量级团队LV2中文描述
		      , domestic_or_oversea_code                              --ICT国内或海外编码     
			  , domestic_or_oversea_cname   	                      -- ICT国内或海外中文名称 
 	          from currency_tmp	             			  	  
              ;
			  
			  -- 汇总到ICT
               drop table if exists bg_tmp;
	           create temporary table  bg_tmp
		          as
			    select    period_id 								        -- 会计期
		      , currency_code                                               -- 币种
		      , sum(equip_rev_cons_after_amt)  as equip_rev_cons_after_amt  -- 设备收入(对价后)     
              , sum(equip_cost_cons_after_amt) as equip_cost_cons_after_amt -- 设备成本(对价后)      
		      , 'RICT001' as bg_code        	                                    -- BG编码
		      , 'ICT' as bg_name     			                                -- BG中文描述
		      , lv1_prod_rnd_team_code 	            			            -- 重量级团队LV1编码
		      , lv1_prod_rd_team_cn_name 	        			            -- 重量级团队LV1中文描述
		      , lv1_prod_rd_team_en_name 	        			            -- 重量级团队LV1英文描述
		      , lv2_prod_rnd_team_code 	            			            -- 重量级团队LV1编码
		      , lv2_prod_rd_team_cn_name 	        			            -- 重量级团队LV2中文描述
		      , lv2_prod_rd_team_en_name 	        			            -- 重量级团队LV2中文描述
		      , domestic_or_oversea_code                                    -- ICT国内或海外编码     
			  , domestic_or_oversea_cname   	                            -- ICT国内或海外中文名称 
 	          from oversea_tmp	             
			  group by period_id 								
		      , currency_code                                     			                                                                   			
		      , lv1_prod_rnd_team_code 	            			
		      , lv1_prod_rd_team_cn_name 	        			
		      , lv1_prod_rd_team_en_name 	        			
		      , lv2_prod_rnd_team_code 	            			
		      , lv2_prod_rd_team_cn_name 	        			
		      , lv2_prod_rd_team_en_name 	
              , domestic_or_oversea_code                                 
			  , domestic_or_oversea_cname  
                union all
               select    period_id 								        -- 会计期
		      , currency_code                                               -- 币种
		      , equip_rev_cons_after_amt  -- 设备收入(对价后)     
              , equip_cost_cons_after_amt -- 设备成本(对价后)      
		      , bg_code        	                                    -- BG编码
		      , bg_name     			                                -- BG中文描述
		      , lv1_prod_rnd_team_code 	            			            -- 重量级团队LV1编码
		      , lv1_prod_rd_team_cn_name 	        			            -- 重量级团队LV1中文描述
		      , lv1_prod_rd_team_en_name 	        			            -- 重量级团队LV1英文描述
		      , lv2_prod_rnd_team_code 	            			            -- 重量级团队LV1编码
		      , lv2_prod_rd_team_cn_name 	        			            -- 重量级团队LV2中文描述
		      , lv2_prod_rd_team_en_name 	        			            -- 重量级团队LV2中文描述
		      , domestic_or_oversea_code                                    -- ICT国内或海外编码     
			  , domestic_or_oversea_cname   	                            -- ICT国内或海外中文名称 
 	          from oversea_tmp	             			
              ;                                                          
			  

            -- 所有时间维度
            drop table if exists all_time_tmp;
	        create temporary table  all_time_tmp
		          as           
              select to_char(generate_series,'YYYYMM')  as period_id
			        ,to_char(generate_series,'YYYY')    as year
              from generate_series('2020-01-01':: timestamp ,CURRENT_DATE::timestamp, '1 month')
			  where to_char(generate_series,'YYYYMM')<= (select max(period_id)  from ict_pl_tmp  )
             ;		  
			 
			 
			 
			 -- 所有时间关联每年的所有维度
			 drop table if exists all_dim_tmp;
	         create temporary table  all_dim_tmp
		          as
			  select distinct t1.period_id                -- 会计期
			  , t1.year                          -- 年份
		      , t2.currency_code                 -- 币种   
		      , t2.bg_code        	             -- BG编码
		      , t2.bg_name     			         -- BG中文描述
		      , t2.lv1_prod_rnd_team_code 	     -- 重量级团队LV1编码
		      , t2.lv1_prod_rd_team_cn_name      -- 重量级团队LV1中文描述
		      , t2.lv1_prod_rd_team_en_name      -- 重量级团队LV1英文描述
		      , t2.lv2_prod_rnd_team_code 	     -- 重量级团队LV1编码
		      , t2.lv2_prod_rd_team_cn_name      -- 重量级团队LV2中文描述
		      , t2.lv2_prod_rd_team_en_name      -- 重量级团队LV2中文描述
		      , t2.domestic_or_oversea_code      --ICT国内或海外编码     
			  , t2.domestic_or_oversea_cname     -- ICT国内或海外中文名称 
 	          from all_time_tmp t1
			  left join bg_tmp t2
 			  on t1.year= substr(t2.period_id,1,4)
			  ;
			  
			  -- 计算LV2层级的YTD
			  drop table if exists lv2_ytd_tmp;
	         create temporary table  lv2_ytd_tmp
		          as
			  select t1.period_id 									     -- 会计期
			  , t1.year                                                  -- 年份
		      , t1.currency_code                                         -- 币种    
		      , t1.bg_code        	                                     -- BG编码
		      , t1.bg_name     			                                 -- BG中文描述
		      , t1.lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , t1.lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , t1.lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
		      , t1.lv2_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , t1.lv2_prod_rd_team_cn_name 	        			     -- 重量级团队LV2中文描述
		      , t1.lv2_prod_rd_team_en_name 	        			     -- 重量级团队LV2中文描述
		      , t1.domestic_or_oversea_code                              --ICT国内或海外编码     
			  , t1.domestic_or_oversea_cname   	                         -- ICT国内或海外中文名称 
			  ,sum(COALESCE(t2.equip_rev_cons_after_amt,0)) over(partition by t1.year                                                  
		                                                        , t1.currency_code                                        
		                                                        , t1.bg_code        	                                    
		                                                        , t1.bg_name     			                                                                         
		                                                        , t1.lv1_prod_rnd_team_code 	            			    
		                                                        , t1.lv1_prod_rd_team_cn_name 	        			    
		                                                        , t1.lv1_prod_rd_team_en_name 	        			    
		                                                        , t1.lv2_prod_rnd_team_code 	            			    
		                                                        , t1.lv2_prod_rd_team_cn_name 	        			    
		                                                        , t1.lv2_prod_rd_team_en_name 	        			    
		                                                        , t1.domestic_or_oversea_code                             
			                                                    , t1.domestic_or_oversea_cname   	                        
                                                           order by t1.period_id
														   ) as equip_rev_cons_after_amt
			  ,sum(COALESCE(t2.equip_cost_cons_after_amt,0)) over(partition by t1.year                                                  
		                                                        , t1.currency_code                                        
		                                                        , t1.bg_code        	                                    
		                                                        , t1.bg_name     			                                                                        
		                                                        , t1.lv1_prod_rnd_team_code 	            			    
		                                                        , t1.lv1_prod_rd_team_cn_name 	        			    
		                                                        , t1.lv1_prod_rd_team_en_name 	        			    
		                                                        , t1.lv2_prod_rnd_team_code 	            			    
		                                                        , t1.lv2_prod_rd_team_cn_name 	        			    
		                                                        , t1.lv2_prod_rd_team_en_name 	        			    
		                                                        , t1.domestic_or_oversea_code                             
			                                                    , t1.domestic_or_oversea_cname   	                        
                                                           order by t1.period_id
														   ) as  equip_cost_cons_after_amt                             
			  from all_dim_tmp t1
			  left join bg_tmp t2
			  on  t1.period_id =t2.period_id 				 					      
		      and COALESCE(t1.currency_code            ,'SNULL')  = COALESCE(t2.currency_code             ,'SNULL')                            
		      and COALESCE(t1.bg_code        	       ,'SNULL')  = COALESCE(t2.bg_code        	          ,'SNULL')                    
		      and COALESCE(t1.bg_name     			   ,'SNULL')  = COALESCE(t2.bg_name     			  ,'SNULL')                                      			      
		      and COALESCE(t1.lv1_prod_rnd_team_code   ,'SNULL')  = COALESCE(t2.lv1_prod_rnd_team_code 	  ,'SNULL')  			      
		      and COALESCE(t1.lv1_prod_rd_team_cn_name ,'SNULL')  = COALESCE(t2.lv1_prod_rd_team_cn_name  ,'SNULL')  			      
		      and COALESCE(t1.lv1_prod_rd_team_en_name ,'SNULL')  = COALESCE(t2.lv1_prod_rd_team_en_name  ,'SNULL')  			      
		      and COALESCE(t1.lv2_prod_rnd_team_code   ,'SNULL')  = COALESCE(t2.lv2_prod_rnd_team_code 	  ,'SNULL')  			      
		      and COALESCE(t1.lv2_prod_rd_team_cn_name ,'SNULL')  = COALESCE(t2.lv2_prod_rd_team_cn_name  ,'SNULL')  			      
		      and COALESCE(t1.lv2_prod_rd_team_en_name ,'SNULL')  = COALESCE(t2.lv2_prod_rd_team_en_name  ,'SNULL')  			      
		      and COALESCE(t1.domestic_or_oversea_code ,'SNULL')  = COALESCE(t2.domestic_or_oversea_code  ,'SNULL')                    
			  and COALESCE(t1.domestic_or_oversea_cname,'SNULL')  = COALESCE(t2.domestic_or_oversea_cname ,'SNULL')
                    ;				

             
		/*	 
		  -- 在接口层收敛至LV1，故删除这里的lv1汇总	
      		drop table if exists ytd_tmp;
	         create temporary table  ytd_tmp
		          as
			-- 汇总到LV1层级
			  select t1.period_id 									     -- 会计期
			  , t1.year                                                  -- 年份
		      , t1.currency_code                                         -- 币种    
		      , t1.bg_code        	                                     -- BG编码
		      , t1.bg_name     			                                 -- BG中文描述
		      , t1.lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , t1.lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , t1.lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
			  , null as lv2_prod_rnd_team_code 	            			 -- 重量级团队LV1编码
		      , null as lv2_prod_rd_team_cn_name 	        			 -- 重量级团队LV2中文描述
		      , null as lv2_prod_rd_team_en_name 	        			 -- 重量级团队LV2中文描述
		      , t1.domestic_or_oversea_code                              --ICT国内或海外编码     
			  , t1.domestic_or_oversea_cname   	                         -- ICT国内或海外中文名称 
			  ,sum(t1.equip_rev_cons_after_amt)  as equip_rev_cons_after_amt
			  ,sum(t1.equip_cost_cons_after_amt) as  equip_cost_cons_after_amt                            
			  from lv2_ytd_tmp t1
			  group by  t1.period_id 									     -- 会计期
			  , t1.year                                                  -- 年份
		      , t1.currency_code                                         -- 币种    
		      , t1.bg_code        	                                     -- BG编码
		      , t1.bg_name     			                                 -- BG中文描述
		      , t1.lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , t1.lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , t1.lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
		      , t1.domestic_or_oversea_code                              --ICT国内或海外编码     
			  , t1.domestic_or_oversea_cname   	                         -- ICT国内或海外中文名称 
			    union all 
				-- 插入LV2层级的YTD
			  select t1.period_id 									     -- 会计期
			  , t1.year                                                  -- 年份
		      , t1.currency_code                                         -- 币种    
		      , t1.bg_code        	                                     -- BG编码
		      , t1.bg_name     			                                 -- BG中文描述
		      , t1.lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , t1.lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , t1.lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
			  , t1.lv2_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , t1.lv2_prod_rd_team_cn_name 	        			     -- 重量级团队LV2中文描述
		      , t1.lv2_prod_rd_team_en_name 	        			     -- 重量级团队LV2中文描述
		      , t1.domestic_or_oversea_code                              --ICT国内或海外编码     
			  , t1.domestic_or_oversea_cname   	                         -- ICT国内或海外中文名称 
			  ,t1.equip_rev_cons_after_amt 
			  ,t1.equip_cost_cons_after_amt                          
			  from lv2_ytd_tmp t1
			  ;
			  
			  */
			  
	
	      -- 删除最大版本数据
      delete fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t where version_code = v_version_code;
	  
	  insert into fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t(
	   version_code
     , period_id
	 , time_window_code
     , currency_code                  
     , equip_rev_cons_after_amt
     , equip_cost_cons_after_amt
     , bg_code
     , bg_name
     , lv1_prod_rnd_team_code
     , lv1_prod_rd_team_cn_name
     , lv1_prod_rd_team_en_name
     , lv2_prod_rnd_team_code
     , lv2_prod_rd_team_cn_name
     , lv2_prod_rd_team_en_name
     , oversea_code
     , oversea_desc
     , remark
     , created_by
     , creation_date
     , last_updated_by
     , last_update_date
     , del_flag
	  )
	  select    v_version_code as version_code                       -- 版本编码
	          , period_id 										     -- 会计期
			  ,'YTD' as time_window_code
		      , currency_code                               -- 币种
		      , equip_rev_cons_after_amt  -- 设备收入人民币金额     
              , equip_cost_cons_after_amt -- 设备成本人民币金额      
		      , bg_code        	                                     -- BG编码
		      , bg_name     			                             -- BG中文描述
		      , lv1_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv1_prod_rd_team_cn_name 	        			     -- 重量级团队LV1中文描述
		      , lv1_prod_rd_team_en_name 	        			     -- 重量级团队LV1英文描述
		      , lv2_prod_rnd_team_code 	            			     -- 重量级团队LV1编码
		      , lv2_prod_rd_team_cn_name 	        			     -- 重量级团队LV2中文描述
		      , lv2_prod_rd_team_en_name 	        			     -- 重量级团队LV2中文描述
		      , domestic_or_oversea_code  as oversea_code            -- ICT国内或海外编码     
              ,case when domestic_or_oversea_code	= 'GH0002'  
		       then '国内' 
			   else domestic_or_oversea_cname
			   end as oversea_desc                                     -- ICT国内或海外中文名称
			  ,'' as remark
 	          , -1 as created_by
 	          , current_timestamp as creation_date
 	          , -1 as last_updated_by
 	          , current_timestamp as last_update_date
 	          , 'N' as del_flag
 	    from lv2_ytd_tmp	
              ;			  
 
 
 
  end;
 $BODY$
 LANGUAGE plpgsql VOLATILE
  COST 100