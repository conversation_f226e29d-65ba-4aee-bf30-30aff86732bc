-- 量纲颗粒度TGT表
-- dm_fop_dimension_tgt_period_his_t
-- dm_fop_dimension_tgt_period_t
-- dm_fop_dimension_lv2_tgt_period_his_t
-- dm_fop_dimension_lv2_tgt_period_t
-- dm_fop_dimension_tgt_period_filled_t
-- dm_fop_dimension_lv2_tgt_period_filled_t

-- dm_fop_dimension_tgt_period_his_t 量纲层级TGT数据历史表（保留多个版本）
drop table if exists fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t;
create table fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t(
version_code	 	                      varchar(100),
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
period_id	 	                          numeric,
target_period	 				                      varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
dimension_group_code   	             	varchar(100),
dimension_group_cn_name        	      varchar(100),
dimension_group_en_name	         			varchar(100),
dimension_subcategory_code    	      varchar(100),
dimension_subcategory_cn_name 	      varchar(100),
dimension_subcategory_en_name 	      varchar(100),
currency	 														varchar(50),
equip_rev_cons_before_amt	 	          numeric(38,10),
equip_cost_cons_before_amt	 	        numeric(38,10),
ship_qty	 	  								        numeric(38,10),
rev_qty	 											        numeric(38,10),
unit_cost	 										        numeric(38,10),
unit_price	 									        numeric(38,10),
rev_percent	 									        numeric(38,10),
carryover_rate	 							        numeric(38,10),
mgp_ratio	 										        numeric(38,10),
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(version_code,period_id,target_period);
 comment on table fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t                               is '量纲层级TGT数据历史表（保留多个版本）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.version_code                    is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.time_window_code                    is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.target_period                         is '目标时点';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.oversea_desc                    is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.dimension_group_code            is '量纲分组编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.dimension_group_cn_name         is '量纲分组中文名';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.dimension_group_en_name         is '量纲分组英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.dimension_subcategory_code      is '量纲子类编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.dimension_subcategory_cn_name   is '量纲子类中文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.dimension_subcategory_en_name   is '量纲子类英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.equip_rev_cons_before_amt        is '设备收入额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.equip_cost_cons_before_amt       is '设备成本额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.ship_qty                        is '发货量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.rev_qty                         is '收入量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.unit_cost                       is '单位成本';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.unit_price          						 is '单位价格';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.rev_percent        						 is '收入占比';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.carryover_rate        					 is '结转率';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.mgp_ratio          						 is '制毛率';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_his_t.del_flag                        is '是否删除';

-- dm_fop_dimension_tgt_period_t 量纲层级TGT数据接口表（只有一个版本）
drop table if exists fin_dm_opt_fop.dm_fop_dimension_tgt_period_t;
create table fin_dm_opt_fop.dm_fop_dimension_tgt_period_t(
version_code	 	                      varchar(100),
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
period_id	 	                          numeric,
target_period	 				                      varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
dimension_group_code   	             	varchar(100),
dimension_group_cn_name        	      varchar(100),
dimension_group_en_name	         			varchar(100),
dimension_subcategory_code    	      varchar(100),
dimension_subcategory_cn_name 	      varchar(100),
dimension_subcategory_en_name 	      varchar(100),
currency	 														varchar(50),
equip_rev_cons_before_amt	 	          numeric(38,10),
equip_cost_cons_before_amt	 	        numeric(38,10),
ship_qty	 	  								        numeric(38,10),
rev_qty	 											        numeric(38,10),
unit_cost	 										        numeric(38,10),
unit_price	 									        numeric(38,10),
rev_percent	 									        numeric(38,10),
carryover_rate	 							        numeric(38,10),
mgp_ratio	 										        numeric(38,10),
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(period_id,target_period);
 comment on table fin_dm_opt_fop.dm_fop_dimension_tgt_period_t                               is '量纲层级TGT数据接口表（只有一个版本）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.version_code                    is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.time_window_code                    is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.target_period                         is '目标时点';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.oversea_desc                    is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.dimension_group_code            is '量纲分组编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.dimension_group_cn_name         is '量纲分组中文名';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.dimension_group_en_name         is '量纲分组英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.dimension_subcategory_code      is '量纲子类编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.dimension_subcategory_cn_name   is '量纲子类中文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.dimension_subcategory_en_name   is '量纲子类英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.equip_rev_cons_before_amt        is '设备收入额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.equip_cost_cons_before_amt       is '设备成本额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.ship_qty                        is '发货量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.rev_qty                         is '收入量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.unit_cost                       is '单位成本';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.unit_price          						 is '单位价格';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.rev_percent        						 is '收入占比';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.carryover_rate        					 is '结转率';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.mgp_ratio          						 is '制毛率';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_t.del_flag                        is '是否删除';

-- dm_fop_dimension_tgt_period_filled_t 量纲层级TGT数据接口填充表（只有一个版本）
drop table if exists fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t;
create table fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t(
version_code	 	                      varchar(100),
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
period_id	 	                          numeric,
target_period	 				                      varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
dimension_group_code   	             	varchar(100),
dimension_group_cn_name        	      varchar(100),
dimension_group_en_name	         			varchar(100),
dimension_subcategory_code    	      varchar(100),
dimension_subcategory_cn_name 	      varchar(100),
dimension_subcategory_en_name 	      varchar(100),
currency	 														varchar(50),
equip_rev_cons_before_amt	 	          numeric(38,10),
equip_cost_cons_before_amt	 	        numeric(38,10),
ship_qty	 	  								        numeric(38,10),
rev_qty	 											        numeric(38,10),
unit_cost	 										        numeric(38,10),
unit_price	 									        numeric(38,10),
rev_percent	 									        numeric(38,10),
carryover_rate	 							        numeric(38,10),
mgp_ratio	 										        numeric(38,10),
unit_cost_ytd_data	 										        numeric(38,10),
unit_price_ytd_data									        numeric(38,10),
rev_percent_ytd_data									        numeric(38,10),
carryover_rate_ytd_data	 							        numeric(38,10),
mgp_ratio_ytd_data	 										        numeric(38,10),
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(period_id,target_period);
 comment on table fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t                               is '量纲层级TGT数据接口填充表（只有一个版本）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.version_code                    is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.time_window_code                    is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.target_period                         is '目标时点';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.oversea_desc                    is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.dimension_group_code            is '量纲分组编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.dimension_group_cn_name         is '量纲分组中文名';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.dimension_group_en_name         is '量纲分组英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.dimension_subcategory_code      is '量纲子类编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.dimension_subcategory_cn_name   is '量纲子类中文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.dimension_subcategory_en_name   is '量纲子类英文名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.equip_rev_cons_before_amt        is '设备收入额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.equip_cost_cons_before_amt       is '设备成本额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.ship_qty                        is '发货量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.rev_qty                         is '收入量（历史）';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.unit_cost                       is '单位成本';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.unit_price          						 is '单位价格';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.rev_percent        						 is '收入占比';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.carryover_rate        					 is '结转率';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.mgp_ratio          						 is '制毛率';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.unit_cost_ytd_data                      is '单位成本最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.unit_price_ytd_data          						 is '单位价格最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.rev_percent_ytd_data        						 is '收入占比最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.carryover_rate_ytd_data        					 is '结转率最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.mgp_ratio_ytd_data          						 is '制毛率最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_tgt_period_filled_t.del_flag                        is '是否删除';


-- dm_fop_dimension_lv2_tgt_period_his_t 量纲-重量级团队LV2层级TGT历史表（保留多个版本）
drop table if exists fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t;
create table fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t(
version_code	 	                      varchar(100),
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
period_id	 	                          numeric,
target_period	 				                      varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
currency	 														varchar(50),
equip_rev_cons_before_amt	 	          numeric(38,10),
equip_cost_cons_before_amt	 	        numeric(38,10),
equip_rev_cons_after_amt	 	          numeric(38,10),
equip_cost_cons_after_amt	 	          numeric(38,10),
mgp_ratio_after	 										        numeric(38,10),
mgp_ratio_before	 										        numeric(38,10),
mca_adjust_ratio	 	  								        numeric(38,10),
mgp_adjust_ratio	 											        numeric(38,10),
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(version_code,period_id,target_period);
 comment on table fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t                               is '量纲-重量级团队LV2层级TGT历史表（保留多个版本）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.version_code                    is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.time_window_code                    is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.target_period                         is '目标时点';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.oversea_desc                    is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.equip_rev_cons_before_amt       is '设备收入额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.equip_cost_cons_before_amt      is '设备成本额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.equip_rev_cons_after_amt        is '设备收入额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.equip_cost_cons_after_amt       is '设备成本额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.mgp_ratio_after          						 is '制毛率(对价后)';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.mgp_ratio_before          						 is '制毛率（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.mca_adjust_ratio                is 'MCA调整率';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.mgp_adjust_ratio                is '制毛调整率';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t.del_flag                        is '是否删除';

-- dm_fop_dimension_lv2_tgt_period_t 量纲-重量级团队LV2层级TGT接口表（只保留一个版本）
drop table if exists fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t;
create table fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t(
version_code	 	                      varchar(100),
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
period_id	 	                          numeric,
target_period	 				                      varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
currency	 														varchar(50),
equip_rev_cons_before_amt	 	          numeric(38,10),
equip_cost_cons_before_amt	 	        numeric(38,10),
equip_rev_cons_after_amt	 	          numeric(38,10),
equip_cost_cons_after_amt	 	          numeric(38,10),
mgp_ratio_after	 										        numeric(38,10),
mgp_ratio_before	 										        numeric(38,10),
mca_adjust_ratio	 	  								        numeric(38,10),
mgp_adjust_ratio	 											        numeric(38,10),
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(period_id,target_period);
 comment on table fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t                               is '量纲-重量级团队LV2层级TGT接口表（只保留一个版本）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.version_code                    is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.time_window_code                    is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.target_period                         is '目标时点';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.oversea_desc                    is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.equip_rev_cons_before_amt       is '设备收入额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.equip_cost_cons_before_amt      is '设备成本额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.equip_rev_cons_after_amt        is '设备收入额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.equip_cost_cons_after_amt       is '设备成本额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.mgp_ratio_after          						 is '制毛率(对价后)';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.mgp_ratio_before          						 is '制毛率（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.mca_adjust_ratio                is 'MCA调整率';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.mgp_adjust_ratio                is '制毛调整率';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t.del_flag                        is '是否删除';

-- dm_fop_dimension_lv2_tgt_period_filled_t 量纲-重量级团队LV2层级TGT接口填充表（只保留一个版本）
drop table if exists fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t;
create table fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t(
version_code	 	                      varchar(100),
scenarios	 	                          varchar(50),
time_window_code	 	                          varchar(50),
period_id	 	                          numeric,
target_period	 				                      varchar(100),
bg_code	 				                      varchar(50),
bg_name	 				                      varchar(200),
oversea_code	 	                      varchar(50),
oversea_desc	 	                      varchar(50),
lv1_code	 	                          varchar(100),
lv1_name	 	                          varchar(600),
lv2_code	 	                          varchar(100),
lv2_name	 	                          varchar(600),
currency	 														varchar(50),
equip_rev_cons_before_amt	 	          numeric(38,10),
equip_cost_cons_before_amt	 	        numeric(38,10),
equip_rev_cons_after_amt	 	          numeric(38,10),
equip_cost_cons_after_amt	 	          numeric(38,10),
mgp_ratio_after	 										        numeric(38,10),
mgp_ratio_before	 										        numeric(38,10),
mca_adjust_ratio	 	  								        numeric(38,10),
mgp_adjust_ratio	 											        numeric(38,10),
equip_rev_cons_after_amt_ytd_data	 	          numeric(38,10),
mgp_ratio_after_ytd_data	 										        numeric(38,10),
mca_adjust_ratio_ytd_data	 	  								        numeric(38,10),
mgp_adjust_ratio_ytd_data	 											        numeric(38,10),
remark	 											        varchar(500),
created_by	 									        int8,
creation_date	 								        timestamp,
last_updated_by	 							        int8,
last_update_date	 						        timestamp,
del_flag	 										        varchar(10)
 ) with (orientation = column,compression = low, colversion = 2.0, enable_delta = false)   
 distribute by hash(period_id,target_period);
 comment on table fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t                               is '量纲-重量级团队LV2层级TGT接口填充表（只保留一个版本）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.version_code                    is '版本编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.scenarios                    is '场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.time_window_code                    is '统计时间窗 ';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.period_id                       is '会计期';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.target_period                         is '目标时点';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.bg_code                         is 'BG编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.bg_name                         is 'BG名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.oversea_code                    is '区域编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.oversea_desc                    is '区域名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.lv1_code                        is '重量级团队LV1编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.lv1_name                        is '重量级团队LV1描述';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.lv2_code                        is '重量级团队LV2编码';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.lv2_name                        is '重量级团队LV2名称';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.currency is '币种';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.equip_rev_cons_before_amt       is '设备收入额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.equip_cost_cons_before_amt      is '设备成本额（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.equip_rev_cons_after_amt        is '设备收入额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.equip_cost_cons_after_amt       is '设备成本额（对价后）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.mgp_ratio_after          						 is '制毛率(对价后)';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.mgp_ratio_before          						 is '制毛率（对价前）';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.mca_adjust_ratio                is 'MCA调整率';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.mgp_adjust_ratio                is '制毛调整率';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.equip_rev_cons_after_amt_ytd_data        is '设备收入额（对价后）最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.mgp_ratio_after_ytd_data          						 is '制毛率（对价后）最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.mca_adjust_ratio_ytd_data                is 'MCA调整率最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.mgp_adjust_ratio_ytd_data                is '制毛调整率最大期次上一年的均值';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.remark                          is '备注';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.created_by                      is '创建人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.creation_date                   is '创建时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.last_updated_by                 is '修改人';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.last_update_date                is '修改时间';
comment on column fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_filled_t.del_flag                        is '是否删除';

