CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_dimension_ytd_articulated_aggr_t_ict"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
 /*
创建时间：2025-06-23
创建人  ：wwx1077703
背景描述：将量纲层的量本价勾稽后数据处理中的cnbg,ebg聚合到ict的逻辑


参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_ytd_dimension_articulated_aggr_t_ict();
*/
 
 declare
	v_sp_name VARCHAR(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_ytd_articulated_aggr_t_ict('||p_version_code||')';
	v_tbl_name VARCHAR(100) := 'fin_dm_opt_fop.dm_fop_dimension_ytd_articulated_aggr_t_ict';
	v_version_code VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   BIGINT; --步骤号
	v_dml_row_count  NUMBER DEFAULT 0 ;
  v_max_ytd  VARCHAR(50);  -- 最大期次的上一年
  v_ict_code VARCHAR(50) := 'PROD0002';
	


begin
	x_success_flag := '1';                                 --1表示成功
  

      
	
	       -- 获取版本号，如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select max(version_code)  into v_version_code
        from fin_dm_opt_fop.dm_fop_dimension_ytd_articulated_aggr_t ;	 -- 目标表和来源表是同一个表
        end if 
        ;	
        
        select  ((left(max(period_id)::VARCHAR,4)::numeric-1)::VARCHAR)||'12' into  v_max_ytd  
        from fin_dm_opt_fop.dm_fop_dimension_ytd_articulated_aggr_t  where version_code= v_version_code
        and  del_flag = 'N' 
        and  bg_code = any(ARRAY['PDCG901160','PDCG901159']) 
        ;
       
	
-- 	 --1.开始日志
--   v_step_mum := 1;
      perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '量本勾稽处理后汇总到ICT'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',获取最大版本和上年',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  


--       1、将cnbg,ebg的收入、成本、结转量、发货量拿过来，用于在ict计算指标的基础数据。
          drop table if exists dm_fop_ytd_dimension_articulated_aggr_ict_tmp;
          create temporary table  dm_fop_ytd_dimension_articulated_aggr_ict_tmp
          as
          SELECT
            agt.version_code,
            agt.period_id,
            agt.target_period,
            agt.scenarios,
            agt.phase_date,
            v_ict_code  as  bg_code, --v_ict_code
            'ICT'  as  bg_name,
            agt.oversea_code,
            agt.oversea_desc,
            agt.lv1_code,
            agt.lv1_name,
            agt.lv2_code,
            agt.lv2_name,
            agt.dimension_group_code,
            agt.dimension_group_cn_name,
            agt.dimension_group_en_name,
            agt.dimension_subcategory_code,
            agt.dimension_subcategory_cn_name,
            agt.dimension_subcategory_en_name,
            agt.currency,
            agt.fcst_type,
            agt.sop_type,
            case when  (coalesce(agt.dimension_subcategory_code,'SNULLC') ='NOSUB'  OR coalesce(agt.dimension_group_code,'SNULLC') = 'NODIM')  then 'NODIMENSION'
                 else  'DIMENSION'  end  as dimen_flag,
            sum(agt.equip_rev_cons_before_amt)  as  equip_rev_cons_before_amt,            -- 设备收入额（对价前）
            sum(agt.equip_cost_cons_before_amt)  as  equip_cost_cons_before_amt,           -- 设备成本额（对价前）
            sum(agt.carryover_qty)   as  carryover_qty,                        -- 结转量
            sum(agt.ship_qty)   as   ship_qty,                             -- 发货量
            'dm_fop_ytd_dimension_articulated_aggr_t'  as source_table,                         -- 来源表
            '1.预测数把cnbg,ebg的收入、成本、发货量、收入量卷积到ICT'  as  remark,
            sum(agt.equip_rev_cons_before_amt_upper)   as  equip_rev_cons_before_amt_upper,      -- 设备收入上界
            sum(agt.equip_rev_cons_before_amt_lower)   as  equip_rev_cons_before_amt_lower,      -- 设备收入下界
            sum(agt.equip_cost_cons_before_amt_upper)  as  equip_cost_cons_before_amt_upper,     -- 设备成本上界
            sum(agt.equip_cost_cons_before_amt_lower)   as   equip_cost_cons_before_amt_lower     -- 设备成本下界  select  *
          FROM
            fin_dm_opt_fop.dm_fop_dimension_ytd_articulated_aggr_t  agt 
         where  agt.del_flag = 'N'  and  agt.version_code =  v_version_code
         and  agt.bg_code = any(ARRAY['PDCG901160','PDCG901159']) 
         group by  
            agt.version_code,
            agt.period_id,
            agt.target_period,
            agt.scenarios,
            agt.phase_date,
            agt.oversea_code,
            agt.oversea_desc,
            agt.lv1_code,
            agt.lv1_name,
            agt.lv2_code,
            agt.lv2_name,
            agt.dimension_group_code,
            agt.dimension_group_cn_name,
            agt.dimension_group_en_name,
            agt.dimension_subcategory_code,
            agt.dimension_subcategory_cn_name,
            agt.dimension_subcategory_en_name,
            agt.currency,
            agt.fcst_type  ,
            agt.sop_type 
         union  all 
         select   
            tpt.version_code,
            tpt.period_id,
            tpt.target_period,
            tpt.scenarios,
            'TGT'  as phase_date,
            v_ict_code  as  bg_code, --v_ict_code
            'ICT'  as  bg_name,
            tpt.oversea_code,
            tpt.oversea_desc,
            tpt.lv1_code,
            tpt.lv1_name,
            tpt.lv2_code,
            tpt.lv2_name,
            tpt.dimension_group_code,
            tpt.dimension_group_cn_name,
            tpt.dimension_group_en_name,
            tpt.dimension_subcategory_code,
            tpt.dimension_subcategory_cn_name,
            tpt.dimension_subcategory_en_name,
            tpt.currency,
            'TGT'  as fcst_type,
            'TGT'  as sop_type,  
            case when  (coalesce(tpt.dimension_subcategory_code,'SNULLC') ='NOSUB'  OR coalesce(tpt.dimension_group_code,'SNULLC') = 'NODIM')  then 'NODIMENSION'
                 else  'DIMENSION'  end  as dimen_flag,
            tpt.equip_rev_cons_before_amt,
            tpt.equip_cost_cons_before_amt,
            tpt.ship_qty,
            tpt.rev_qty,
            'dm_fop_dimension_tgt_period_t'  as source_table,                         -- 来源表
            '1.实际数把cnbg,ebg的收入、成本、发货量、收入量卷积到ICT'  as  remark,
            null   as  equip_rev_cons_before_amt_upper,      -- 设备收入上界
            null   as  equip_rev_cons_before_amt_lower,      -- 设备收入下界
            null   as  equip_cost_cons_before_amt_upper,     -- 设备成本上界
            null    as   equip_cost_cons_before_amt_lower     -- 设备成本下界  select  *
        from   fin_dm_opt_fop.dm_fop_dimension_tgt_period_t  tpt
        where   tpt.del_flag = 'N' 
        and     RIGHT(tpt.target_period,3) = 'YTD' and  tpt.period_id = v_max_ytd
        and     tpt.bg_code = v_ict_code
        and     tpt.scenarios = any(ARRAY['量纲子类','量纲分组'])
            ;
            
       
       v_dml_row_count := sql%rowcount;  -- 收集数据量
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 2,
        p_log_cal_log_desc => '把ebg、cnbg上年的数据和当年的数据一起灌倒临时表中',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
--       2、通过上一步中获取到的收入、成本来计算率指标
          drop table if exists dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp;
          create temporary table  dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp
          as
          select   
          ait.version_code,
            ait.period_id,
            ait.target_period,
            ait.scenarios,
            ait.phase_date,
            ait.bg_code,
            ait.bg_name,
            ait.oversea_code,
            ait.oversea_desc,
            ait.lv1_code,
            ait.lv1_name,
            ait.lv2_code,
            ait.lv2_name,
            ait.dimension_group_code,
            ait.dimension_group_cn_name,
            ait.dimension_group_en_name,
            ait.dimension_subcategory_code,
            ait.dimension_subcategory_cn_name,
            ait.dimension_subcategory_en_name,
            ait.currency,
            ait.fcst_type,
            ait.sop_type,
            ait.equip_rev_cons_before_amt,            -- 设备收入额（对价前）
            ait.equip_cost_cons_before_amt,           -- 设备成本额（对价前）
            case when  ait.dimen_flag='DIMENSION' then ait.carryover_qty else null end carryover_qty,    -- 结转量
            case when  ait.dimen_flag='DIMENSION' then ait.ship_qty else null end ship_qty,    -- 发货量                        
            null   as  rev_percent,  -- 收入占比，先把收入拿过来在更新
            case when  ait.dimen_flag='DIMENSION' then 
                case when ait.carryover_qty = 0 then -999999
                  else  ait.equip_rev_cons_before_amt/ait.carryover_qty  end   
                else null  end  as  unit_price,   -- 单位价格 设备收入/结转量  
            case when  ait.dimen_flag='DIMENSION' then 
                case when ait.carryover_qty = 0 then -999999
                  else  ait.equip_rev_cons_before_amt_upper / ait.carryover_qty  end  
                else  null  end  as  unit_price_fcst_upper,                -- 单位价格置信上界  设备收入置信上界/结转量
            case when  ait.dimen_flag='DIMENSION' then ait.equip_rev_cons_before_amt_upper else null end equip_rev_cons_before_amt_upper,      -- 设备收入上界
            case when  ait.dimen_flag='DIMENSION' then 
                case when ait.carryover_qty = 0 then -999999
                  else  ait.equip_rev_cons_before_amt_lower/ait.carryover_qty   end
                else null  end    as  unit_price_fcst_lower,                -- 单位价格置信下界
            case when  ait.dimen_flag='DIMENSION' then ait.equip_rev_cons_before_amt_lower else null  end   as  equip_rev_cons_before_amt_lower,      -- 设备收入下界
            case when  ait.dimen_flag='DIMENSION' then 
                case when ait.carryover_qty = 0 then -999999
                  else  ait.equip_cost_cons_before_amt /ait.carryover_qty  end
                else  null  end    as  unit_cost,                            -- 均本
            case when  ait.dimen_flag='DIMENSION' then 
                case when ait.carryover_qty = 0 then -999999
                  else  ait.equip_cost_cons_before_amt_upper/ait.carryover_qty  end
                else  null  end   as  unit_cost_fcst_upper,                 -- 单位成本置信上界
            case when  ait.dimen_flag='DIMENSION' then ait.equip_cost_cons_before_amt_upper else null  end  equip_cost_cons_before_amt_upper,     -- 设备成本上界
            case when  ait.dimen_flag='DIMENSION' then 
                case when ait.carryover_qty = 0 then -999999
                  else  ait.equip_cost_cons_before_amt_lower /ait.carryover_qty  end
                else  null  end    as unit_cost_fcst_lower,                 -- 单位成本置信下界
            case when  ait.dimen_flag='DIMENSION' then ait.equip_cost_cons_before_amt_lower  else  null  end  equip_cost_cons_before_amt_lower ,     -- 设备成本下界
            case when  ait.dimen_flag='DIMENSION' then 
                case when ait.equip_rev_cons_before_amt = 0 then -999999
                  else  (ait.equip_rev_cons_before_amt-ait.equip_cost_cons_before_amt) /ait.equip_rev_cons_before_amt  end
                else  null  end  as mgp_ratio,                            -- 制毛率
            case when ait.ship_qty = 0 then -999999
                else  ait.carryover_qty /ait.ship_qty  end  as  carryover_rate,                       -- 结转率
            ait.source_table,                         -- 来源表
            ait.remark||'2.'||ait.dimen_flag||'率和占比的处理。'  as  remark,
            ait.dimen_flag
     from   dm_fop_ytd_dimension_articulated_aggr_ict_tmp  ait  ;
     
      v_dml_row_count := sql%rowcount;  -- 收集数据量
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '率指标和占比指标处理之后',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
      
     -- 这个把lv2的收入卷积进来
           -- 单独处理lv2层的收入
      drop table if exists dm_fop_ytd_dimension_articulated_aggr_ict_lv2_tmp;
	    create temporary table  dm_fop_ytd_dimension_articulated_aggr_ict_lv2_tmp
		  as
      with   lv2_sum  as (
      select   
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.phase_date
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  as lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency	                      -- 币种 
          ,base.fcst_type	                        
          ,base.sop_type	                        
          ,sum(base.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt 
        from   dm_fop_ytd_dimension_articulated_aggr_ict_tmp  base 
        group by 
           base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.phase_date
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end    -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency
          ,base.fcst_type
          ,base.sop_type
      ), lv2_ran_sum
       as (
        select   
             base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.phase_date
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end  as lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD'                      -- 币种 
          ,base.currency	                      -- 币种 
          ,base.fcst_type	 
          ,base.sop_type                     
          ,sum(base.equip_rev_cons_before_amt)  as equip_rev_cons_before_amt 
          ,sum(base.equip_cost_cons_before_amt)  as equip_cost_cons_before_amt 
        from   dm_fop_ytd_dimension_articulated_aggr_ict_tmp  base 
        where   base.lv2_code in   ('133661', '101212', '153326', '153324') 
        and   dimension_subcategory_code='NOSUB'
        group by 
            base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.phase_date
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,case when base.lv2_code in   ('133661', '101212', '153326', '153324') then 'RAN' else base.lv2_code  end    -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD'                      -- 币种 
          ,base.currency   
          ,base.fcst_type   
          ,base.sop_type
        )
                  
         select   
            base.version_code	                  -- 版本编码
          ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
          ,base.period_id	                      -- 会计期
          ,base.target_period	                  -- 目标时点 无 我来计算的
          ,base.phase_date
          ,base.bg_code	                          -- BG编码
          ,base.oversea_code	                  -- 区域编码
          ,base.lv1_code	                      -- 重量级团队LV1编码
          ,base.lv2_vir  -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
          ,base.currency                     -- 币种 
          ,base.fcst_type   
          ,base.sop_type                     
          ,base.equip_rev_cons_before_amt 
          ,lrs.equip_rev_cons_before_amt  as ran_rev_cons_before_amt
          ,lrs.equip_cost_cons_before_amt  as ran_cost_cons_before_amt
        from   lv2_sum  base 
        left join lv2_ran_sum lrs 
        on  base.version_code = lrs.version_code	                  -- 版本编码
         and  base.scenarios=	lrs.scenarios                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
         and  base.phase_date=	 lrs.phase_date             --   
         and  base.period_id=	  lrs.period_id                    -- 会计期
         and  base.target_period=	 lrs.target_period                 -- 目标时点 无 我来计算的
         and  base.bg_code=	  lrs.bg_code                        -- BG编码
         and  base.oversea_code=	lrs.oversea_code                  -- 区域编码
         and  base.lv1_code=	lrs.lv1_code                      -- 重量级团队LV1编码
         and  base.lv2_vir= lrs.lv2_vir   -- 'GUC', 'SRAN', '5G&LTE TDD', '5G&LTE FDD' 
         and  base.currency=	lrs.currency 
         and  coalesce(base.fcst_type,'SNULLC')=	coalesce(lrs.fcst_type ,'SNULLC')
         and  coalesce(base.sop_type,'SNULLC')=	coalesce(lrs.sop_type ,'SNULLC')
          ;              -- 币种  ;
                  
                  
        -- 更新收入占比,RAN里面的无量纲是相同的一个值，所以先聚合，在关联
        update   dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp  tmp    
        SET tmp.rev_percent=  case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324') and  tmp.dimension_subcategory_code='NOSUB' 
                                  then  
                                     case when upd.equip_rev_cons_before_amt = 0   then -999999 
                                         else  upd.ran_rev_cons_before_amt/upd.equip_rev_cons_before_amt end
                                  else
                                    case when upd.equip_rev_cons_before_amt = 0   then -999999 
                                         else  tmp.equip_rev_cons_before_amt/upd.equip_rev_cons_before_amt end 
                                 end,
              tmp.mgp_ratio= case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324') and  tmp.dimension_subcategory_code='NOSUB' 
                                  then
                                     case when upd.ran_rev_cons_before_amt = 0  then -999999 
                                      else  (1-upd.ran_cost_cons_before_amt/upd.ran_rev_cons_before_amt) end 
                                  else 
                                       tmp.mgp_ratio  end   
        from  dm_fop_ytd_dimension_articulated_aggr_ict_lv2_tmp upd 
        where   1=1  
        and   tmp.version_code = upd.version_code
        and tmp.period_id=upd.period_id    
        and tmp.target_period=upd.target_period 
        and  tmp.scenarios = upd.scenarios  
        and  tmp.phase_date = upd.phase_date  
        and tmp.bg_code = upd.bg_code
        and tmp.oversea_code = upd.oversea_code 
        and tmp.lv1_code = upd.lv1_code  
        and  case when tmp.lv2_code  in  ('133661', '101212', '153326', '153324')  then 'RAN' else tmp.lv2_code  end = upd.lv2_vir 
        and coalesce(tmp.fcst_type,'SNULLC') = coalesce(upd.fcst_type,'SNULLC') 
        and coalesce(tmp.sop_type,'SNULLC') = coalesce(upd.sop_type,'SNULLC') 
        and tmp.currency = upd.currency ; 
           
         v_dml_row_count := sql%rowcount;  -- 收集数据量
       perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 4,
        p_log_cal_log_desc => '更新占比和制毛率',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
--         更新制毛率的异常值，取最大期次的上年          
                  
        drop table if exists max_ytd_mgp_ratio_tmp;
        create temporary table  max_ytd_mgp_ratio_tmp
        as 
        SELECT
          version_code,
          period_id,
          target_period,
          scenarios,
          phase_date,
          bg_code,
          oversea_code,
          lv1_code,
          lv2_code,
          dimension_group_code,
          dimension_subcategory_code,
          currency,
          fcst_type,
          sop_type,
          mgp_ratio ,
          unit_price,
          unit_cost
        FROM
          dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp
        where   period_id::varchar =  v_max_ytd 
         ;
        
        
        update   dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp  tmp    
        SET tmp.mgp_ratio=  upd.mgp_ratio ,
        tmp.remark = tmp.remark||'，制毛率取上年值' 
        from  max_ytd_mgp_ratio_tmp upd 
        where    ( tmp.mgp_ratio > 1 or tmp.mgp_ratio < -1 )
        and    tmp.mgp_ratio != -999999
        and    tmp.phase_date !='TGT' 
        and  tmp.scenarios = upd.scenarios  
        and tmp.bg_code = upd.bg_code
        and tmp.oversea_code = upd.oversea_code 
        and tmp.lv1_code = upd.lv1_code  
        and  tmp.lv2_code  = upd.lv2_code 
        and  coalesce(tmp.dimension_group_code,'SNULLC')  = upd.dimension_group_code 
        and  coalesce(tmp.dimension_subcategory_code,'SNULLC')  = coalesce(upd.dimension_subcategory_code ,'SNULLC')
        and tmp.currency = upd.currency ; 
        
        
        update   dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp  tmp    
        SET tmp.mgp_ratio=  0 ,
        tmp.remark = tmp.remark||'，制毛率取上年值之后的兜底处理' 
        where    ( tmp.mgp_ratio > 1 or tmp.mgp_ratio < -1 )
        and    tmp.mgp_ratio != -999999
        and    tmp.phase_date !='TGT' ;
        
        
        update   dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp  tmp    
        SET tmp.unit_price=  upd.unit_price   ,
        tmp.remark = tmp.remark||'，均价取上年值' 
        from  max_ytd_mgp_ratio_tmp upd 
        where     tmp.unit_price < 0 
        and    tmp.unit_price != -999999
        and    tmp.phase_date !='TGT' 
        and  tmp.scenarios = upd.scenarios  
        and tmp.bg_code = upd.bg_code
        and tmp.oversea_code = upd.oversea_code 
        and tmp.lv1_code = upd.lv1_code  
        and  tmp.lv2_code  = upd.lv2_code 
        and  coalesce(tmp.dimension_group_code,'SNULLC')  = upd.dimension_group_code 
        and  coalesce(tmp.dimension_subcategory_code,'SNULLC')  = coalesce(upd.dimension_subcategory_code ,'SNULLC')
        and tmp.currency = upd.currency ; 
        
        update   dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp  tmp    
        SET tmp.unit_cost=  upd.unit_cost   ,
        tmp.remark = tmp.remark||'，均本取上年值' 
        from  max_ytd_mgp_ratio_tmp upd 
        where     tmp.unit_cost < 0 
        and    tmp.unit_cost != -999999
        and    tmp.phase_date !='TGT' 
        and  tmp.scenarios = upd.scenarios  
        and tmp.bg_code = upd.bg_code
        and tmp.oversea_code = upd.oversea_code 
        and tmp.lv1_code = upd.lv1_code  
        and  tmp.lv2_code  = upd.lv2_code 
        and  coalesce(tmp.dimension_group_code,'SNULLC')  = upd.dimension_group_code 
        and  coalesce(tmp.dimension_subcategory_code,'SNULLC')  = coalesce(upd.dimension_subcategory_code ,'SNULLC')
        and tmp.currency = upd.currency ; 
        
        delete   from  fin_dm_opt_fop.dm_fop_dimension_ytd_articulated_aggr_t  where  version_code = v_version_code  and  bg_code = v_ict_code  ;  --v_ict_code
        
        INSERT INTO fin_dm_opt_fop.dm_fop_dimension_ytd_articulated_aggr_t (
          version_code,
          period_id,
          target_period,
          scenarios,
          phase_date,
          bg_code,
          bg_name,
          oversea_code,
          oversea_desc,
          lv1_code,
          lv1_name,
          lv2_code,
          lv2_name,
          dimension_group_code,
          dimension_group_cn_name,
          dimension_group_en_name,
          dimension_subcategory_code,
          dimension_subcategory_cn_name,
          dimension_subcategory_en_name,
          currency,
          fcst_type,
          sop_type,
          equip_rev_cons_before_amt,
          equip_cost_cons_before_amt,
          carryover_qty,
          ship_qty,
          rev_percent,
          unit_price,
          unit_cost,
          mgp_ratio,
          carryover_rate,
          unit_price_fcst_upper,
          unit_price_fcst_lower,
          unit_cost_fcst_upper,
          unit_cost_fcst_lower,
          source_table,
          remark,
          created_by,
          creation_date,
          last_updated_by,
          last_update_date,
          del_flag,
          equip_rev_cons_before_amt_upper,
          equip_rev_cons_before_amt_lower,
          equip_cost_cons_before_amt_upper,
          equip_cost_cons_before_amt_lower 
        )
        select 
        version_code,
          period_id,
          target_period,
          scenarios,
          phase_date,
          bg_code,
          bg_name,
          oversea_code,
          oversea_desc,
          lv1_code,
          lv1_name,
          lv2_code,
          lv2_name,
          dimension_group_code,
          dimension_group_cn_name,
          dimension_group_en_name,
          dimension_subcategory_code,
          dimension_subcategory_cn_name,
          dimension_subcategory_en_name,
          currency,
          fcst_type,
          sop_type,
          equip_rev_cons_before_amt,
          equip_cost_cons_before_amt,
          carryover_qty,
          ship_qty,
          rev_percent,
          unit_price,
          unit_cost,
          mgp_ratio,
          carryover_rate,
          unit_price_fcst_upper,
          unit_price_fcst_lower,
          unit_cost_fcst_upper,
          unit_cost_fcst_lower,
          source_table,
          remark,
          -1  as created_by,
          now()  as creation_date,
          -1  as last_updated_by,
          now()  as last_update_date,
          'N'  as  del_flag,
          equip_rev_cons_before_amt_upper,
          equip_rev_cons_before_amt_lower,
          equip_cost_cons_before_amt_upper,
          equip_cost_cons_before_amt_lower 
          from  dm_fop_ytd_dimension_articulated_aggr_ict_all_item_tmp 
          where   phase_date !='TGT' ;

        

  	-- 写结束日志
    v_dml_row_count := sql%rowcount;  -- 收集数据量
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 8,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',插入历史表',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;

--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
			) ;
	x_success_flag := '2001';
	
	
		
 end;
 $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100 ;