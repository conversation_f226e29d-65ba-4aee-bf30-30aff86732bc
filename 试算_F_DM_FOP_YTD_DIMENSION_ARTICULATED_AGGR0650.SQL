CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR(OUT X_SUCCESS_FLAG TEXT)
 RETURNS TEXT
 LANGUAGE PLPGSQL
 NOT FENCED NOT SHIPPABLE
AS $$
 /*
创建时间：
创建人  ：TWX1139790
背景描述：将算法预测后的数据、量本价勾稽后的预测数据以及TGT表的实际数据，整合处理后落入YTD结果表
参数描述：参数一(P_VERSION_CODE)：版本编码202505
          参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR();
*/
 
 DECLARE
    V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR';
    V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
    V_STEP_NUM   BIGINT := 0; --步骤号
    V_TGT_YEAR INT := YEAR(CURRENT_TIMESTAMP)-1;   -- 只取当年的数据（TGT表取数限制）
    V_LAST_PERIOD INT := CAST(TO_CHAR(ADD_MONTHS(CURRENT_TIMESTAMP,-13),'YYYYMM') AS INT);
    V_FCST_PERIOD INT;   -- 算法预测输出表中使用的类似版本概念值
BEGIN
    X_SUCCESS_FLAG := '1';                                 --1表示成功
    
       -- 如果是传 VERSION_CODE 调函数取JAVA传入的 P_VERSION_CODE ，如果是自动调度的则取 当前年月 版本
/*        IF P_VERSION_CODE IS NOT NULL THEN 
        SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
        ELSE */
        SELECT TO_CHAR(CURRENT_DATE,'YYYYMM') AS VERSION_CODE INTO V_VERSION_CODE ;
--        END IF ;

  -- 取得算法预测输出表中最大的一般数据
  SELECT MAX(PERIOD_ID) INTO V_FCST_PERIOD FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_FCST_T;
  
  -- 删除结果表历史版本数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T WHERE VERSION_CODE = '||V_VERSION_CODE;   -- 量纲层级YTD表
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T WHERE VERSION_CODE = '||V_VERSION_CODE;   -- LV2层级YTD表
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T WHERE VERSION_CODE = '||V_VERSION_CODE;   -- LV1层级YTD表


  -- 创建量纲临时表
  DROP TABLE IF EXISTS FOP_DIMENSION_RESULT_TMP;
  CREATE TEMPORARY TABLE FOP_DIMENSION_RESULT_TMP (
         PERIOD_ID NUMERIC,
         TARGET_PERIOD VARCHAR(50),
         SCENARIOS CHARACTER VARYING(50),
         PHASE_DATE CHARACTER VARYING(100),
         BG_CODE CHARACTER VARYING(50),
         BG_NAME CHARACTER VARYING(200),
         OVERSEA_CODE CHARACTER VARYING(50),
         OVERSEA_DESC CHARACTER VARYING(50),
         LV1_CODE CHARACTER VARYING(50),
         LV1_NAME CHARACTER VARYING(600),
         LV2_CODE CHARACTER VARYING(50),
         LV2_NAME CHARACTER VARYING(600),
         DIMENSION_GROUP_CODE CHARACTER VARYING(50),
         DIMENSION_GROUP_CN_NAME CHARACTER VARYING(600),
         DIMENSION_GROUP_EN_NAME CHARACTER VARYING(600),
         DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(50),
         DIMENSION_SUBCATEGORY_CN_NAME CHARACTER VARYING(600),
         DIMENSION_SUBCATEGORY_EN_NAME CHARACTER VARYING(600),
         CURRENCY_CODE CHARACTER VARYING(50),
         FCST_TYPE CHARACTER VARYING(100),
         EQUIP_REV_CONS_BEFORE_AMT NUMERIC(38,10),
         EQUIP_COST_CONS_BEFORE_AMT NUMERIC(38,10),
         CARRYOVER_QTY NUMERIC(38,10),
         SHIP_QTY NUMERIC(38,10), 
         REV_PERCENT NUMERIC(38,10),
         UNIT_PRICE NUMERIC(38,10),
         UNIT_COST NUMERIC(38,10),
         MGP_RATIO NUMERIC(38,10),
         CARRYOVER_RATE NUMERIC(38,10),
         UNIT_PRICE_FCST_UPPER NUMERIC(38,10),
         UNIT_PRICE_FCST_LOWER NUMERIC(38,10),
         UNIT_COST_FCST_UPPER NUMERIC(38,10),
         UNIT_COST_FCST_LOWER NUMERIC(38,10)
   )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);
  
  -- 计算量纲层级数据插入临时表
  INSERT INTO FOP_DIMENSION_RESULT_TMP(
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         CARRYOVER_QTY,
         SHIP_QTY, 
         REV_PERCENT,
         UNIT_PRICE,
         UNIT_COST,
         MGP_RATIO,
         CARRYOVER_RATE,
         UNIT_PRICE_FCST_UPPER,
         UNIT_PRICE_FCST_LOWER,
         UNIT_COST_FCST_UPPER,
         UNIT_COST_FCST_LOWER
  )
  WITH PYTHON_FCST_TMP AS(
  -- 取量纲层级：算法预测后YTD指标数据
  SELECT T1.TARGET_PERIOD||'YTD' AS TARGET_PERIOD,
         T1.SCENARIOS,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY,
         T1.FCST_TYPE,
         T1.UNIT_PRICE_FCST,
         T1.UNIT_COST_FCST,
         T1.CARRYOVER_RATIO_FCST,
         T1.UNIT_PRICE_FCST_UPPER,
         T1.UNIT_PRICE_FCST_LOWER,
         T1.UNIT_COST_FCST_UPPER,
         T1.UNIT_COST_FCST_LOWER,
         T1.MGP_RATE_BEFORE_FCST,
         T1.REV_PERCENT_FCST
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_FCST_T T1 
      WHERE T1.PERIOD_ID = V_FCST_PERIOD   -- 限制只取一个最大版本的数据
      AND T1.SCENARIOS IN ('量纲分组','量纲子类')   -- 只处理量纲数据
      AND T1.CURRENCY = 'CNY'   -- 当前需求，只取人民币
      AND SUBSTR(T1.TARGET_PERIOD,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
      AND T1.BG_CODE IN ('PDCG901159','PDCG901160')   -- 不取ICT的数据
      AND T1.DEL_FLAG = 'N'
    ),
  TGT_HIS_TMP AS(
  -- 取量纲层级：TGT表当前T年实际数YTD的数据
  SELECT T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY,
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.REV_QTY AS CARRYOVER_QTY,
         T1.SHIP_QTY
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_TGT_PERIOD_T T1 
	  WHERE T1.VERSION_CODE = V_VERSION_CODE
	  AND SUBSTR(T1.TARGET_PERIOD,-3,3) = 'YTD'   -- 这张表只取YTD数据
	  AND SUBSTR(T1.TARGET_PERIOD,1,4) = V_TGT_YEAR
	  AND T1.SCENARIOS IN ('量纲分组','量纲子类')   -- 只处理量纲数据
	  AND T1.CURRENCY = 'CNY'   -- 当前需求，只取人民币
      AND T1.BG_CODE IN ('PDCG901159','PDCG901160')   -- 不取ICT的数据
  ),
  ARTICULATION_FCST_TMP AS(
  -- 取量纲层级：量本勾稽后预测的月度数据
  SELECT T1.MON_CODE||'YTD' AS TARGET_PERIOD,   -- 假设MON_CODE存储为202506
         T1.SCE_CODE AS SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.GEO_CODE AS OVERSEA_CODE,
         T1.LV2_CODE,
         T1.DIMENSION_GROUP_CODE,   -- 当场景为量纲分组时，取值，否则置空
         T1.DIMENSION_SUBCATEGORY_CODE,   -- 当场景为量纲子类时，取值，否则置空
         'CNY' AS CURRENCY_CODE,
         T1.MET_CODE AS FCST_TYPE,
         SUM(T1.EQUIP_REV_CONS_BEFORE_AMT) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.DIMENSION_GROUP_CODE,T1.DIMENSION_SUBCATEGORY_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS EQUIP_REV_CONS_BEFORE_AMT, 
         CASE WHEN T1.DIMENSION_GROUP_CODE ='NODIM' OR T1.DIMENSION_SUBCATEGORY_CODE = 'NOSUB'
              THEN SUM(T1.EQUIP_REV_CONS_BEFORE_AMT * (1-NVL(T2.MGP_RATE_BEFORE_FCST,0))) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.DIMENSION_GROUP_CODE,T1.DIMENSION_SUBCATEGORY_CODE,T1.MET_CODE ORDER BY T1.MON_CODE)   -- 无量纲：设备成本（对价前）= 设备收入（对价前）* （1-制毛率（对价前））
              ELSE SUM(T1.CARRYOVER_QTY * NVL(T2.UNIT_COST_FCST,0)) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.DIMENSION_GROUP_CODE,T1.DIMENSION_SUBCATEGORY_CODE,T1.MET_CODE ORDER BY T1.MON_CODE)   -- 有量纲：设备成本（对价前）= 单位成本*结转量
         END AS EQUIP_COST_CONS_BEFORE_AMT,
         SUM(T1.CARRYOVER_QTY) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.DIMENSION_GROUP_CODE,T1.DIMENSION_SUBCATEGORY_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS CARRYOVER_QTY,
         SUM(T1.SHIP_QTY) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.DIMENSION_GROUP_CODE,T1.DIMENSION_SUBCATEGORY_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS SHIP_QTY 
      FROM (
              SELECT T1.VER_CODE,
                     T1.MON_CODE,   -- 假设MON_CODE存储为202506
                     T3.PHASE_DATE,
                     T1.SCE_CODE,
                     T1.BG_CODE,
                     T1.GEO_CODE,
                     SUBSTR(T1.DIV_CODE,INSTR(T1.DIV_CODE,'_')+1) AS LV2_CODE,
                     DECODE(T1.SCE_CODE,'量纲分组',SUBSTR(T1.DIV_CODE,1,INSTR(T1.DIV_CODE,'_')-1),NULL) AS DIMENSION_GROUP_CODE,   -- 当场景为量纲分组时，取值，否则置空
                     DECODE(T1.SCE_CODE,'量纲子类',SUBSTR(T1.DIV_CODE,1,INSTR(T1.DIV_CODE,'_')-1),NULL) AS DIMENSION_SUBCATEGORY_CODE,   -- 当场景为量纲子类时，取值，否则置空
                     T1.MET_CODE,
                     SUM(DECODE(T1.LIC_CODE,'量价设备收入',NVL(AMOUNT,0),0)) AS EQUIP_REV_CONS_BEFORE_AMT,   -- 设备收入（对价前）
                     SUM(DECODE(T1.LIC_CODE,'结转量',NVL(AMOUNT,0),0)) AS CARRYOVER_QTY,   -- 结转量
                     SUM(DECODE(T1.LIC_CODE,'SOP计划量',NVL(AMOUNT,0),0)) AS SHIP_QTY   -- 发货量
                  FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_T T1 
                  LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
                  ON T1.SOP_CODE = T3.SOP_CODE 
                  WHERE T1.VER_CODE = V_FCST_PERIOD
                  AND T3.VERSION_CODE = V_VERSION_CODE
                  AND UPPER(T3.SOP_TYPE) = 'FCST'
                  AND SUBSTR(T1.MON_CODE,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
                  AND T1.LIC_CODE IN ('结转量','SOP计划量','量价设备收入')
                  AND T1.AMOUNT <> 0
                  AND T1.DIV_CODE IN(SELECT MEMBER_CODE
                                    FROM DM_FSPPUB_DIM_MEMBER_T
                                   WHERE DIM_ID =(SELECT DIM_ID
                                                    FROM DM_FSPPUB_MY_DIMENSION_T
                                                   WHERE DIM_NAME = '产业'
                                                     AND WORKSPACE_ID IN(SELECT WORKSPACE_ID
                                                                            FROM DM_FSPPUB_WORKSPACE_BASE_INFO_T
                                                                            WHERE WORKSPACE_NAME = '盈利测试'
                                                                         )
                                                 )
                                     AND LEVEL IN ('4','5'))
                  AND DIV_CODE NOT LIKE '%SNULL'
                  GROUP BY T1.VER_CODE,
                           T1.MON_CODE,
                           T3.PHASE_DATE,
                           T1.SCE_CODE,
                           T1.BG_CODE,
                           T1.GEO_CODE,
                           T1.DIV_CODE,
                           T1.MET_CODE
	        ) T1
        LEFT JOIN PYTHON_FCST_TMP T2
        ON T1.MON_CODE = SUBSTR(T2.TARGET_PERIOD,1,6)
        AND T1.SCE_CODE = T2.SCENARIOS
        AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
        AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
        AND T1.LV2_CODE = T2.LV2_CODE
        AND T1.BG_CODE = T2.BG_CODE
        AND T1.GEO_CODE = T2.OVERSEA_CODE
        AND T1.MET_CODE = T2.FCST_TYPE
    ),
  DIM_PERIOD_TMP AS(
  -- 补全维度的当年12个月月份维
  SELECT DISTINCT T2.PERIOD_ID,
         T2.PERIOD_ID||'YTD' AS TARGET_PERIOD,
         T1.SCENARIOS,
         T3.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY
      FROM PYTHON_FCST_TMP T1 ,   -- 算法预测表
      (
      --生成连续月份, 当年第一月至当年12月
      SELECT CAST(TO_CHAR(ADD_MONTHS(TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-1)||'01','YYYYMM'),NUM.VAL - 1),'YYYYMM') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                      TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-1)||'01','YYYYMM'),
                                                      TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP))||'01','YYYYMM'))),
                              1) NUM(VAL)) T2,
      FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
      WHERE T3.VERSION_CODE = V_VERSION_CODE
      AND UPPER(T3.SOP_TYPE) = 'FCST'
    ),
  FCST_SUM_TMP AS (
  -- 处理实际数+预测月份YTD数据：上个月实际数+预测月份YTD数
  SELECT T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY,
         T3.FCST_TYPE, 
         T2.EQUIP_REV_CONS_BEFORE_AMT AS EQUIP_REV_AMT_HIS,
         T3.EQUIP_REV_CONS_BEFORE_AMT + T4.EQUIP_REV_CONS_BEFORE_AMT AS EQUIP_REV_AMT_HIS_FCST,
         T2.EQUIP_COST_CONS_BEFORE_AMT AS EQUIP_COST_AMT_HIS,
         T3.EQUIP_COST_CONS_BEFORE_AMT + T4.EQUIP_COST_CONS_BEFORE_AMT AS EQUIP_COST_AMT_HIS_FCST,
         T2.CARRYOVER_QTY AS CARRYOVER_QTY_HIS,
         T3.CARRYOVER_QTY + T4.CARRYOVER_QTY AS CARRYOVER_QTY_HIS_FCST,
         T2.SHIP_QTY AS SHIP_QTY_HIS,
         T3.SHIP_QTY + T4.SHIP_QTY AS SHIP_QTY_HIS_FCST
      FROM DIM_PERIOD_TMP T1   -- 全时间维表
      LEFT JOIN TGT_HIS_TMP T2   -- TGT实际数处理数据表
      ON T1.TARGET_PERIOD = T2.TARGET_PERIOD
      AND T1.SCENARIOS = T2.SCENARIOS
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND T1.LV2_CODE = T2.LV2_CODE
      AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T1.CURRENCY = T2.CURRENCY
      LEFT JOIN ARTICULATION_FCST_TMP T3   -- 量本价勾稽后处理数据表 
      ON T1.TARGET_PERIOD = T3.TARGET_PERIOD
      AND T1.SCENARIOS = T3.SCENARIOS
      AND T1.BG_CODE = T3.BG_CODE
      AND T1.OVERSEA_CODE = T3.OVERSEA_CODE
      AND T1.LV2_CODE = T3.LV2_CODE
      AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T3.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T3.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T1.CURRENCY = T3.CURRENCY_CODE
      AND T1.PHASE_DATE = T3.PHASE_DATE
      LEFT JOIN ( 
                 SELECT TARGET_PERIOD,
                        SCENARIOS,
                        BG_CODE,
                        OVERSEA_CODE,
                        LV1_CODE,
                        LV2_CODE,
                        DIMENSION_GROUP_CODE,
                        DIMENSION_SUBCATEGORY_CODE,
                        CURRENCY,
                        EQUIP_REV_CONS_BEFORE_AMT,
                        EQUIP_COST_CONS_BEFORE_AMT,
                        CARRYOVER_QTY,
                        SHIP_QTY
                    FROM TGT_HIS_TMP
                    WHERE SUBSTR(TARGET_PERIOD,1,6) = V_LAST_PERIOD  -- 只取上个月历史YTD数据
                 ) T4
      ON T3.SCENARIOS = T4.SCENARIOS
      AND T3.BG_CODE = T4.BG_CODE
      AND T3.OVERSEA_CODE = T4.OVERSEA_CODE
      AND T3.LV2_CODE = T4.LV2_CODE
      AND NVL(T3.DIMENSION_GROUP_CODE,'S1') = NVL(T4.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T3.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T4.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T3.CURRENCY_CODE = T4.CURRENCY
    )
  SELECT T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY,
         T2.FCST_TYPE,
         NVL(T1.EQUIP_REV_AMT_HIS,T1.EQUIP_REV_AMT_HIS_FCST) AS EQUIP_REV_CONS_BEFORE_AMT,
         NVL(T1.EQUIP_COST_AMT_HIS,T1.EQUIP_COST_AMT_HIS_FCST) AS EQUIP_COST_CONS_BEFORE_AMT,
         NVL(T1.CARRYOVER_QTY_HIS,T1.CARRYOVER_QTY_HIS_FCST) AS CARRYOVER_QTY,
         NVL(T1.SHIP_QTY_HIS,T1.SHIP_QTY_HIS) AS SHIP_QTY, 
         CASE WHEN T1.DIMENSION_GROUP_CODE = 'NODIM' OR T1.DIMENSION_SUBCATEGORY_CODE = 'NOSUB'
              THEN T2.REV_PERCENT_FCST 
              ELSE NULL
         END AS REV_PERCENT,   -- 为无量纲数据时，取算法预测表的收入占比，否则置空
         T2.UNIT_PRICE_FCST,
         T2.UNIT_COST_FCST,
         CASE WHEN T1.DIMENSION_GROUP_CODE = 'NODIM' OR T1.DIMENSION_SUBCATEGORY_CODE = 'NOSUB'
              THEN T2.MGP_RATE_BEFORE_FCST 
              ELSE NULL
         END AS MGP_RATIO,   -- 为无量纲数据时，取算法预测表的制毛率（对价前），否则置空
         T2.CARRYOVER_RATIO_FCST,
         T2.UNIT_PRICE_FCST_UPPER,
         T2.UNIT_PRICE_FCST_LOWER,
         T2.UNIT_COST_FCST_UPPER,
         T2.UNIT_COST_FCST_LOWER
      FROM FCST_SUM_TMP T1
      LEFT JOIN PYTHON_FCST_TMP T2   -- 算法预测后处理数据表
      ON T1.TARGET_PERIOD = T2.TARGET_PERIOD
      AND T1.SCENARIOS = T2.SCENARIOS
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND T1.LV2_CODE = T2.LV2_CODE
      AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T1.CURRENCY = T2.CURRENCY;
  DBMS_OUTPUT.PUT_LINE('量纲层级数据插入临时表');
   
  
  ------------------------------------------------------------------------------LV2层级逻辑--------------------------------------------------------------------------------------
 -- 将LV2逻辑的相关指标数据计算后插入LV2临时表
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER,
         MCA_ADJUST_RATIO,
         MGP_ADJUST_RATE,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH ARTICULATION_LV2_FCST_TMP AS(
  -- 取量本价勾稽后LV2预测数据
  SELECT T1.MON_CODE||'YTD' AS TARGET_PERIOD,   -- 假设MON_CODE存储为202506
         T1.SCE_CODE AS SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.GEO_CODE AS OVERSEA_CODE,
         T1.LV2_CODE,
         'CNY' AS CURRENCY_CODE,
         T1.MET_CODE AS FCST_TYPE,
         SUM(DECODE(T1.SCE_CODE,'LV2',NULL,T1.EQUIP_REV_CONS_BEFORE_AMT)) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS EQUIP_REV_CONS_BEFORE_AMT, 
         SUM(DECODE(T1.SCE_CODE,'LV2',NULL,T1.EQUIP_REV_CONS_AFTER_AMT)) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS EQUIP_REV_CONS_AFTER_AMT,
         SUM(DECODE(T1.SCE_CODE,'LV2',NULL,T1.EQUIP_REV_CONS_BEFORE_AMT * (1-MGP_RATIO_BEFORE))) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS EQUIP_COST_CONS_BEFORE_AMT, 
         SUM(DECODE(T1.SCE_CODE,'LV2',NULL,T1.EQUIP_REV_CONS_AFTER_AMT * (1-MGP_RATIO_AFTER))) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T1.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.LV2_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS EQUIP_COST_CONS_AFTER_AMT,
         T1.MGP_RATIO_BEFORE,
         T1.MGP_RATIO_AFTER,
         DECODE(T1.SCE_CODE,'LV2',T1.EQUIP_REV_CONS_AFTER_AMT,NULL) AS EQUIP_REV_CONS_AFTER_AMT_NODIM,
         DECODE(T1.SCE_CODE,'LV2',T1.EQUIP_REV_CONS_AFTER_AMT * (1-MGP_RATIO_AFTER),NULL) AS EQUIP_COST_CONS_AFTER_AMT_NODIM,
         DECODE(T1.SCE_CODE,'LV2',T1.MGP_RATIO_AFTER,NULL) AS MGP_RATIO_AFTER_NODIM
      FROM (
              SELECT T1.VER_CODE,
                     T1.MON_CODE,   -- 假设MON_CODE存储为202506
                     T3.PHASE_DATE,
                     T1.SCE_CODE,
                     T1.BG_CODE,
                     T1.GEO_CODE,
                     T1.DIV_CODE AS LV2_CODE,
                     T1.MET_CODE,
                     SUM(DECODE(T1.LIC_CODE,'量价设备收入',NVL(AMOUNT,0),0)) AS EQUIP_REV_CONS_BEFORE_AMT,   -- 设备收入（对价前）
                     SUM(DECODE(T1.LIC_CODE,'损益口径设备收入',NVL(AMOUNT,0),0)) AS EQUIP_REV_CONS_AFTER_AMT,   -- 设备收入（对价后）
                     SUM(DECODE(T1.LIC_CODE,'量价制毛率',NVL(AMOUNT,0),0)) AS MGP_RATIO_BEFORE,   -- 待确认制毛率（对价前）存储字段
                     SUM(DECODE(T1.LIC_CODE,'损益口径制毛率',NVL(AMOUNT,0),0)) AS MGP_RATIO_AFTER   -- 待确认制毛率（对价后）存储字段
                  FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_T T1 
                  LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
                  ON T1.SOP_CODE = T3.SOP_CODE 
                  WHERE T1.VER_CODE = V_FCST_PERIOD
                  AND T3.VERSION_CODE = V_FCST_PERIOD
                  AND UPPER(T3.SOP_TYPE) = 'FCST'
                  AND SUBSTR(T1.MON_CODE,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
                  AND T1.LIC_CODE IN( '量价设备收入', '损益口径设备收入', '量价制毛率', '损益口径制毛率')
                  AND T1.AMOUNT <> 0
                  AND T1.DIV_CODE IN(SELECT MEMBER_CODE
                                        FROM DM_FSPPUB_DIM_MEMBER_T
                                       WHERE DIM_ID =(SELECT DIM_ID
                                                        FROM DM_FSPPUB_MY_DIMENSION_T
                                                       WHERE DIM_NAME = '产业'
                                                         AND WORKSPACE_ID IN(SELECT WORKSPACE_ID
                                                                                FROM DM_FSPPUB_WORKSPACE_BASE_INFO_T
                                                                                WHERE WORKSPACE_NAME = '盈利测试'
                                                                             )
                                                     )
                                         AND LEVEL = '3')
                  GROUP BY T1.VER_CODE,
                           T1.MON_CODE,
                           T3.PHASE_DATE,
                           T1.SCE_CODE,
                           T1.BG_CODE,
                           T1.GEO_CODE,
                           T1.DIV_CODE,
                           T1.MET_CODE
	        ) T1
    ),
  TGT_LV2_HIS_TMP AS(
  -- 取TGT表的LV2表实际数据
  SELECT T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.CURRENCY,
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_REV_CONS_AFTER_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_AFTER_AMT
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_TGT_PERIOD_T T1 
      WHERE T1.VERSION_CODE = V_VERSION_CODE
      AND T1.SCENARIOS IN ('量纲分组','量纲子类')   -- 只处理有量纲的LV2实际数，无量纲的LV2处理不需要实际数
      AND SUBSTR(T1.TARGET_PERIOD,-3,3) = 'YTD'   -- 这张表只取YTD数据
      AND SUBSTR(T1.TARGET_PERIOD,1,4) = V_TGT_YEAR
	  AND T1.LV2_CODE IS NOT NULL
      AND T1.CURRENCY = 'CNY'   -- 当前需求，只取人民币
      AND T1.BG_CODE IN ('PDCG901159','PDCG901160')   -- 不取ICT的数据
  ),
  LV2_DIM_PERIOD_TMP AS(
  -- 补全维度的当年12个月月份维
  SELECT DISTINCT T2.PERIOD_ID,
         T2.PERIOD_ID||'YTD' AS TARGET_PERIOD,
         T1.SCENARIOS,
         T3.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.FCST_TYPE,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.CURRENCY
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_FCST_T T1 ,   -- 算法预测表
      (
      --生成连续月份, 当年第一月至当年12月
      SELECT CAST(TO_CHAR(ADD_MONTHS(TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-1)||'01','YYYYMM'),NUM.VAL - 1),'YYYYMM') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                      TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-1)||'01','YYYYMM'),
                                                      TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP))||'01','YYYYMM'))),
                              1) NUM(VAL)) T2,
      FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
      WHERE T1.PERIOD_ID = V_FCST_PERIOD
      AND T1.CURRENCY = 'CNY'   -- 当前需求，只取人民币
      AND SUBSTR(T1.TARGET_PERIOD,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
      AND T1.SCENARIOS <> 'LV1'   -- 不取LV1部分数据参与计算
      AND T1.DEL_FLAG = 'N'
	  AND T1.LV2_CODE IS NOT NULL   -- 取LV2编码不为空的数据
      AND T1.BG_CODE IN ('PDCG901159','PDCG901160')   -- 不取ICT的数据
      AND T3.VERSION_CODE = V_VERSION_CODE
      AND UPPER(T3.SOP_TYPE) = 'FCST'
  ),
  LV2_FCST_SUM_TMP AS (
  -- 处理实际数+预测月份YTD数据：上个月实际数+预测月份YTD数
  SELECT T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.CURRENCY,
         T3.FCST_TYPE,     
         T2.EQUIP_REV_CONS_BEFORE_AMT AS EQUIP_REV_BEFORE_AMT_HIS,
         T3.EQUIP_REV_CONS_BEFORE_AMT + T4.EQUIP_REV_CONS_BEFORE_AMT AS EQUIP_REV_BEFORE_AMT_HIS_FCST,
         T2.EQUIP_REV_CONS_AFTER_AMT AS EQUIP_REV_AFTER_AMT_HIS,
         NVL(T3.EQUIP_REV_CONS_AFTER_AMT_NODIM,T3.EQUIP_REV_CONS_AFTER_AMT + T4.EQUIP_REV_CONS_AFTER_AMT) AS EQUIP_REV_AFTER_AMT_HIS_FCST,   -- 量本价勾稽部分预测数据，无量纲LV2数据直取，有量纲LV2相加
         T2.EQUIP_COST_CONS_BEFORE_AMT AS EQUIP_COST_BEFORE_AMT_HIS,
         T3.EQUIP_COST_CONS_BEFORE_AMT + T4.EQUIP_COST_CONS_BEFORE_AMT AS EQUIP_COST_BEFORE_AMT_HIS_FCST,
         T2.EQUIP_COST_CONS_AFTER_AMT AS EQUIP_COST_AFTER_AMT_HIS,
         NVL(T3.EQUIP_COST_CONS_AFTER_AMT_NODIM,T3.EQUIP_COST_CONS_AFTER_AMT + T4.EQUIP_COST_CONS_AFTER_AMT) AS EQUIP_COST_AFTER_AMT_HIS_FCST,   -- 量本价勾稽部分预测数据，无量纲LV2数据直取，有量纲LV2相加
         T3.MGP_RATIO_AFTER_NODIM
      FROM LV2_DIM_PERIOD_TMP T1   -- 全时间维表
      LEFT JOIN TGT_LV2_HIS_TMP T2   -- TGT实际数处理数据表
      ON T1.TARGET_PERIOD = T2.TARGET_PERIOD
      AND T1.SCENARIOS = T2.SCENARIOS
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND T1.LV2_CODE = T2.LV2_CODE
      AND T1.CURRENCY = T2.CURRENCY
      LEFT JOIN ARTICULATION_LV2_FCST_TMP T3   -- 量本价勾稽后处理数据表 
      ON T1.TARGET_PERIOD = T3.TARGET_PERIOD
      AND T1.SCENARIOS = T3.SCENARIOS
      AND T1.BG_CODE = T3.BG_CODE
      AND T1.OVERSEA_CODE = T3.OVERSEA_CODE
      AND T1.LV2_CODE = T3.LV2_CODE
      AND T1.CURRENCY = T3.CURRENCY_CODE
      AND T1.PHASE_DATE = T3.PHASE_DATE
      LEFT JOIN ( 
                 SELECT TARGET_PERIOD,
                        SCENARIOS,
                        BG_CODE,
                        OVERSEA_CODE,
                        LV1_CODE,
                        LV2_CODE,
                        CURRENCY,
                        EQUIP_REV_CONS_BEFORE_AMT,
                        EQUIP_REV_CONS_AFTER_AMT,
                        EQUIP_COST_CONS_BEFORE_AMT,
                        EQUIP_COST_CONS_AFTER_AMT
                    FROM TGT_LV2_HIS_TMP
                    WHERE SUBSTR(TARGET_PERIOD,1,6) = V_LAST_PERIOD  -- 只取上个月历史YTD数据
                 ) T4
      ON T3.SCENARIOS = T4.SCENARIOS
      AND T3.BG_CODE = T4.BG_CODE
      AND T3.OVERSEA_CODE = T4.OVERSEA_CODE
      AND T3.LV2_CODE = T4.LV2_CODE
      AND T3.CURRENCY_CODE = T4.CURRENCY
    )
  SELECT V_VERSION_CODE AS VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.CURRENCY,
         T1.FCST_TYPE,
         NVL(T2.EQUIP_REV_BEFORE_AMT_HIS,T2.EQUIP_REV_BEFORE_AMT_HIS_FCST) AS EQUIP_REV_CONS_BEFORE_AMT,
         NVL(T2.EQUIP_COST_BEFORE_AMT_HIS,T2.EQUIP_COST_BEFORE_AMT_HIS_FCST) AS EQUIP_COST_CONS_BEFORE_AMT,
         NVL(T2.EQUIP_REV_AFTER_AMT_HIS,T2.EQUIP_REV_AFTER_AMT_HIS_FCST) AS EQUIP_REV_CONS_AFTER_AMT,
         NVL(T2.EQUIP_COST_AFTER_AMT_HIS,T2.EQUIP_COST_AFTER_AMT_HIS_FCST) AS EQUIP_COST_CONS_AFTER_AMT,
         1-(NVL(T2.EQUIP_COST_BEFORE_AMT_HIS,T2.EQUIP_COST_BEFORE_AMT_HIS_FCST)/NVL(T2.EQUIP_REV_BEFORE_AMT_HIS,T2.EQUIP_REV_BEFORE_AMT_HIS_FCST)) AS MGP_RATIO_BEFORE,
         NVL(T2.MGP_RATIO_AFTER_NODIM,1-(NVL(T2.EQUIP_COST_AFTER_AMT_HIS,T2.EQUIP_COST_AFTER_AMT_HIS_FCST)/NVL(T2.EQUIP_REV_AFTER_AMT_HIS,T2.EQUIP_REV_AFTER_AMT_HIS_FCST))) AS MGP_RATIO_AFTER,
         T3.MCA_ADJUST_RATIO_FCST,
         T3.MGP_ADJUST_RATIO_FCST,
         '' AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM LV2_DIM_PERIOD_TMP T1
      LEFT JOIN LV2_FCST_SUM_TMP T2 
      ON T1.TARGET_PERIOD = T2.TARGET_PERIOD
      AND T1.SCENARIOS = T2.SCENARIOS
      AND T1.PHASE_DATE = T2.PHASE_DATE
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND T1.LV2_CODE = T2.LV2_CODE
      AND T1.CURRENCY = T2.CURRENCY
      AND T1.FCST_TYPE = T2.FCST_TYPE
      LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_FCST_T T3
      ON SUBSTR(T1.TARGET_PERIOD,1,6) = T3.TARGET_PERIOD
      AND T1.SCENARIOS = T3.SCENARIOS
      AND T1.BG_CODE = T3.BG_CODE
      AND T1.OVERSEA_CODE = T3.OVERSEA_CODE
      AND T1.LV1_CODE = T3.LV1_CODE
      AND T1.LV2_CODE = T3.LV2_CODE
      AND T1.CURRENCY = T3.CURRENCY
      AND T1.FCST_TYPE = T3.FCST_TYPE
      WHERE T3.PERIOD_ID = V_FCST_PERIOD
      AND T3.BG_CODE IN ('PDCG901159','PDCG901160')   -- 不取ICT的数据
      AND T3.DEL_FLAG = 'N';
  DBMS_OUTPUT.PUT_LINE('LV2层级数据插入结果表'); 
  
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_LV1_ARTICULATED_AGGR_T';
  
  -----------------------------------------------------------------------量纲层级数据插入结果表------------------------------------------------------------------------
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         LV2_CODE,
         LV2_NAME,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,
         DIMENSION_SUBCATEGORY_CN_NAME,
         DIMENSION_SUBCATEGORY_EN_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         CARRYOVER_QTY,
         SHIP_QTY, 
         REV_PERCENT,
         UNIT_PRICE,
         UNIT_COST,
         MGP_RATIO,
         CARRYOVER_RATE,
         UNIT_PRICE_FCST_UPPER,
         UNIT_PRICE_FCST_LOWER,
         UNIT_COST_FCST_UPPER,
         UNIT_COST_FCST_LOWER,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH SUM_NODIM_REV_TMP AS(
  -- RAN下无量纲数据求和
  SELECT T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.OVERSEA_CODE,
         T1.LV1_CODE,
         'RAN' AS LV2_CODE,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         SUM( T1.EQUIP_REV_CONS_BEFORE_AMT) AS RAN_SUM_REV_AMT,   -- 分子：RAN下无量纲数据求和
         SUM(SUM( T1.EQUIP_REV_CONS_BEFORE_AMT)) OVER(PARTITION BY T1.TARGET_PERIOD,T1.SCENARIOS,T1.PHASE_DATE,T1.BG_CODE,T1.OVERSEA_CODE,T1.LV1_CODE,T1.CURRENCY_CODE,T1.FCST_TYPE) AS RAN_LV2_SUM_REV_AMT   -- 分母：RAN下LV2数据求和
      FROM FOP_DIMENSION_RESULT_TMP T1
      WHERE T1.LV2_CODE IN ( '153324','153326','133661','101212')   -- 无线下LV2为RAN：'5G&LTE FDD','5G&LTE TDD','GUC','SRAN'
      GROUP BY T1.TARGET_PERIOD,
               T1.SCENARIOS,
               T1.PHASE_DATE,
               T1.BG_CODE,
               T1.OVERSEA_CODE,
               T1.LV1_CODE,
               T1.DIMENSION_GROUP_CODE,
               T1.DIMENSION_SUBCATEGORY_CODE,
               T1.CURRENCY_CODE,
               T1.FCST_TYPE
  )
  SELECT V_VERSION_CODE AS VERSION_CODE,
         T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.LV2_CODE,
         T1.LV2_NAME,
         T1.DIMENSION_GROUP_CODE,
         T1.DIMENSION_GROUP_CN_NAME,
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,
         T1.DIMENSION_SUBCATEGORY_CN_NAME,
         T1.DIMENSION_SUBCATEGORY_EN_NAME,
         T1.CURRENCY_CODE,
         T1.FCST_TYPE,
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.CARRYOVER_QTY,
         T1.SHIP_QTY, 
         CASE WHEN T1.LV2_CODE IN ('153324','153326','133661','101212') AND (T1.DIMENSION_GROUP_CODE ='NODIM' OR T1.DIMENSION_SUBCATEGORY_CODE = 'NOSUB')
              THEN T2.RAN_SUM_REV_AMT/NULLIF(T2.RAN_LV2_SUM_REV_AMT,0)
              WHEN T1.REV_PERCENT IS NOT NULL 
              THEN T1.REV_PERCENT
              ELSE T1.EQUIP_REV_CONS_BEFORE_AMT/NULLIF(T3.EQUIP_REV_CONS_BEFORE_AMT,0)
         END AS REV_PERCENT,
         T1.UNIT_PRICE,
         T1.UNIT_COST,
         NVL(T1.MGP_RATIO,1-(T1.EQUIP_COST_CONS_BEFORE_AMT/NULLIF(T1.EQUIP_REV_CONS_BEFORE_AMT,0))) AS MGP_RATIO,
         T1.CARRYOVER_RATE,
         T1.UNIT_PRICE_FCST_UPPER,
         T1.UNIT_PRICE_FCST_LOWER,
         T1.UNIT_COST_FCST_UPPER,
         T1.UNIT_COST_FCST_LOWER,
         NULL AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FOP_DIMENSION_RESULT_TMP T1 
      LEFT JOIN (SELECT * FROM SUM_NODIM_REV_TMP WHERE (DIMENSION_GROUP_CODE ='NODIM' OR DIMENSION_SUBCATEGORY_CODE = 'NOSUB')) T2 
      ON T1.TARGET_PERIOD = T2.TARGET_PERIOD
      AND T1.SCENARIOS = T2.SCENARIOS
      AND T1.PHASE_DATE = T2.PHASE_DATE
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND T1.LV2_CODE = T2.LV2_CODE
      AND NVL(T1.DIMENSION_GROUP_CODE,'S1') = NVL(T2.DIMENSION_GROUP_CODE,'S1')
      AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S2') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S2')
      AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
      AND T1.FCST_TYPE = T2.FCST_TYPE
      LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T T3
      ON T1.TARGET_PERIOD = T3.TARGET_PERIOD
      AND T1.SCENARIOS = T3.SCENARIOS
      AND T1.PHASE_DATE = T3.PHASE_DATE
      AND T1.BG_CODE = T3.BG_CODE
      AND T1.OVERSEA_CODE = T3.OVERSEA_CODE
      AND T1.LV1_CODE = T3.LV1_CODE
      AND T1.LV2_CODE = T3.LV2_CODE
      AND T1.CURRENCY_CODE = T3.CURRENCY_CODE
      AND T1.FCST_TYPE = T3.FCST_TYPE;
  DBMS_OUTPUT.PUT_LINE('量纲层级数据插入结果表'); 
  
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_YTD_DIMENSION_ARTICULATED_AGGR_T';

  ------------------------------------------------------------------------LV1层级逻辑处理----------------------------------------------------------------------------
  -- 将数据插入LV2结果表
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T(
         VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY_CODE,
         FCST_TYPE,
         EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         MGP_RATIO_AFTER,
         SOURCE_TABLE,
         REMARK,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  WITH FCST_LV1_SUM_TMP AS(
  -- 取量本价勾稽后LV1预测数据
  SELECT T1.MON_CODE||'YTD' AS TARGET_PERIOD,   -- 假设MON_CODE存储为202506
         T1.SCE_CODE AS SCENARIOS,
         T3.PHASE_DATE,
         T1.BG_CODE,
         T1.GEO_CODE AS OVERSEA_CODE,
         T1.DIV_CODE AS LV1_CODE,
         'CNY' AS CURRENCY_CODE,
         T1.MET_CODE AS FCST_TYPE,
         SUM(SUM(DECODE(T1.LIC_CODE,'损益口径设备收入',NVL(T1.AMOUNT,0),0))) OVER(PARTITION BY SUBSTR(T1.MON_CODE,1,4),T1.SCE_CODE,T3.PHASE_DATE,T1.BG_CODE,T1.GEO_CODE,T1.DIV_CODE,T1.MET_CODE ORDER BY T1.MON_CODE) AS EQUIP_REV_CONS_AFTER_AMT,   -- 设备收入（对价后）
         SUM(T2.EQUIP_COST_CONS_AFTER_AMT) AS EQUIP_COST_CONS_AFTER_AMT   -- 关联LV2表，已经整合后的YTD数据表，取得设备成本（对价后）数据，卷积为LV1层级
         FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_T T1 
         LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_YTD_LV2_ARTICULATED_AGGR_T T2   -- 关联LV2表，已经整合后的YTD数据表，取得设备成本（对价后）数据，卷积为LV1层级
         ON T1.MON_CODE = SUBSTR(T2.TARGET_PERIOD,1,6)
--         AND T1.SCE_CODE = T2.SCENARIOS
         AND T1.DIV_CODE = T2.LV1_CODE
         AND T1.BG_CODE = T2.BG_CODE
         AND T1.GEO_CODE = T2.OVERSEA_CODE
         AND T1.MET_CODE = T2.FCST_TYPE
         LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
         ON T1.SOP_CODE = T3.SOP_CODE 
         WHERE T1.VER_CODE = V_FCST_PERIOD
         AND T3.VERSION_CODE = V_FCST_PERIOD
         AND UPPER(T3.SOP_TYPE) = 'FCST'
         AND T1.LIC_CODE IN('损益口径设备收入')
         AND T1.AMOUNT <> 0
         AND T1.DIV_CODE IN(SELECT MEMBER_CODE
                              FROM DM_FSPPUB_DIM_MEMBER_T
                              WHERE DIM_ID =(SELECT DIM_ID
                                                FROM DM_FSPPUB_MY_DIMENSION_T
                                               WHERE DIM_NAME = '产业'
                                                 AND WORKSPACE_ID IN(
                                                                     SELECT WORKSPACE_ID
                                                                         FROM DM_FSPPUB_WORKSPACE_BASE_INFO_T
                                                                         WHERE WORKSPACE_NAME = '盈利测试'
                                                                         )
                                            )
                           AND LEVEL = '2')
         AND SUBSTR(T1.MON_CODE,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
         GROUP BY T1.MON_CODE,
                  T3.PHASE_DATE,
                  T1.SCE_CODE,
                  T1.BG_CODE,
                  T1.GEO_CODE,
                  T1.DIV_CODE,
                  T1.MET_CODE
  ),
  LV1_DIM_PERIOD_TMP AS(
  -- 补全维度的当年12个月月份维
  SELECT DISTINCT T2.PERIOD_ID,
         T2.PERIOD_ID||'YTD' AS TARGET_PERIOD,
         T1.SCENARIOS,
         T3.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.FCST_TYPE,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.CURRENCY
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_FCST_T T1 ,   -- 算法预测表
      (
      --生成连续月份, 当年第一月至当年12月
      SELECT CAST(TO_CHAR(ADD_MONTHS(TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-1)||'01','YYYYMM'),NUM.VAL - 1),'YYYYMM') AS BIGINT)
          AS PERIOD_ID
        FROM GENERATE_SERIES(1,
                              TO_NUMBER(TIMESTAMPDIFF(MONTH,
                                                      TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP)-1)||'01','YYYYMM'),
                                                      TO_DATE(TO_CHAR(YEAR(CURRENT_TIMESTAMP))||'01','YYYYMM'))),
                              1) NUM(VAL)) T2,
      FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
      WHERE T1.PERIOD_ID = V_FCST_PERIOD
      AND T1.CURRENCY = 'CNY'   -- 当前需求，只取人民币
      AND SUBSTR(T1.TARGET_PERIOD,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
      AND T1.LV2_CODE IS NULL   -- 取LV1部分数据参与计算
      AND T1.BG_CODE IN ('PDCG901159','PDCG901160')   -- 不取ICT的数据
      AND T1.DEL_FLAG = 'N'
      AND T3.VERSION_CODE = V_VERSION_CODE
      AND UPPER(T3.SOP_TYPE) = 'FCST'
  ),
  TGT_FCST_LV1_TMP AS(
  -- 取TGT表的LV2表实际数据
  SELECT T1.PERIOD_ID,
         T1.TARGET_PERIOD,
         T1.SCENARIOS,
         T1.PHASE_DATE,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_CODE,
         T1.LV1_NAME,
         T1.CURRENCY,
         T1.FCST_TYPE,
         T2.EQUIP_REV_CONS_AFTER_AMT AS EQUIP_REV_AFTER_AMT_HIS,
         T2.EQUIP_REV_CONS_AFTER_AMT + T3.EQUIP_REV_CONS_AFTER_AMT AS EQUIP_REV_AFTER_AMT_HIS_FCST,
         T3.EQUIP_COST_CONS_AFTER_AMT
      FROM LV1_DIM_PERIOD_TMP T1
	  INNER JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_LV2_TGT_PERIOD_T T2 
      ON T1.TARGET_PERIOD = T2.TARGET_PERIOD
      AND T1.SCENARIOS = T2.SCENARIOS
      AND T1.BG_CODE = T2.BG_CODE
      AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
      AND T1.LV1_CODE = T2.LV1_CODE
      AND T1.CURRENCY = T2.CURRENCY
	  INNER JOIN FCST_LV1_SUM_TMP T3
      ON T1.TARGET_PERIOD = T3.TARGET_PERIOD
      AND T1.SCENARIOS = T3.SCENARIOS
      AND T1.BG_CODE = T3.BG_CODE
      AND T1.OVERSEA_CODE = T3.OVERSEA_CODE
      AND T1.LV1_CODE = T3.LV1_CODE
      AND T1.CURRENCY = T3.CURRENCY_CODE
      AND T1.PHASE_DATE = T3.PHASE_DATE
      AND T1.FCST_TYPE = T3.FCST_TYPE
      WHERE T2.VERSION_CODE = V_VERSION_CODE
      AND T2.LV2_CODE IS NULL   -- 只处理LV1的实际数
      AND SUBSTR(T2.TARGET_PERIOD,-3,3) = 'YTD'   -- 这张表只取YTD数据
      AND SUBSTR(T2.TARGET_PERIOD,1,4) = V_TGT_YEAR
      AND T2.CURRENCY = 'CNY'   -- 当前需求，只取人民币
      AND T2.BG_CODE IN ('PDCG901159','PDCG901160')   -- 不取ICT的数据
  )
  SELECT V_VERSION_CODE AS VERSION_CODE,
         PERIOD_ID,
         TARGET_PERIOD,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_CODE,
         LV1_NAME,
         CURRENCY,
         FCST_TYPE,
         NVL(EQUIP_REV_AFTER_AMT_HIS,EQUIP_REV_AFTER_AMT_HIS_FCST) AS EQUIP_REV_CONS_AFTER_AMT,
         EQUIP_COST_CONS_AFTER_AMT,
         1-(EQUIP_COST_CONS_AFTER_AMT/NVL(EQUIP_REV_AFTER_AMT_HIS,EQUIP_REV_AFTER_AMT_HIS_FCST)) AS MGP_RATIO_AFTER,
         'DM_FOP_DIMENSION_ARTICULATION_T+DM_FOP_YTD_LV2_ARTICULATED_AGGR_T+DM_FOP_DIMENSION_LV2_TGT_PERIOD_T' AS SOURCE_TABLE,
         NULL AS REMARK,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM TGT_FCST_LV1_TMP;
  DBMS_OUTPUT.PUT_LINE('LV1层级数据插入结果表'); 
  
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_YTD_LV1_ARTICULATED_AGGR_T';
  
  RETURN 'SUCCESS';
  
END$$
/
