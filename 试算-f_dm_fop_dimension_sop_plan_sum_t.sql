CREATE OR REPLACE FUNCTION fin_dm_opt_fop.f_dm_fop_dimension_sop_plan_sum_t(p_version_code character varying DEFAULT NULL::character varying, OUT x_success_flag text)
 RETURNS pg_catalog.text AS $BODY$
 /*
创建时间：2025-06-10
创建人  ：朱雅欣
背景描述：盈利量纲SOP计划量汇总表,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_sop_plan_sum_t();
*/
 
 declare
	v_sp_name varchar(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_sop_plan_sum_t('''||p_version_code||')';
	v_tbl_name varchar(100) := 'fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t';
	v_version_code varchar(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_num bigint := 0; --步骤号
    v_dml_row_count number default 0 ;

begin
	x_success_flag := '1';                                 --1表示成功
	
	
	         -- 如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select to_char(current_date,'yyyymm') as version_code into v_version_code ;	
        end if 
        ;	
		
	 --1.开始日志
  v_step_num := v_step_num + 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '盈利量纲SOP计划量汇总表'||v_tbl_name||',目标表中'||'本编码:'||v_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
  
  

		
		drop table if exists product_tmp;
	     create temporary table  product_tmp
		          as
		select distinct 
              lv1_prod_rnd_team_code
              ,lv1_prod_rd_team_cn_name
              ,lv1_prod_rd_team_en_name
              ,lv2_prod_rnd_team_code
              ,lv2_prod_rd_team_cn_name
              ,lv2_prod_rd_team_en_name
		from  dmdim.dm_dim_product_d
		where lv1_prod_rnd_team_code in ('100001'     --无线
                                          ,'134557'     --光
                                          ,'101775'     --数据存储
                                          ,'137565'     --数据通信
                                          ,'133277'     --计算
                                          ,'100011')    --云核心网
										  ;

		
		-- sop计划量数据表关联产品维表和量纲维表，获取重量级团队code和量纲分组
		 drop table if exists sop_plan_tmp;
	     create temporary table  sop_plan_tmp
		          as
	     select cast(t1.month as numeric)  as period_id
		       ,replace(t1.period,'/','') as phase_date                                                                                      
               ,t1.period_flag                                                                                      
               ,case when t1.bg_cn  = '运营商' then 'PDCG901159'                                                          
                     when t1.bg_cn  = '政企'   then 'PDCG901160'		                                                    
                end as bg_code				                                                                        
               ,t1.bg_cn as bg_name                                                                                                                                                                                       
               ,t1.measure_code                                                                                     
               ,t2.lv1_prod_rnd_team_code			                                                                
               ,t1.lv1_org_cn  as lv1_prod_rd_team_cn_name                                                           
               ,t2.lv2_prod_rnd_team_code			                                                                
               ,t1.lv2_org_cn  as lv2_prod_rd_team_cn_name 
               ,t1.cn_dimension_group_l2_code
               ,t1.cn_dimension_group_l2  
               ,t3.product_dimension_group_code    as dimension_group_code
               ,t3.product_dimension_group         as dimension_group_cn_name
               ,t3.product_dimension_group_en_name as dimension_group_en_name                                             
               ,t1.country_measure   as oversea_desc                                                                               
               ,t1.dimension_subcategory_code                                                                       
               ,t1.dimension_subcategory_cn_name                                                                    
               ,t1.dimension_subcategory_en_name  
			  -- ,t1.demision_flag
               ,sum(t1.qty) as snop_quantity                                                                        
           from  fin_dm_opt_fop.dwk_ps_sop_plan_country_measure_i t1                                                
		   join  product_tmp t2                                                                                
		    on t1.lv1_org_cn = 	t2.lv1_prod_rd_team_cn_name                                                                                 
		   and t1.lv2_org_cn = 	t2.lv2_prod_rd_team_cn_name                                                                            
		   left join fin_dm_opt_fop.dwr_dim_productdimension_d t3                                                                            
		   on t1.dimension_key = t3.dimension_key                                                                                                                                       
           where 1=1   
          and case when replace(t1.period,'/','') LIKE'%-%' 
		            then substring(t1.period,position('-' in t1.period)+1) >= to_char(current_date - interval'1month','yyyymm')			            
					else replace(t1.period,'/','')  >= to_char(add_months(current_date,-1),'yyyymm')||'06'   --上个月6号
	                 and replace(t1.period,'/','')  < to_char(add_months(current_date,1),'yyyymm')||'06'     --这个月6号	 
                    end 				
		   and t1.bg_cn in ('运营商','政企')                                                                             
		   and t1.measure_code in ( '滚动S&OP计划','产品线年度规划')                                                     
		   and case when t1.measure_code = '滚动S&OP计划'                                                                
		            then t1.period_flag = '月调整期次'                                                                              
			   else t1.period_flag = 'N'                                                                                              
			    end                                                                                                                     
		    group by t1.period                                                                                           
               ,t1.period_flag                                                                                      
               ,case when t1.bg_cn  = '运营商' then 'PDCG901159'                                                          
                     when t1.bg_cn  = '政企'   then 'PDCG901160'		                                                    
                end  			                                                                        
               ,t1.bg_cn                                                                                            
               ,t1.month                                                                                            
               ,t1.measure_code                                                                                     
               ,t2.lv1_prod_rnd_team_code			                                                                
               ,t1.lv1_org_cn                                                  
               ,t2.lv2_prod_rnd_team_code			                                                                
               ,t1.lv2_org_cn                                                                                                                                                                                                                                                                                                                             
               ,t1.cn_dimension_group_l2_code
               ,t1.cn_dimension_group_l2  
               ,t3.product_dimension_group_code
               ,t3.product_dimension_group 
               ,t3.product_dimension_group_en_name                           
               ,t1.country_measure                                                                                  
               ,t1.dimension_subcategory_code                                                                       
               ,t1.dimension_subcategory_cn_name                                                                    
               ,t1.dimension_subcategory_en_name  
			   --,t1.demision_flag              
               ;	
			   
			   -- 分国内海外分政企的数据（中国区CNBG   是量纲分组l2 ，其余的用量纲分组）
		 drop table if exists cnbg_tmp;
	     create temporary table  cnbg_tmp
		          as
         select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name  
               ,oversea_desc			   
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name     
               ,case when t1.bg_name = '运营商' and oversea_desc = '国内' 
                     then t1.cn_dimension_group_l2_code    
	                 else t1.dimension_group_code
                     end as dimension_group_code                                          
               ,case when t1.bg_name = '运营商' and oversea_desc = '国内' 
                     then t1.cn_dimension_group_l2           
	                 else t1.dimension_group_cn_name         
		              end as dimension_group_cn_name                                       
               ,case when t1.bg_name = '运营商' and oversea_desc = '国内' 
                     then null          
	                 else t1.dimension_group_en_name 
		             end as dimension_group_en_name       			                                                                                                                                      
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,sum(snop_quantity) as  snop_quantity     			   
           from  sop_plan_tmp t1  	
           group by period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name  
               ,oversea_desc			   
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name     
               ,case when t1.bg_name = '运营商' and oversea_desc = '国内' 
                     then t1.cn_dimension_group_l2_code    
	                 else t1.dimension_group_code
                     end                                        
               ,case when t1.bg_name = '运营商' and oversea_desc = '国内' 
                     then t1.cn_dimension_group_l2           
	                 else t1.dimension_group_cn_name         
		              end                                        
               ,case when t1.bg_name = '运营商' and oversea_desc = '国内' 
                     then null          
	                 else t1.dimension_group_en_name 
		             end      			                                                                                                                                      
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  		   
               ;	
			    			   
			    -- 将国内和海外数据合并，获得全球的数据 (全球的量纲分组都是用量纲分组，不需要中国区l2量纲分组) 
		 drop table if exists oversea_desc_tmp;
	     create temporary table  oversea_desc_tmp
		          as
			    select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name 
               ,dimension_group_en_name
               ,'全球' as oversea_desc			   
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,sum(snop_quantity) as snop_quantity                                                                                     
           from  sop_plan_tmp  
           group by period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name                                                                                                                             
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
			   union all
			   -- 区分国内海外（量纲分组，不需要中国区l2量纲分组，用于后面计算不分BG的数据）
                select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name                                             
               ,oversea_desc                                                                               
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,sum(snop_quantity) as snop_quantity                                                                                     
           from  sop_plan_tmp  
           group by period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name                                              
               ,oversea_desc                                                                               
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
		          ;
                
                -- 	将 运营商和政企 合成ICT的数据(ICT分BG、ICT不分BG)
  				drop table if exists bg_tmp;
	            create temporary table  bg_tmp
		          as
	            select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,'RICT001' as bg_code				                                                                        
               ,'ICT' as bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name                                               
               ,oversea_desc                                                                               
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,sum(snop_quantity) as snop_quantity                                                                                     
           from  oversea_desc_tmp  
           group by period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                                                                                                                                                                                                            
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name                                               
               ,oversea_desc                                                                               
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
		          ;
				  
				 -- 将四种数据（分国内海外分BG,不区分国内海外分BG，分国内海外不分BG，不区分国内海外不分BG）
				drop table if exists all_tmp;
	            create temporary table  all_tmp
		          as
		    select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name
               ,oversea_desc			   
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,snop_quantity                                                                                     
           from  cnbg_tmp  
		   union all
		   select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name
               ,oversea_desc			   
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,snop_quantity                                                                                     
           from  oversea_desc_tmp
           where oversea_desc = '全球' 
             union all 		   
			select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name
               ,oversea_desc			   
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,snop_quantity                                                                                     
           from  bg_tmp	  
		   ;
		   
		   -- 打 场景标签： 量纲分组、量纲子类 
		   drop table if exists scenarios_tmp;
	       create temporary table  scenarios_tmp
		     as
			 select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
			   ,'量纲分组' as scenarios
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name
               ,oversea_desc
               ,null as dimension_subcategory_code                                                                       
               ,null as dimension_subcategory_cn_name                                                                    
               ,null as dimension_subcategory_en_name  			   
               ,sum(snop_quantity) as snop_quantity                                                                                     
           from  all_tmp	  
		    where lv1_prod_rnd_team_code in ('134557','133277','137565')	   /*光、计算、数据通信*/
			group by period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name
               ,oversea_desc			   
			union all
			 select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
			   ,'量纲子类' as scenarios
               ,dimension_group_code
               ,dimension_group_cn_name
               ,dimension_group_en_name
               ,oversea_desc			   
               ,dimension_subcategory_code                                                                       
               ,dimension_subcategory_cn_name                                                                    
               ,dimension_subcategory_en_name  
               ,snop_quantity                                                                                     
           from  all_tmp	  
		    where lv1_prod_rnd_team_code in ('100001','101775')	   /*无线、数据存储*/
			union all
			select period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
			   ,'LV2' as scenarios
			   ,null as dimension_group_code
               ,null as dimension_group_cn_name
               ,null as dimension_group_en_name
               ,oversea_desc		
               ,null as dimension_subcategory_code                                                                       
               ,null as dimension_subcategory_cn_name                                                                    
               ,null as dimension_subcategory_en_name 			   
               ,sum(snop_quantity) as snop_quantity                                                                                     
           from  all_tmp	  
		    where lv1_prod_rnd_team_code in ('100011')	   /*云核心网*/
			group by period_id
		       ,phase_date                                                                                      
               ,period_flag                                                                                      
               ,bg_code				                                                                        
               ,bg_name                                                                                                                                                                                       
               ,measure_code                                                                                     
               ,lv1_prod_rnd_team_code			                                                                
               ,lv1_prod_rd_team_cn_name                                                            
               ,lv2_prod_rnd_team_code			                                                                
               ,lv2_prod_rd_team_cn_name  
               ,oversea_desc			   
			;
		
	    /**********************************************仅试算期间使用的数据begin*****************************************************************/	
		 /****试算实际数发货量取值范围：202401-202412*******/	
		  drop table if exists ship_qty_tmp;
	       create temporary table  ship_qty_tmp
		     as
		 select version_code
	     ,time_window_code                                       
         ,period_id  
         ,'试算' as phase_date   		 
         ,bg_code       
         ,bg_name 
         ,oversea_code
         ,oversea_desc
         ,lv1_prod_rnd_team_code                       
         ,lv1_prod_rd_team_cn_name 	 
         ,lv2_prod_rnd_team_code         
         ,lv2_prod_rd_team_cn_name                                                                 			   
         ,scenarios	                      /*场景*/		 
         ,dimension_group_code            /*量纲分组*/                                      
         ,dimension_group_cn_name         /*量纲中文名*/                                   
         ,dimension_group_en_name 	      /*量纲英文名*/   
         ,dimension_subcategory_code	  /*量纲子类*/	   
         ,dimension_subcategory_cn_name   /*量纲子类中文名*/
         ,dimension_subcategory_en_name   /*量纲子类英文名*/                                                                                  
         ,ship_qty  as snop_quantity      /*发货量（历史）作为sop发货量用于试算 */  
	 from fin_dm_opt_fop.dm_fop_dimension_carryover_sum_t
	 where version_code = v_version_code
	 and period_id >=202401
	 and period_id <=202412
	 ;
	  -- 将YTD的数据处理成月度数据
	  drop table if exists MONTH_tmp;
	  create temporary table  MONTH_tmp
		     as
	 select t1.version_code
	     ,'MONTH' as time_window_code                                       
         ,t1.period_id as period_id
         ,t2.period_id as last_period		 
         ,'试算' as phase_date   		 
         ,t1.bg_code       
         ,t1.bg_name 
         ,t1.oversea_code
         ,t1.oversea_desc
         ,t1.lv1_prod_rnd_team_code                       
         ,t1.lv1_prod_rd_team_cn_name 	 
         ,t1.lv2_prod_rnd_team_code         
         ,t1.lv2_prod_rd_team_cn_name                                                                 			   
         ,t1.scenarios	                      /*场景*/		 
         ,t1.dimension_group_code            /*量纲分组*/                                      
         ,t1.dimension_group_cn_name         /*量纲中文名*/                                   
         ,t1.dimension_group_en_name 	      /*量纲英文名*/   
         ,t1.dimension_subcategory_code	  /*量纲子类*/	   
         ,t1.dimension_subcategory_cn_name   /*量纲子类中文名*/
         ,t1.dimension_subcategory_en_name   /*量纲子类英文名*/ 
         ,t1.snop_quantity as this_month
         ,t2.snop_quantity as last_month	 
         ,(t1.snop_quantity - coalesce(t2.snop_quantity,0))  as snop_quantity      /*发货量（历史）作为sop发货量用于试算 */  
	 from ship_qty_tmp t1    /*取当月数据*/
	 left join ship_qty_tmp t2/*取上月数据*/
	 on  1=1 
     and t1.period_id=  t2.period_id +1                       
     and coalesce(t1.bg_code                       ,'SNULL') = coalesce(t2.bg_code                       ,'SNULL')
     and coalesce(t1.bg_name                       ,'SNULL') = coalesce(t2.bg_name                       ,'SNULL')
     and coalesce(t1.oversea_code                  ,'SNULL') = coalesce(t2.oversea_code                  ,'SNULL')
     and coalesce(t1.oversea_desc                  ,'SNULL') = coalesce(t2.oversea_desc                  ,'SNULL')
     and coalesce(t1.lv1_prod_rnd_team_code        ,'SNULL') = coalesce(t2.lv1_prod_rnd_team_code        ,'SNULL')               
     and coalesce(t1.lv1_prod_rd_team_cn_name 	   ,'SNULL') = coalesce(t2.lv1_prod_rd_team_cn_name 	 ,'SNULL')
     and coalesce(t1.lv2_prod_rnd_team_code        ,'SNULL') = coalesce(t2.lv2_prod_rnd_team_code        ,'SNULL') 
     and coalesce(t1.lv2_prod_rd_team_cn_name      ,'SNULL') = coalesce(t2.lv2_prod_rd_team_cn_name      ,'SNULL')                                                           			   
     and coalesce(t1.scenarios	                   ,'SNULL') = coalesce(t2.scenarios	                 ,'SNULL')   
     and coalesce(t1.dimension_group_code          ,'SNULL') = coalesce(t2.dimension_group_code          ,'SNULL')                                 
     and coalesce(t1.dimension_group_cn_name       ,'SNULL') = coalesce(t2.dimension_group_cn_name       ,'SNULL')                                
     and coalesce(t1.dimension_group_en_name 	   ,'SNULL') = coalesce(t2.dimension_group_en_name 	     ,'SNULL')   
     and coalesce(t1.dimension_subcategory_code	   ,'SNULL') = coalesce(t2.dimension_subcategory_code    ,'SNULL')
     and coalesce(t1.dimension_subcategory_cn_name ,'SNULL') = coalesce(t2.dimension_subcategory_cn_name ,'SNULL')  
     and coalesce(t1.dimension_subcategory_en_name ,'SNULL') = coalesce(t2.dimension_subcategory_en_name ,'SNULL')  
	    ;
	-- 造6个期次的数据
	  drop table if exists phase_tmp;
	  create temporary table  phase_tmp
		     as	
	select  '202407' as version_code
	  , time_window_code
      ,period_id
      ,'试算202407' as phase_date
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
	  from MONTH_tmp
	  where period_id >= 202407
	  union all
	  select  '202408' as version_code
	  , time_window_code
      ,period_id
      ,'试算202408' as phase_date
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
	  from MONTH_tmp
	  where period_id >= 202408
	   union all
	  select  '202409' as version_code
	  , time_window_code
      ,period_id
      ,'试算202409' as phase_date
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
	  from MONTH_tmp
	  where period_id >= 202409
	   union all
	  select  '202410' as  version_code
	  , time_window_code
      ,period_id
      ,'试算202410' as phase_date
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
	  from MONTH_tmp
	  where period_id >= 202410
	   union all
	  select  '202411' as version_code
	  , time_window_code
      ,period_id
      ,'试算202411' as phase_date
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
	  from MONTH_tmp
	  where period_id >= 202411
	   union all
	  select  '202412' as version_code
	  , time_window_code
      ,period_id
      ,'试算202412' as phase_date
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
	  from MONTH_tmp
	  where period_id >= 202412
	  ;
	
	
	  /**********************************************仅试算期间使用的数据end*****************************************************************/	

            -- 删除最大版本数据
      delete fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t where version_code = v_version_code;
	  
	  insert into fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t(	
       version_code
     ,time_window_code
     ,period_id
     ,phase_date
     ,bg_code
     ,bg_name
     ,oversea_code
     ,oversea_desc
     ,lv1_prod_rnd_team_code
     ,lv1_prod_rd_team_cn_name
     ,lv2_prod_rnd_team_code
     ,lv2_prod_rd_team_cn_name
     ,scenarios
     ,dimension_group_code   
     ,dimension_group_cn_name        
     ,dimension_group_en_name
     ,dimension_subcategory_code    
     ,dimension_subcategory_cn_name 
     ,dimension_subcategory_en_name 
     ,snop_quantity
     ,source_table
     ,remark
     ,created_by
     ,creation_date
     ,last_updated_by
     ,last_update_date
     ,del_flag
	  )
	  select v_version_code as version_code
	  ,'MONTH' as time_window_code
      ,period_id
      ,phase_date
      ,bg_code
      ,bg_name
	  ,case when oversea_desc = '国内' then 'GH0002' 
	        when oversea_desc = '海外' then 'GH0003'
			else 'GH0001'
 			end as oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
      ,'dwk_ps_sop_plan_country_measure_i' as source_table
      ,'' as remark
 	  , -1 as created_by
 	  , current_timestamp as creation_date
 	  , -1 as last_updated_by
 	  , current_timestamp as last_update_date
 	  , 'N' as del_flag
	  from scenarios_tmp
	  union all
	  select  version_code
	  , time_window_code
      ,period_id
      ,phase_date
      ,bg_code
      ,bg_name
	  ,oversea_code
      ,oversea_desc
      ,lv1_prod_rnd_team_code
      ,lv1_prod_rd_team_cn_name
      ,lv2_prod_rnd_team_code
      ,lv2_prod_rd_team_cn_name
	  ,scenarios
      ,dimension_group_code   
      ,dimension_group_cn_name        
      ,dimension_group_en_name
      ,dimension_subcategory_code    
      ,dimension_subcategory_cn_name 
      ,dimension_subcategory_en_name 
      ,snop_quantity
      ,'dm_fop_dimension_carryover_sum_t' as source_table
      ,'' as remark
 	  , -1 as created_by
 	  , current_timestamp as creation_date
 	  , -1 as last_updated_by
 	  , current_timestamp as last_update_date
 	  , 'N' as del_flag
	  from phase_tmp
	  ;
			   
-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码:'||v_version_code||',dm_fop_dimension_sop_plan_sum_t 目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;



--处理异常信息
	exception
		when others then
		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
			p_log_sp_name => v_sp_name,    --sp名称
			p_log_step_num  => null,
			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
			) ;
	x_success_flag := '2001';
	--收集统计信息
	analyse fin_dm_opt_fop.dm_fop_dimension_sop_plan_sum_t;
	
		
 end;
 $BODY$
 LANGUAGE plpgsql VOLATILE
  COST 100