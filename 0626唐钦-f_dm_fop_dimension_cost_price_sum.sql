CREATE OR REPLACE FUNCTION fin_dm_opt_fop.f_dm_fop_dimension_cost_price_sum(OUT x_success_flag text)
 RETURNS text
 LANGUAGE plpgsql
 NOT FENCED NOT SHIPPABLE
AS $$
 /*
创建时间：2025-06-18
创建人  ：TWX1139790
背景描述：对价前收入、对价前成本逻辑处理,然后调用该函数将相对应的数据生成导入到目标表中
参数描述：参数一(P_VERSION_CODE)：版本编码202505
          参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_COST_PRICE_SUM();
*/
 
 DECLARE
    V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_COST_PRICE_SUM';
    V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
    V_STEP_NUM   BIGINT := 0; --步骤号
    V_DML_ROW_COUNT  NUMBER DEFAULT 0 ;
BEGIN
    X_SUCCESS_FLAG := '1';                                 --1表示成功
    
       -- 如果是传 VERSION_CODE 调函数取JAVA传入的 P_VERSION_CODE ，如果是自动调度的则取 当前年月 版本
/*        IF P_VERSION_CODE IS NOT NULL THEN 
        SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
        ELSE */
        SELECT TO_CHAR(CURRENT_DATE,'YYYYMM') AS VERSION_CODE INTO V_VERSION_CODE ;
--        END IF ;
            
  --1.开始日志
/*  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '对价前收入、对价前成本的数据来源表,目标表中'||TO_CHAR(CURRENT_DATE,'YYYYMM')||'日期对应的最大版本编码:'||V_VERSION_CODE||',开始运行'--日志描述
      ) ;
*/

  -- 删除对应版本数据
  EXECUTE IMMEDIATE 'DELETE FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_COST_PRICE_SUM_T WHERE VERSION_CODE = '||V_VERSION_CODE;
/*  
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '删除对应版本数据：'||V_VERSION_CODE,--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => SQL%ROWCOUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
*/
  
  -- 创建对价前收入临时表
  DROP TABLE IF EXISTS FOP_FROM_REV_AMT_TMP;
  CREATE TEMPORARY TABLE FOP_FROM_REV_AMT_TMP (
    TIME_WINDOW_CODE VARCHAR(50),
    PERIOD_ID INT,
    BG_CODE VARCHAR(50),
    BG_NAME VARCHAR(200),
    OVERSEA_CODE VARCHAR(50),
    OVERSEA_DESC VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    SCENARIOS VARCHAR(50),
    DIMENSION_GROUP_CODE_L2 VARCHAR(50),
    DIMENSION_GROUP_L2_CN_NAME VARCHAR(200),
    DIMENSION_GROUP_L2_EN_NAME VARCHAR(200),
    PRODUCT_DIMENSION_GROUP_CODE VARCHAR(50),
    PRODUCT_DIMENSION_GROUP VARCHAR(200),
    PRODUCT_DIMENSION_GROUP_EN_NAME VARCHAR(200),
    DIMENSION_SUBCATEGORY_CODE VARCHAR(50),    
    DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200), 
    DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200), 
    REV_AMT NUMERIC,
    CURRENCY_CODE VARCHAR(50)
   )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(PRODUCT_DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);
  
  -- 创建对价前收入目标数据临时表
  DROP TABLE IF EXISTS FOP_DIMENSION_REV_AMT_TMP;
  CREATE TEMPORARY TABLE FOP_DIMENSION_REV_AMT_TMP (
    TIME_WINDOW_CODE VARCHAR(50),
    PERIOD_ID INT,
    BG_CODE VARCHAR(50),
    BG_NAME VARCHAR(200),
    OVERSEA_CODE VARCHAR(50),
    OVERSEA_DESC VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    SCENARIOS VARCHAR(50),
    DIMENSION_GROUP_CODE VARCHAR(50),
    DIMENSION_GROUP_CN_NAME VARCHAR(200),
    DIMENSION_GROUP_EN_NAME VARCHAR(200),
    DIMENSION_SUBCATEGORY_CODE VARCHAR(50),    
    DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200), 
    DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200), 
    REV_AMT NUMERIC,
    CURRENCY_CODE VARCHAR(50)
   )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);
  
  -- 创建对价前成本临时表
  DROP TABLE IF EXISTS FOP_FROM_COST_AMT_TMP;
  CREATE TEMPORARY TABLE FOP_FROM_COST_AMT_TMP (
    TIME_WINDOW_CODE VARCHAR(50),
    PERIOD_ID INT,
    BG_CODE VARCHAR(50),
    BG_NAME VARCHAR(200),
    OVERSEA_CODE VARCHAR(50),
    OVERSEA_DESC VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    SCENARIOS VARCHAR(50),
    DIMENSION_GROUP_CODE_L2 VARCHAR(50),
    DIMENSION_GROUP_L2_CN_NAME VARCHAR(200),
    DIMENSION_GROUP_L2_EN_NAME VARCHAR(200),
    PRODUCT_DIMENSION_GROUP_CODE VARCHAR(50),
    PRODUCT_DIMENSION_GROUP VARCHAR(200),
    PRODUCT_DIMENSION_GROUP_EN_NAME VARCHAR(200),
    DIMENSION_SUBCATEGORY_CODE VARCHAR(50),    
    DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200), 
    DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200), 
    COST_AMT NUMERIC,
    CURRENCY_CODE VARCHAR(50)
   )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(PRODUCT_DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);
  
  -- 创建对价前成本目标数据临时表
  DROP TABLE IF EXISTS FOP_DIMENSION_COST_AMT_TMP;
  CREATE TEMPORARY TABLE FOP_DIMENSION_COST_AMT_TMP (
    TIME_WINDOW_CODE VARCHAR(50),
    PERIOD_ID INT,
    BG_CODE VARCHAR(50),
    BG_NAME VARCHAR(200),
    OVERSEA_CODE VARCHAR(50),
    OVERSEA_DESC VARCHAR(200),
    LV1_PROD_RND_TEAM_CODE VARCHAR(50),
    LV1_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV1_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    LV2_PROD_RND_TEAM_CODE VARCHAR(50),
    LV2_PROD_RD_TEAM_CN_NAME VARCHAR(200),
    LV2_PROD_RD_TEAM_EN_NAME VARCHAR(200),
    SCENARIOS VARCHAR(50),
    DIMENSION_GROUP_CODE VARCHAR(50),
    DIMENSION_GROUP_CN_NAME VARCHAR(200),
    DIMENSION_GROUP_EN_NAME VARCHAR(200),
    DIMENSION_SUBCATEGORY_CODE VARCHAR(50),    
    DIMENSION_SUBCATEGORY_CN_NAME VARCHAR(200), 
    DIMENSION_SUBCATEGORY_EN_NAME VARCHAR(200), 
    COST_AMT NUMERIC,
    CURRENCY_CODE VARCHAR(50)
   )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);

  -- 取对价前收入-量价数据
  INSERT INTO FOP_FROM_REV_AMT_TMP(
         TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE_L2,
         DIMENSION_GROUP_L2_CN_NAME,
         DIMENSION_GROUP_L2_EN_NAME,
         PRODUCT_DIMENSION_GROUP_CODE, 
         PRODUCT_DIMENSION_GROUP,
         PRODUCT_DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         REV_AMT,
         CURRENCY_CODE
  )
  WITH ALLOC_DTL_TMP AS(
  -- 取人民币数据
  SELECT 'YTD' AS TIME_WINDOW_CODE,
          T1.PERIOD_ID,
          DIM.LV0_PROD_LIST_CODE AS BG_CODE,
          DIM.LV0_PROD_LIST_CN_NAME AS BG_NAME,
          T1.DOMESTIC_OR_OVERSEA_CODE AS OVERSEA_CODE,
          DECODE(T1.DOMESTIC_OR_OVERSEA_CODE,'GH0002','国内',T1.DOMESTIC_OR_OVERSEA_CNAME) AS OVERSEA_DESC,   -- 将源表名称为中国区的，调整为国内
          DIM.LV1_PROD_RND_TEAM_CODE,
          DIM.LV1_PROD_RD_TEAM_CN_NAME,
          DIM.LV1_PROD_RD_TEAM_EN_NAME,
          DIM.LV2_PROD_RND_TEAM_CODE,
          DIM.LV2_PROD_RD_TEAM_CN_NAME,
          DIM.LV2_PROD_RD_TEAM_EN_NAME,
          CASE WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100001','101775')   -- 无线、数据存储
               THEN '量纲子类'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','134557','137565')   -- 计算、光、数据通信
               THEN '量纲分组'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100011')   -- 云核心网
               THEN 'LV2'
          END AS SCENARIOS,
          T1.DIMENSION_GROUP_CODE_L2,
          T1.DIMENSION_GROUP_L2_CN_NAME,
          T1.DIMENSION_GROUP_L2_EN_NAME,
          T1.PRODUCT_DIMENSION_GROUP_CODE, 
          T1.PRODUCT_DIMENSION_GROUP,
          T1.PRODUCT_DIMENSION_GROUP_EN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,    
          T1.DIMENSION_SUBCATEGORY_CN_NAME, 
          T1.DIMENSION_SUBCATEGORY_EN_NAME, 
          NVL(T1.NEW_PROD_AMOUNT_RMB,0) AS REV_AMT,
          'CNY' AS CURRENCY_CODE
     FROM FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_DTL_I T1   -- 对价前收入表
     LEFT JOIN DMDIM.DM_DIM_PRODUCT_D DIM   -- 产品维表（取LV1-LV2重量级团队字段、BG字段）
     ON T1.PROD_KEY = DIM.PROD_KEY
     WHERE DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','101775','134557','137565','100001','100011')   -- 限制LV1重量级团队为：'无线','数据存储','数据通信','计算','光','云核心网'
     AND T1.DATA_SOURCE <> '损益设备收入成本调整(非401/402/50101)'    -- 剔除差异分摊数据
     AND T1.TIME_WINDOW_CODE = 'YTD'   -- 只取月份YTD数据
	 AND T1.DOMESTIC_OR_OVERSEA_CODE <> 'GH0004'   -- 不取其他的数据
     AND CAST(T1.PERIOD_ID AS INT) >= CAST(TO_CHAR(ADD_MONTHS(CURRENT_DATE,-61),'YYYYMM') AS INT)   -- 取近5年数据
  UNION ALL 
  -- 取美元数据
  SELECT 'YTD' AS TIME_WINDOW_CODE,
          T1.PERIOD_ID,
          DIM.LV0_PROD_LIST_CODE AS BG_CODE,
          DIM.LV0_PROD_LIST_CN_NAME AS BG_NAME,
          T1.DOMESTIC_OR_OVERSEA_CODE AS OVERSEA_CODE,
          DECODE(T1.DOMESTIC_OR_OVERSEA_CODE,'GH0002','国内',T1.DOMESTIC_OR_OVERSEA_CNAME) AS OVERSEA_DESC,   -- 将源表名称为中国区的，调整为国内
          DIM.LV1_PROD_RND_TEAM_CODE,
          DIM.LV1_PROD_RD_TEAM_CN_NAME,
          DIM.LV1_PROD_RD_TEAM_EN_NAME,
          DIM.LV2_PROD_RND_TEAM_CODE,
          DIM.LV2_PROD_RD_TEAM_CN_NAME,
          DIM.LV2_PROD_RD_TEAM_EN_NAME,
          CASE WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100001','101775')   -- 无线、数据存储
               THEN '量纲子类'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','134557','137565')   -- 计算、光、数据通信
               THEN '量纲分组'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100011')   -- 云核心网
               THEN 'LV2'
          END AS SCENARIOS,
          T1.DIMENSION_GROUP_CODE_L2,
          T1.DIMENSION_GROUP_L2_CN_NAME,
          T1.DIMENSION_GROUP_L2_EN_NAME,
          T1.PRODUCT_DIMENSION_GROUP_CODE, 
          T1.PRODUCT_DIMENSION_GROUP,
          T1.PRODUCT_DIMENSION_GROUP_EN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,    
          T1.DIMENSION_SUBCATEGORY_CN_NAME, 
          T1.DIMENSION_SUBCATEGORY_EN_NAME, 
          NVL(T1.NEW_PROD_AMOUNT_USD,0) AS REV_AMT,
          'USD' AS CURRENCY_CODE
     FROM FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_DTL_I T1   -- 对价前收入表
     LEFT JOIN DMDIM.DM_DIM_PRODUCT_D DIM   -- 产品维表（取LV1-LV2重量级团队字段、BG字段）
     ON T1.PROD_KEY = DIM.PROD_KEY
     WHERE DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','101775','134557','137565','100001','100011')   -- 限制LV1重量级团队为：'无线','数据存储','数据通信','计算','光','云核心网'
     AND T1.DATA_SOURCE <> '损益设备收入成本调整(非401/402/50101)'    -- 剔除差异分摊数据
     AND T1.TIME_WINDOW_CODE = 'YTD'   -- 只取月份YTD数据
	 AND T1.DOMESTIC_OR_OVERSEA_CODE <> 'GH0004'   -- 不取其他的数据
     AND CAST(T1.PERIOD_ID AS INT) >= CAST(TO_CHAR(ADD_MONTHS(CURRENT_DATE,-61),'YYYYMM') AS INT)   -- 取近5年数据
  )
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE_L2,
         DIMENSION_GROUP_L2_CN_NAME,
         DIMENSION_GROUP_L2_EN_NAME,
         PRODUCT_DIMENSION_GROUP_CODE, 
         PRODUCT_DIMENSION_GROUP,
         PRODUCT_DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(REV_AMT) AS REV_AMT,
         CURRENCY_CODE
     FROM ALLOC_DTL_TMP
     GROUP BY TIME_WINDOW_CODE,
              PERIOD_ID,
              BG_CODE,
              BG_NAME,
              OVERSEA_CODE,
              OVERSEA_DESC,
              LV1_PROD_RND_TEAM_CODE,
              LV1_PROD_RD_TEAM_CN_NAME,
              LV1_PROD_RD_TEAM_EN_NAME,
              LV2_PROD_RND_TEAM_CODE,
              LV2_PROD_RD_TEAM_CN_NAME,
              LV2_PROD_RD_TEAM_EN_NAME,
              SCENARIOS,
              DIMENSION_GROUP_CODE_L2,
              DIMENSION_GROUP_L2_CN_NAME,
              DIMENSION_GROUP_L2_EN_NAME,
              PRODUCT_DIMENSION_GROUP_CODE, 
              PRODUCT_DIMENSION_GROUP,
              PRODUCT_DIMENSION_GROUP_EN_NAME,
              DIMENSION_SUBCATEGORY_CODE,    
              DIMENSION_SUBCATEGORY_CN_NAME, 
              DIMENSION_SUBCATEGORY_EN_NAME, 
              CURRENCY_CODE;
  
/*  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '对价前收入数据汇总求和后插入临时表',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => SQL%ROWCOUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
  */
  
  -- 对价前收入目标表数据逻辑处理
  INSERT INTO FOP_DIMENSION_REV_AMT_TMP(
         TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         REV_AMT,
         CURRENCY_CODE
  )
  -- 造一版全球的数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         'GH0001' AS OVERSEA_CODE,  
         '全球' AS OVERSEA_DESC,     -- 国内+海外的数据都卷积为全球
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         PRODUCT_DIMENSION_GROUP_CODE AS DIMENSION_GROUP_CODE,
         PRODUCT_DIMENSION_GROUP AS DIMENSION_GROUP_CN_NAME,
         PRODUCT_DIMENSION_GROUP_EN_NAME AS DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(REV_AMT) AS REV_AMT,
         CURRENCY_CODE
      FROM FOP_FROM_REV_AMT_TMP
      GROUP BY TIME_WINDOW_CODE,
               PERIOD_ID,
               BG_CODE,
               BG_NAME,
               LV1_PROD_RND_TEAM_CODE,
               LV1_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RD_TEAM_EN_NAME,
               LV2_PROD_RND_TEAM_CODE,
               LV2_PROD_RD_TEAM_CN_NAME,
               LV2_PROD_RD_TEAM_EN_NAME,
               SCENARIOS,
               PRODUCT_DIMENSION_GROUP_CODE,
               PRODUCT_DIMENSION_GROUP,
               PRODUCT_DIMENSION_GROUP_EN_NAME,
               DIMENSION_SUBCATEGORY_CODE,    
               DIMENSION_SUBCATEGORY_CN_NAME, 
               DIMENSION_SUBCATEGORY_EN_NAME, 
               CURRENCY_CODE;
  
  
  -- 对价前收入目标表数据逻辑处理
  INSERT INTO FOP_DIMENSION_REV_AMT_TMP(
         TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         REV_AMT,
         CURRENCY_CODE
  )
  WITH ICT_SUM_REV_TMP AS(
  -- 取全球造数的部分数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         REV_AMT,
         CURRENCY_CODE
      FROM FOP_DIMENSION_REV_AMT_TMP
  UNION ALL 
  -- 取来源表部分数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,  
         OVERSEA_DESC,     
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         PRODUCT_DIMENSION_GROUP_CODE AS DIMENSION_GROUP_CODE,
         PRODUCT_DIMENSION_GROUP AS DIMENSION_GROUP_CN_NAME,
         PRODUCT_DIMENSION_GROUP_EN_NAME AS DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         REV_AMT,
         CURRENCY_CODE
      FROM FOP_FROM_REV_AMT_TMP
  ),
  CNBG_HOME_REV_TMP AS(
  -- 来源表所有数据，并对中国&CNBG的量纲分组场景数据进行特殊预处理
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         CASE WHEN OVERSEA_CODE = 'GH0002' AND BG_CODE = 'PDCG901159'   -- 中国区&CNBG
              THEN DIMENSION_GROUP_CODE_L2 
              ELSE PRODUCT_DIMENSION_GROUP_CODE  
         END AS DIMENSION_GROUP_CODE,   
         CASE WHEN OVERSEA_CODE = 'GH0002' AND BG_CODE = 'PDCG901159'   -- 中国区&CNBG
              THEN DIMENSION_GROUP_L2_CN_NAME 
              ELSE PRODUCT_DIMENSION_GROUP
         END AS DIMENSION_GROUP_CN_NAME,        
         CASE WHEN OVERSEA_CODE = 'GH0002' AND BG_CODE = 'PDCG901159'   -- 中国区&CNBG
              THEN DIMENSION_GROUP_L2_EN_NAME 
              ELSE PRODUCT_DIMENSION_GROUP_EN_NAME
         END AS DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         REV_AMT,
         CURRENCY_CODE
      FROM FOP_FROM_REV_AMT_TMP
  )
  -- 造一份完整的BG为ICT的数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         'PROD0002' AS BG_CODE,
         'ICT' AS BG_NAME,        -- 所有BG的数据都卷积为ICT
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(REV_AMT) AS REV_AMT,
         CURRENCY_CODE
      FROM ICT_SUM_REV_TMP
      GROUP BY TIME_WINDOW_CODE,
               PERIOD_ID,
               OVERSEA_CODE,
               OVERSEA_DESC,
               LV1_PROD_RND_TEAM_CODE,
               LV1_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RD_TEAM_EN_NAME,
               LV2_PROD_RND_TEAM_CODE,
               LV2_PROD_RD_TEAM_CN_NAME,
               LV2_PROD_RD_TEAM_EN_NAME,
               SCENARIOS,
               DIMENSION_GROUP_CODE,
               DIMENSION_GROUP_CN_NAME,
               DIMENSION_GROUP_EN_NAME,
               DIMENSION_SUBCATEGORY_CODE,    
               DIMENSION_SUBCATEGORY_CN_NAME, 
               DIMENSION_SUBCATEGORY_EN_NAME, 
               CURRENCY_CODE
  UNION ALL
  -- 来源表所有数据，并对中国&CNBG的量纲分组场景数据进行特殊预处理
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,     
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(REV_AMT) AS REV_AMT,
         CURRENCY_CODE
      FROM CNBG_HOME_REV_TMP
      GROUP BY TIME_WINDOW_CODE,
               PERIOD_ID,
               OVERSEA_CODE,
               OVERSEA_DESC,
               BG_CODE,
               BG_NAME,
               LV1_PROD_RND_TEAM_CODE,
               LV1_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RD_TEAM_EN_NAME,
               LV2_PROD_RND_TEAM_CODE,
               LV2_PROD_RD_TEAM_CN_NAME,
               LV2_PROD_RD_TEAM_EN_NAME,
               SCENARIOS,
               DIMENSION_GROUP_CODE,
               DIMENSION_GROUP_CN_NAME,
               DIMENSION_GROUP_EN_NAME,
               DIMENSION_SUBCATEGORY_CODE,    
               DIMENSION_SUBCATEGORY_CN_NAME, 
               DIMENSION_SUBCATEGORY_EN_NAME, 
               CURRENCY_CODE;

  
  -- 对价前成本-量价数据
  INSERT INTO FOP_FROM_COST_AMT_TMP(
         TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE_L2,
         DIMENSION_GROUP_L2_CN_NAME,
         DIMENSION_GROUP_L2_EN_NAME,
         PRODUCT_DIMENSION_GROUP_CODE, 
         PRODUCT_DIMENSION_GROUP,
         PRODUCT_DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         COST_AMT,
         CURRENCY_CODE
  )
  WITH ALLOC_COST_DCP_TMP AS(
    -- 取人民币数据
  SELECT 'YTD' AS TIME_WINDOW_CODE,
          T1.PERIOD_ID,
          DIM.LV0_PROD_LIST_CODE AS BG_CODE,
          DIM.LV0_PROD_LIST_CN_NAME AS BG_NAME,
          T1.DOMESTIC_OR_OVERSEA_CODE AS OVERSEA_CODE,
          DECODE(T1.DOMESTIC_OR_OVERSEA_CODE,'GH0002','国内',T1.DOMESTIC_OR_OVERSEA_CNAME) AS OVERSEA_DESC,   -- 将源表名称为中国区的，调整为国内
          DIM.LV1_PROD_RND_TEAM_CODE,
          DIM.LV1_PROD_RD_TEAM_CN_NAME,
          DIM.LV1_PROD_RD_TEAM_EN_NAME,
          DIM.LV2_PROD_RND_TEAM_CODE,
          DIM.LV2_PROD_RD_TEAM_CN_NAME,
          DIM.LV2_PROD_RD_TEAM_EN_NAME,
          CASE WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100001','101775')   -- 无线、数据存储
               THEN '量纲子类'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','134557','137565')   -- 计算、光、数据通信
               THEN '量纲分组'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100011')   -- 云核心网
               THEN 'LV2'
          END AS SCENARIOS,
          T1.DIMENSION_GROUP_CODE_L2,
          T1.DIMENSION_GROUP_L2_CN_NAME,
          T1.DIMENSION_GROUP_L2_EN_NAME,
          T1.PRODUCT_DIMENSION_GROUP_CODE, 
          T1.PRODUCT_DIMENSION_GROUP,
          T1.PRODUCT_DIMENSION_GROUP_EN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,    
          T1.DIMENSION_SUBCATEGORY_CN_NAME, 
          T1.DIMENSION_SUBCATEGORY_EN_NAME, 
          NVL(T1.COST_ALLOC_AMT_RMB,0) AS COST_AMT,
          'CNY' AS CURRENCY_CODE
     FROM FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_COST_DCP_I T1   -- 对价前成本表
     LEFT JOIN DMDIM.DM_DIM_PRODUCT_D DIM   -- 产品维表（取LV1-LV2重量级团队字段、BG字段）
     ON T1.PROD_KEY = DIM.PROD_KEY
     WHERE DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','101775','134557','137565','100001','100011')   -- 限制LV1重量级团队为：'无线','数据存储','数据通信','计算','光','云核心网'
--     AND T1.DATA_SOURCE <> '损益设备收入成本调整(非401/402/50101)'    -- 剔除差异分摊数据
     AND T1.TIME_WINDOW_CODE = 'YTD'   -- 只取月份YTD数据
     AND T1.DOMESTIC_OR_OVERSEA_CODE <> 'GH0004'   -- 不取其他的数据
     AND CAST(T1.PERIOD_ID AS INT) >= CAST(TO_CHAR(ADD_MONTHS(CURRENT_DATE,-61),'YYYYMM') AS INT)   -- 取近5年数据
  UNION ALL 
  -- 取美元数据
  SELECT 'YTD' AS TIME_WINDOW_CODE,
          T1.PERIOD_ID,
          DIM.LV0_PROD_LIST_CODE AS BG_CODE,
          DIM.LV0_PROD_LIST_CN_NAME AS BG_NAME,
          T1.DOMESTIC_OR_OVERSEA_CODE AS OVERSEA_CODE,
          DECODE(T1.DOMESTIC_OR_OVERSEA_CODE,'GH0002','国内',T1.DOMESTIC_OR_OVERSEA_CNAME) AS OVERSEA_DESC,   -- 将源表名称为中国区的，调整为国内
          DIM.LV1_PROD_RND_TEAM_CODE,
          DIM.LV1_PROD_RD_TEAM_CN_NAME,
          DIM.LV1_PROD_RD_TEAM_EN_NAME,
          DIM.LV2_PROD_RND_TEAM_CODE,
          DIM.LV2_PROD_RD_TEAM_CN_NAME,
          DIM.LV2_PROD_RD_TEAM_EN_NAME,
          CASE WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100001','101775')   -- 无线、数据存储
               THEN '量纲子类'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','134557','137565')   -- 计算、光、数据通信
               THEN '量纲分组'
               WHEN DIM.LV1_PROD_RND_TEAM_CODE IN ('100011')   -- 云核心网
               THEN 'LV2'
          END AS SCENARIOS,
          T1.DIMENSION_GROUP_CODE_L2,
          T1.DIMENSION_GROUP_L2_CN_NAME,
          T1.DIMENSION_GROUP_L2_EN_NAME,
          T1.PRODUCT_DIMENSION_GROUP_CODE, 
          T1.PRODUCT_DIMENSION_GROUP,
          T1.PRODUCT_DIMENSION_GROUP_EN_NAME,
          T1.DIMENSION_SUBCATEGORY_CODE,    
          T1.DIMENSION_SUBCATEGORY_CN_NAME, 
          T1.DIMENSION_SUBCATEGORY_EN_NAME, 
          NVL(T1.COST_ALLOC_AMT_USD,0) AS COST_AMT,
          'USD' AS CURRENCY_CODE
     FROM FIN_DM_OPT_FOP.DWK_ICT_PS_PROD_ALLOC_COST_DCP_I T1   -- 对价前成本表
     LEFT JOIN DMDIM.DM_DIM_PRODUCT_D DIM   -- 产品维表（取LV1-LV2重量级团队字段、BG字段）
     ON T1.PROD_KEY = DIM.PROD_KEY
     WHERE DIM.LV1_PROD_RND_TEAM_CODE IN ('133277','101775','134557','137565','100001','100011')   -- 限制LV1重量级团队为：'无线','数据存储','数据通信','计算','光','云核心网'
--     AND T1.DATA_SOURCE <> '损益设备收入成本调整(非401/402/50101)'    -- 剔除差异分摊数据
     AND T1.TIME_WINDOW_CODE = 'YTD'   -- 只取月份YTD数据
     AND T1.DOMESTIC_OR_OVERSEA_CODE <> 'GH0004'   -- 不取其他的数据
     AND CAST(T1.PERIOD_ID AS INT) >= CAST(TO_CHAR(ADD_MONTHS(CURRENT_DATE,-61),'YYYYMM') AS INT)   -- 取近5年数据
  )
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE_L2,
         DIMENSION_GROUP_L2_CN_NAME,
         DIMENSION_GROUP_L2_EN_NAME,
         PRODUCT_DIMENSION_GROUP_CODE, 
         PRODUCT_DIMENSION_GROUP,
         PRODUCT_DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(COST_AMT) AS COST_AMT,
         CURRENCY_CODE
     FROM ALLOC_COST_DCP_TMP
     GROUP BY TIME_WINDOW_CODE,
              PERIOD_ID,
              BG_CODE,
              BG_NAME,
              OVERSEA_CODE,
              OVERSEA_DESC,
              LV1_PROD_RND_TEAM_CODE,
              LV1_PROD_RD_TEAM_CN_NAME,
              LV1_PROD_RD_TEAM_EN_NAME,
              LV2_PROD_RND_TEAM_CODE,
              LV2_PROD_RD_TEAM_CN_NAME,
              LV2_PROD_RD_TEAM_EN_NAME,
              SCENARIOS,
              DIMENSION_GROUP_CODE_L2,
              DIMENSION_GROUP_L2_CN_NAME,
              DIMENSION_GROUP_L2_EN_NAME,
              PRODUCT_DIMENSION_GROUP_CODE, 
              PRODUCT_DIMENSION_GROUP,
              PRODUCT_DIMENSION_GROUP_EN_NAME,
              DIMENSION_SUBCATEGORY_CODE,    
              DIMENSION_SUBCATEGORY_CN_NAME, 
              DIMENSION_SUBCATEGORY_EN_NAME, 
              CURRENCY_CODE;
/*  
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '对价前成本数据汇总求和后插入临时表',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => SQL%ROWCOUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
*/

  -- 对价前收入目标表数据逻辑处理
  INSERT INTO FOP_DIMENSION_COST_AMT_TMP(
         TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         COST_AMT,
         CURRENCY_CODE
  )
  -- 造一版全球的数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         'GH0001' AS OVERSEA_CODE,  
         '全球' AS OVERSEA_DESC,     -- 国内+海外的数据都卷积为全球
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         PRODUCT_DIMENSION_GROUP_CODE AS DIMENSION_GROUP_CODE,
         PRODUCT_DIMENSION_GROUP AS DIMENSION_GROUP_CN_NAME,
         PRODUCT_DIMENSION_GROUP_EN_NAME AS DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(COST_AMT) AS COST_AMT,
         CURRENCY_CODE
      FROM FOP_FROM_COST_AMT_TMP
      GROUP BY TIME_WINDOW_CODE,
               PERIOD_ID,
               BG_CODE,
               BG_NAME,
               LV1_PROD_RND_TEAM_CODE,
               LV1_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RD_TEAM_EN_NAME,
               LV2_PROD_RND_TEAM_CODE,
               LV2_PROD_RD_TEAM_CN_NAME,
               LV2_PROD_RD_TEAM_EN_NAME,
               SCENARIOS,
               PRODUCT_DIMENSION_GROUP_CODE,
               PRODUCT_DIMENSION_GROUP,
               PRODUCT_DIMENSION_GROUP_EN_NAME,
               DIMENSION_SUBCATEGORY_CODE,    
               DIMENSION_SUBCATEGORY_CN_NAME, 
               DIMENSION_SUBCATEGORY_EN_NAME, 
               CURRENCY_CODE;
  
  
  -- 对价前成本目标表数据逻辑处理
  INSERT INTO FOP_DIMENSION_COST_AMT_TMP(
         TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         COST_AMT,
         CURRENCY_CODE
  )
  WITH ICT_SUM_COST_TMP AS(
  -- 取全球造数的部分数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         COST_AMT,
         CURRENCY_CODE
      FROM FOP_DIMENSION_COST_AMT_TMP
  UNION ALL 
  -- 取来源表部分数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,  
         OVERSEA_DESC,     
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         PRODUCT_DIMENSION_GROUP_CODE AS DIMENSION_GROUP_CODE,
         PRODUCT_DIMENSION_GROUP AS DIMENSION_GROUP_CN_NAME,
         PRODUCT_DIMENSION_GROUP_EN_NAME AS DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         COST_AMT,
         CURRENCY_CODE
      FROM FOP_FROM_COST_AMT_TMP
  ),
  CNBG_HOME_COST_TMP AS(
  -- 来源表所有数据，并对中国&CNBG的量纲分组场景数据进行特殊预处理
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         CASE WHEN OVERSEA_CODE = 'GH0002' AND BG_CODE = 'PDCG901159'   -- 中国区&CNBG
              THEN DIMENSION_GROUP_CODE_L2 
              ELSE PRODUCT_DIMENSION_GROUP_CODE  
         END AS DIMENSION_GROUP_CODE,   
         CASE WHEN OVERSEA_CODE = 'GH0002' AND BG_CODE = 'PDCG901159'   -- 中国区&CNBG
              THEN DIMENSION_GROUP_L2_CN_NAME 
              ELSE PRODUCT_DIMENSION_GROUP
         END AS DIMENSION_GROUP_CN_NAME,        
         CASE WHEN OVERSEA_CODE = 'GH0002' AND BG_CODE = 'PDCG901159'   -- 中国区&CNBG
              THEN DIMENSION_GROUP_L2_EN_NAME 
              ELSE PRODUCT_DIMENSION_GROUP_EN_NAME
         END AS DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         COST_AMT,
         CURRENCY_CODE
      FROM FOP_FROM_COST_AMT_TMP 
  )
  -- 造一份完整的BG为ICT的数据
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         'PROD0002' AS BG_CODE,
         'ICT' AS BG_NAME,        -- 所有BG的数据都卷积为ICT
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(COST_AMT) AS COST_AMT,
         CURRENCY_CODE
      FROM ICT_SUM_COST_TMP
      GROUP BY TIME_WINDOW_CODE,
               PERIOD_ID,
               OVERSEA_CODE,
               OVERSEA_DESC,
               LV1_PROD_RND_TEAM_CODE,
               LV1_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RD_TEAM_EN_NAME,
               LV2_PROD_RND_TEAM_CODE,
               LV2_PROD_RD_TEAM_CN_NAME,
               LV2_PROD_RD_TEAM_EN_NAME,
               SCENARIOS,
               DIMENSION_GROUP_CODE,
               DIMENSION_GROUP_CN_NAME,
               DIMENSION_GROUP_EN_NAME,
               DIMENSION_SUBCATEGORY_CODE,    
               DIMENSION_SUBCATEGORY_CN_NAME, 
               DIMENSION_SUBCATEGORY_EN_NAME, 
               CURRENCY_CODE
  UNION ALL
  -- 来源表所有数据，并对中国&CNBG的量纲分组场景数据进行特殊预处理
  SELECT TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,     
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,
         DIMENSION_GROUP_CN_NAME,
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(COST_AMT) AS COST_AMT,
         CURRENCY_CODE
      FROM CNBG_HOME_COST_TMP
      GROUP BY TIME_WINDOW_CODE,
               PERIOD_ID,
               OVERSEA_CODE,
               OVERSEA_DESC,
               BG_CODE,
               BG_NAME,
               LV1_PROD_RND_TEAM_CODE,
               LV1_PROD_RD_TEAM_CN_NAME,
               LV1_PROD_RD_TEAM_EN_NAME,
               LV2_PROD_RND_TEAM_CODE,
               LV2_PROD_RD_TEAM_CN_NAME,
               LV2_PROD_RD_TEAM_EN_NAME,
               SCENARIOS,
               DIMENSION_GROUP_CODE,
               DIMENSION_GROUP_CN_NAME,
               DIMENSION_GROUP_EN_NAME,
               DIMENSION_SUBCATEGORY_CODE,    
               DIMENSION_SUBCATEGORY_CN_NAME, 
               DIMENSION_SUBCATEGORY_EN_NAME, 
               CURRENCY_CODE;

       -- 分别收敛到量纲子类，量纲分组，LV2粒度
	   DROP TABLE IF EXISTS REV_COST_TMP;
	   CREATE TEMPORARY TABLE  REV_COST_TMP
		          AS
           SELECT  T1.TIME_WINDOW_CODE,
         T1.PERIOD_ID,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_EN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_EN_NAME,
         T1.SCENARIOS,
         NULL AS DIMENSION_GROUP_CODE,   
         NULL AS DIMENSION_GROUP_CN_NAME,        
         NULL AS DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,    
         T1.DIMENSION_SUBCATEGORY_CN_NAME, 
         T1.DIMENSION_SUBCATEGORY_EN_NAME, 
         T1.REV_AMT AS EQUIP_REV_CONS_BEFORE_AMT,
         T2.COST_AMT AS EQUIP_COST_CONS_BEFORE_AMT,
         T1.CURRENCY_CODE                  
     FROM FOP_DIMENSION_REV_AMT_TMP T1 
     INNER JOIN FOP_DIMENSION_COST_AMT_TMP T2
     ON T1.TIME_WINDOW_CODE = T2.TIME_WINDOW_CODE
     AND T1.PERIOD_ID = T2.PERIOD_ID
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
     AND T1.LV1_PROD_RND_TEAM_CODE = T2.LV1_PROD_RND_TEAM_CODE
     AND T1.SCENARIOS = T2.SCENARIOS
     AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
     AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'S1') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'S1')
     AND NVL(T1.DIMENSION_GROUP_CODE,'S2') = NVL(T2.DIMENSION_GROUP_CODE,'S2')
     AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S3') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S3')
	 WHERE T1.SCENARIOS = '量纲子类'
	 UNION ALL
	 SELECT  T1.TIME_WINDOW_CODE,
         T1.PERIOD_ID,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_EN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_EN_NAME,
         T1.SCENARIOS,
         T1.DIMENSION_GROUP_CODE,   
         T1.DIMENSION_GROUP_CN_NAME,        
         T1.DIMENSION_GROUP_EN_NAME,
		 NULL AS DIMENSION_SUBCATEGORY_CODE,    
         NULL AS DIMENSION_SUBCATEGORY_CN_NAME, 
         NULL AS DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(T1.REV_AMT)  AS EQUIP_REV_CONS_BEFORE_AMT,
         SUM(T2.COST_AMT) AS EQUIP_COST_CONS_BEFORE_AMT,
         T1.CURRENCY_CODE                  
     FROM FOP_DIMENSION_REV_AMT_TMP T1 
     INNER JOIN FOP_DIMENSION_COST_AMT_TMP T2
     ON T1.TIME_WINDOW_CODE = T2.TIME_WINDOW_CODE
     AND T1.PERIOD_ID = T2.PERIOD_ID
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
     AND T1.LV1_PROD_RND_TEAM_CODE = T2.LV1_PROD_RND_TEAM_CODE
     AND T1.SCENARIOS = T2.SCENARIOS
     AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
     AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'S1') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'S1')
     AND NVL(T1.DIMENSION_GROUP_CODE,'S2') = NVL(T2.DIMENSION_GROUP_CODE,'S2')
     AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S3') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S3')
	 WHERE T1.SCENARIOS = '量纲分组'
	 GROUP BY T1.TIME_WINDOW_CODE,
         T1.PERIOD_ID,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_EN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_EN_NAME,
         T1.SCENARIOS,
         T1.DIMENSION_GROUP_CODE,   
         T1.DIMENSION_GROUP_CN_NAME,        
         T1.DIMENSION_GROUP_EN_NAME,
         T1.CURRENCY_CODE  
 UNION ALL
	 SELECT  T1.TIME_WINDOW_CODE,
         T1.PERIOD_ID,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_EN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_EN_NAME,
         T1.SCENARIOS,
		 NULL AS DIMENSION_GROUP_CODE,   
         NULL AS DIMENSION_GROUP_CN_NAME,        
         NULL AS DIMENSION_GROUP_EN_NAME,
		 NULL AS DIMENSION_SUBCATEGORY_CODE,    
         NULL AS DIMENSION_SUBCATEGORY_CN_NAME, 
         NULL AS DIMENSION_SUBCATEGORY_EN_NAME, 
         SUM(T1.REV_AMT)  AS EQUIP_REV_CONS_BEFORE_AMT,
         SUM(T2.COST_AMT) AS EQUIP_COST_CONS_BEFORE_AMT,
         T1.CURRENCY_CODE                  
     FROM FOP_DIMENSION_REV_AMT_TMP T1 
     INNER JOIN FOP_DIMENSION_COST_AMT_TMP T2
     ON T1.TIME_WINDOW_CODE = T2.TIME_WINDOW_CODE
     AND T1.PERIOD_ID = T2.PERIOD_ID
     AND T1.BG_CODE = T2.BG_CODE
     AND T1.OVERSEA_CODE = T2.OVERSEA_CODE
     AND T1.LV1_PROD_RND_TEAM_CODE = T2.LV1_PROD_RND_TEAM_CODE
     AND T1.SCENARIOS = T2.SCENARIOS
     AND T1.CURRENCY_CODE = T2.CURRENCY_CODE
     AND NVL(T1.LV2_PROD_RND_TEAM_CODE,'S1') = NVL(T2.LV2_PROD_RND_TEAM_CODE,'S1')
     AND NVL(T1.DIMENSION_GROUP_CODE,'S2') = NVL(T2.DIMENSION_GROUP_CODE,'S2')
     AND NVL(T1.DIMENSION_SUBCATEGORY_CODE,'S3') = NVL(T2.DIMENSION_SUBCATEGORY_CODE,'S3')
	 WHERE T1.SCENARIOS = 'LV2'
	 GROUP BY T1.TIME_WINDOW_CODE,
         T1.PERIOD_ID,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_EN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_EN_NAME,
         T1.SCENARIOS,
         T1.CURRENCY_CODE             		 
	 ;

 

  -- 将处理后的对价前成本和对价前收入数据，插入结果表
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_COST_PRICE_SUM_T(
         VERSION_CODE,
         TIME_WINDOW_CODE,
         PERIOD_ID,
         BG_CODE,
         BG_NAME,
         OVERSEA_CODE,
         OVERSEA_DESC,
         LV1_PROD_RND_TEAM_CODE,
         LV1_PROD_RD_TEAM_CN_NAME,
         LV1_PROD_RD_TEAM_EN_NAME,
         LV2_PROD_RND_TEAM_CODE,
         LV2_PROD_RD_TEAM_CN_NAME,
         LV2_PROD_RD_TEAM_EN_NAME,
         SCENARIOS,
         DIMENSION_GROUP_CODE,   
         DIMENSION_GROUP_CN_NAME,        
         DIMENSION_GROUP_EN_NAME,
         DIMENSION_SUBCATEGORY_CODE,    
         DIMENSION_SUBCATEGORY_CN_NAME, 
         DIMENSION_SUBCATEGORY_EN_NAME, 
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_COST_CONS_BEFORE_AMT,
         CURRENCY_CODE,                  
         SOURCE_TABLE,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
  )
  SELECT V_VERSION_CODE AS VERSION_CODE,
         T1.TIME_WINDOW_CODE,
         T1.PERIOD_ID,
         T1.BG_CODE,
         T1.BG_NAME,
         T1.OVERSEA_CODE,
         T1.OVERSEA_DESC,
         T1.LV1_PROD_RND_TEAM_CODE,
         T1.LV1_PROD_RD_TEAM_CN_NAME,
         T1.LV1_PROD_RD_TEAM_EN_NAME,
         T1.LV2_PROD_RND_TEAM_CODE,
         T1.LV2_PROD_RD_TEAM_CN_NAME,
         T1.LV2_PROD_RD_TEAM_EN_NAME,
         T1.SCENARIOS,
         T1.DIMENSION_GROUP_CODE,   
         T1.DIMENSION_GROUP_CN_NAME,        
         T1.DIMENSION_GROUP_EN_NAME,
         T1.DIMENSION_SUBCATEGORY_CODE,    
         T1.DIMENSION_SUBCATEGORY_CN_NAME, 
         T1.DIMENSION_SUBCATEGORY_EN_NAME, 
         T1.EQUIP_REV_CONS_BEFORE_AMT,
         T1.EQUIP_COST_CONS_BEFORE_AMT,
         T1.CURRENCY_CODE,                  
         'DWK_ICT_PS_PROD_ALLOC_DTL_I+DWK_ICT_PS_PROD_ALLOC_COST_DCP_I' AS SOURCE_TABLE,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
     FROM REV_COST_TMP T1;
/*       
  V_STEP_NUM := V_STEP_NUM+1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T(
        P_LOG_VERSION_ID => NULL,                 --版本
        P_LOG_SP_NAME => V_SP_NAME,    --SP名称
        P_LOG_PARA_LIST => '',--参数
        P_LOG_STEP_NUM  => V_STEP_NUM,
        P_LOG_CAL_LOG_DESC => '将处理后的对价前成本和对价前收入数据，插入结果表：DM_FOP_DIMENSION_COST_PRICE_SUM_T',--日志描述
        P_LOG_FORMULA_SQL_TXT => NULL,--错误信息
        P_LOG_ROW_COUNT => SQL%ROWCOUNT,
        P_LOG_ERRBUF => NULL  --错误编码
      ) ;
*/
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_COST_PRICE_SUM_T';
/*
  -- 日志结束
  V_STEP_NUM := V_STEP_NUM + 1;
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T
  (P_LOG_SP_NAME => V_SP_NAME,
   P_LOG_STEP_NUM => V_STEP_NUM,
   P_LOG_CAL_LOG_DESC => V_SP_NAME||'运行结束, 收集DM_FOP_DIMENSION_COST_PRICE_SUM_T统计信息完成!');
*/
  RETURN 'SUCCESS';
/*
EXCEPTION
  WHEN OTHERS THEN
  X_SUCCESS_FLAG := '0';
  
  PERFORM FIN_DM_OPT_FOP.F_DM_FOP_CAPTURE_LOG_INFO_T
  (F_SP_NAME => V_SP_NAME, 
   P_LOG_CAL_LOG_DESC => V_SP_NAME||'运行失败', 
   P_LOG_FORMULA_SQL_TXT => SQLERRM,--错误信息
   P_LOG_ERRBUF => SQLSTATE  --错误编码
   );
*/
END$$
/