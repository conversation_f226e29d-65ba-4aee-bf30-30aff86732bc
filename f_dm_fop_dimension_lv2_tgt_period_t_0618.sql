CREATE OR REPLACE FUNCTION "fin_dm_opt_fop"."f_dm_fop_dimension_lv2_tgt_period_t"(IN "p_version_code" varchar=NULL::character varying, OUT "x_success_flag" text)
  RETURNS "pg_catalog"."text" AS $BODY$
 /*
创建时间：2025-06-7
创建人  ：wwx1077703
背景描述：将汇总层的数据插入到TGT的LV2接口表，具体逻辑如下
LV1LV2的对接后的收入成本的数据从盈利量纲-ICT损益汇总表 汇总表（dm_fop_dimension_ict_pl_sum_t）获取
设备前收入  设备前成本从盈利量对价前纲价格成本汇总表（dm_fop_dimension_cost_price_sum_t）获取
制毛率：=（对价后收入-对价后成本）/对价后收入
MAC调整率：（对价后收入-对价前收入）/对价后收入
制毛调整率：=（对价后收入-对价后成本）/对价后收入-（对价前收入-对价前成本）/对价前收入


参数描述：参数一(p_version_code)：版本编码202505
		  参数四(x_success_flag):返回状态 1-SUCCESS/2001-ERROR
事例    ：select fin_dm_opt_fop.f_dm_fop_dimension_filled_t();
*/
 
 declare
	v_sp_name VARCHAR(100)  := 'fin_dm_opt_fop.f_dm_fop_dimension_lv2_tgt_period_t('||p_version_code||')';
	v_tbl_name VARCHAR(100) := 'fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t';
	v_version_code VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
	v_step_mum   BIGINT; --步骤号
	v_dml_row_count  NUMBER DEFAULT 0 ;
  v_max_ytd  VARCHAR(50);  -- 最大期次的上一年
	


begin
	x_success_flag := '1';                                 --1表示成功
	
	       -- 获取版本号，如果是传 version_code 调函数取JAVA传入的 p_version_code ，如果是自动调度的则取 当前年月 版本
        if p_version_code is not null then 
        select  p_version_code into v_version_code ;
        else 
        select max(version_code) into v_version_code
        from fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t ;	
        end if 
        ;	
        
        select  (left(max(period_id)::VARCHAR,4)::numeric-1)::VARCHAR  into  v_max_ytd  from fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t  where version_code= v_version_code;
	
-- 	 --1.开始日志
  v_step_mum := 1;
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 1,
        p_log_cal_log_desc => '量纲层级数据汇总表'||v_tbl_name||',目标表中最大版本编码:'||v_version_code||',开始运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
	  

         -- 1.把收入和成本的数据全部灌到临时表，后面用来关联
      -- 同纬度下，取最大的一条数据  按照收入最大来取
      drop table if exists dm_fop_dimension_cost_price_sum_tmp;
	    create temporary table  dm_fop_dimension_cost_price_sum_tmp
		  as
      with  dimension_cost_price_sum   as (
      select   -- distinct  152256     
      cps.version_code	                  -- 版本编码
      ,cps.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,cps.time_window_code	              -- 统计时间窗          
      ,cps.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,cps.bg_code	                          -- BG编码
      ,cps.bg_name	                          -- BG名称
      ,cps.oversea_code	                  -- 区域编码
      ,cps.oversea_desc	                  -- 区域
      ,cps.lv1_prod_rnd_team_code  as lv1_code	                      -- 重量级团队LV1编码
      ,cps.lv1_prod_rd_team_cn_name as lv1_name	                      -- 重量级团队LV1描述
      ,cps.lv2_prod_rnd_team_code as lv2_code	                      -- 重量级团队LV2编码
      ,cps.lv2_prod_rd_team_cn_name as lv2_name	                      -- 重量级团队LV2名称
      ,cps.dimension_group_code   	          -- 量纲分组编码            
      ,cps.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,cps.dimension_group_en_name	          -- 量纲分组英文名称        
      ,cps.dimension_subcategory_code    	  -- 量纲子类编码               
      ,cps.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,cps.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      ,cps.currency_code    as currency	                      -- 币种
      ,cps.equip_rev_cons_before_amt	      -- 设备收入额（对价前） 
      ,cps.equip_cost_cons_before_amt	      -- 设备成本额（对价前） 
      ,row_number()over(partition by cps.period_id,cps.scenarios,cps.time_window_code,cps.bg_code,cps.oversea_code,cps.lv1_prod_rnd_team_code,cps.lv2_prod_rnd_team_code,
                        cps.dimension_group_code,cps.dimension_subcategory_code ,cps.currency_code order by cps.equip_rev_cons_before_amt desc ) rk 
      from    fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t	 cps -- 对价前设备收入、对价前设备成本、   YTD
      where  cps.del_flag = 'N' and  cps.version_code = v_version_code
      )
      select  
      pss.version_code	                  -- 版本编码
      ,pss.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,pss.time_window_code	              -- 统计时间窗          
      ,pss.period_id	                      -- 会计期
      ,pss.target_period	                  -- 目标时点 无 我来计算的
      ,pss.bg_code	                          -- BG编码
      ,pss.bg_name	                          -- BG名称
      ,pss.oversea_code	                  -- 区域编码
      ,pss.oversea_desc	                  -- 区域
      ,pss.lv1_code	                      -- 重量级团队LV1编码
      ,pss.lv1_name	                      -- 重量级团队LV1描述
      ,pss.lv2_code	                      -- 重量级团队LV2编码
      ,pss.lv2_name	                      -- 重量级团队LV2名称
      ,pss.dimension_group_code   	          -- 量纲分组编码            
      ,pss.dimension_group_cn_name        	  -- 量纲分组中文名                
      ,pss.dimension_group_en_name	          -- 量纲分组英文名称        
      ,pss.dimension_subcategory_code    	  -- 量纲子类编码               
      ,pss.dimension_subcategory_cn_name 	  -- 量纲子类中文名称           
      ,pss.dimension_subcategory_en_name 	  -- 量纲子类英文名称           
      ,pss.currency	                      -- 币种
      ,pss.equip_rev_cons_before_amt	      -- 设备收入额（对价前） 
      ,pss.equip_cost_cons_before_amt	      -- 设备成本额（对价前） 
      ,case when  pss.rk = 1 then 'N' else 'Y'  end  as  del_flag 
      ,case when  pss.rk = 1 then null else '上游对价前汇总表最小粒度的非最大收入记录'  end  as  remark 
       from    dimension_cost_price_sum pss 
      ;                       
      
      -- 1.把对价前的收入和成本的数据全部灌到临时表，后面用来关联
      drop table if exists dm_fop_lv1lv2_cost_price_sum_tmp;
	    create temporary table  dm_fop_lv1lv2_cost_price_sum_tmp
		  as
      select       
      cps.version_code	                  -- 版本编码
      ,'LV2'  as  level_flag	                      -- 数据层级 
      ,cps.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,cps.time_window_code	              -- 统计时间窗          
      ,cps.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,cps.bg_code	                          -- BG编码
      ,cps.bg_name	                          -- BG名称
      ,cps.oversea_code	                  -- 区域编码
      ,cps.oversea_desc	                  -- 区域
      ,cps.lv1_code	                      -- 重量级团队LV1编码
      ,cps.lv1_name	                      -- 重量级团队LV1描述
      ,cps.lv2_code	                      -- 重量级团队LV2编码
      ,cps.lv2_name	                      -- 重量级团队LV2名称       
      ,cps.currency	                      -- 币种
      ,sum(cps.equip_rev_cons_before_amt)  as  	equip_rev_cons_before_amt      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,sum(cps.equip_cost_cons_before_amt	)  as   equip_cost_cons_before_amt     -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,cps.del_flag 
      ,cps.remark 
      from    dm_fop_dimension_cost_price_sum_tmp	 cps -- 对价前设备收入、对价前设备成本、   YTD
      group by cps.version_code	                  -- 版本编码
      ,cps.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2） 
      ,cps.time_window_code	              -- 统计时间窗          
      ,cps.period_id	                      -- 会计期
      ,cps.bg_code	                          -- BG编码
      ,cps.bg_name	                          -- BG名称
      ,cps.oversea_code	                  -- 区域编码
      ,cps.oversea_desc	                  -- 区域
      ,cps.lv1_code	                      -- 重量级团队LV1编码
      ,cps.lv1_name	                      -- 重量级团队LV1描述
      ,cps.lv2_code	                      -- 重量级团队LV2编码
      ,cps.lv2_name	                      -- 重量级团队LV2名称       
      ,cps.currency	                      -- 币种
      ,cps.del_flag 
      ,cps.remark 
      union  all 
      select       
      cps.version_code	                  -- 版本编码
      ,'LV1'  as  level_flag	                      -- 数据层级  
      ,cps.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,cps.time_window_code	              -- 统计时间窗          
      ,cps.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,cps.bg_code	                          -- BG编码
      ,cps.bg_name	                          -- BG名称
      ,cps.oversea_code	                  -- 区域编码
      ,cps.oversea_desc	                  -- 区域
      ,cps.lv1_code	                      -- 重量级团队LV1编码
      ,cps.lv1_name	                      -- 重量级团队LV1描述
      ,NULL  as lv2_code	                      -- 重量级团队LV2编码
      ,NULL  as lv2_name	                      -- 重量级团队LV2名称        
      ,cps.currency    as currency	                      -- 币种
      ,sum(cps.equip_rev_cons_before_amt)  as  	equip_rev_cons_before_amt      -- 设备收入额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,sum(cps.equip_cost_cons_before_amt	)  as   equip_cost_cons_before_amt     -- 设备成本额（对价前） dm_fop_dimension_cost_price_sum_t 无数据
      ,cps.del_flag 
      ,cps.remark 
      from    dm_fop_dimension_cost_price_sum_tmp	 cps -- 对价前设备收入、对价前设备成本、   YTD
      group  by  cps.version_code	                  -- 版本编码  
      ,cps.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2） 
      ,cps.time_window_code	              -- 统计时间窗       
      ,cps.period_id	                      -- 会计期
      ,cps.bg_code	                          -- BG编码
      ,cps.bg_name	                          -- BG名称
      ,cps.oversea_code	                  -- 区域编码
      ,cps.oversea_desc	                  -- 区域
      ,cps.lv1_code	                      -- 重量级团队LV1编码
      ,cps.lv1_name	                      -- 重量级团队LV1描述
      ,cps.currency                           -- 币种
      ,cps.del_flag 
      ,cps.remark 
      ;
      
      
      
      drop table if exists dimension_ict_pl_sum_temp;
	    create temporary table  dimension_ict_pl_sum_temp
		  as
      with  dimension_ict_pl_sum_temp   as (
      select   -- distinct  152256          
      ictpl.version_code	                  -- 版本编码  
      ,ictpl.time_window_code	              -- 统计时间窗          
      ,ictpl.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,ictpl.bg_code	                          -- BG编码
      ,ictpl.bg_name	                          -- BG名称
      ,ictpl.oversea_code	                  -- 区域编码
      ,ictpl.oversea_desc	                  -- 区域
      ,ictpl.lv1_prod_rnd_team_code  as lv1_code	                      -- 重量级团队LV1编码
      ,ictpl.lv1_prod_rd_team_cn_name as lv1_name	                      -- 重量级团队LV1描述
      ,ictpl.lv2_prod_rnd_team_code as lv2_code	                      -- 重量级团队LV2编码
      ,ictpl.lv2_prod_rd_team_cn_name as lv2_name	                      -- 重量级团队LV2名称       
      ,ictpl.currency_code    as currency	                      -- 币种
      ,ictpl.equip_rev_cons_after_amt      -- 设备收入额（对价后） dm_fop_dimension_cost_price_sum_t 无数据
      ,ictpl.equip_cost_cons_after_amt     -- 设备成本额（对价后） dm_fop_dimension_cost_price_sum_t 无数据
      ,row_number()over(partition by ictpl.period_id,ictpl.time_window_code,ictpl.bg_code,ictpl.oversea_code,ictpl.lv1_prod_rnd_team_code,ictpl.lv2_prod_rnd_team_code,
                        ictpl.currency_code order by ictpl.equip_rev_cons_after_amt desc ) rk 
      from   fin_dm_opt_fop.dm_fop_dimension_ict_pl_sum_t ictpl 
      where   ictpl.del_flag = 'N'  and  ictpl.version_code = v_version_code
      )
      select   -- distinct  152256          
      pst.version_code	                  -- 版本编码  
      ,pst.time_window_code	              -- 统计时间窗          
      ,pst.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,pst.bg_code	                          -- BG编码
      ,pst.bg_name	                          -- BG名称
      ,pst.oversea_code	                  -- 区域编码
      ,pst.oversea_desc	                  -- 区域
      ,pst.lv1_code	                      -- 重量级团队LV1编码
      ,pst.lv1_name	                      -- 重量级团队LV1描述
      ,pst.lv2_code	                      -- 重量级团队LV2编码
      ,pst.lv2_name	                      -- 重量级团队LV2名称       
      ,pst.currency	                      -- 币种
      ,pst.equip_rev_cons_after_amt      -- 设备收入额（对价后） dm_fop_dimension_cost_price_sum_t 无数据
      ,pst.equip_cost_cons_after_amt     -- 设备成本额（对价后） dm_fop_dimension_cost_price_sum_t 无数据
      ,case when  pst.rk = 1 then 'N' else 'Y'  end  as  del_flag 
      ,case when  pst.rk = 1 then null else '上游对价前汇总表最小粒度的非最大收入对价后记录'  end  as  remark 
       from    dimension_ict_pl_sum_temp pst 
      ;     
      
      
      -- 2.把对价后的收入和成本的数据全部灌到临时表，后面用来关联
      drop table if exists dm_fop_dimension_ict_pl_sum_tmp;
	    create temporary table  dm_fop_dimension_ict_pl_sum_tmp
		  as
      select       
      ictpl.version_code	                  -- 版本编码
      ,'LV2'  as   level_flag
--       ,ictpl.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,ictpl.time_window_code	              -- 统计时间窗          
      ,ictpl.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,ictpl.bg_code	                          -- BG编码
      ,ictpl.bg_name	                          -- BG名称
      ,ictpl.oversea_code	                  -- 区域编码
      ,ictpl.oversea_desc	                  -- 区域
      ,ictpl.lv1_code	                      -- 重量级团队LV1编码
      ,ictpl.lv1_name	                      -- 重量级团队LV1描述
      ,ictpl.lv2_code	                      -- 重量级团队LV2编码
      ,ictpl.lv2_name	                      -- 重量级团队LV2名称       
      ,ictpl.currency	                      -- 币种
      ,ictpl.equip_rev_cons_after_amt      -- 设备收入额（对价后） dm_fop_dimension_cost_price_sum_t 无数据
      ,ictpl.equip_cost_cons_after_amt     -- 设备成本额（对价后） dm_fop_dimension_cost_price_sum_t 无数据  select  *
      ,ictpl.del_flag 
      ,ictpl.remark 
      from    dimension_ict_pl_sum_temp ictpl 
      union  all 
      select       
      ictpl.version_code	                  -- 版本编码
      ,'LV1'  as   level_flag
--       ,ictpl.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,ictpl.time_window_code	              -- 统计时间窗          
      ,ictpl.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,ictpl.bg_code	                          -- BG编码
      ,ictpl.bg_name	                          -- BG名称
      ,ictpl.oversea_code	                  -- 区域编码
      ,ictpl.oversea_desc	                  -- 区域
      ,ictpl.lv1_code	                      -- 重量级团队LV1编码
      ,ictpl.lv1_name	                      -- 重量级团队LV1描述
      ,null  as lv2_code	                      -- 重量级团队LV2编码
      ,null  as lv2_name	                      -- 重量级团队LV2名称       
      ,ictpl.currency	                      -- 币种
      ,sum(ictpl.equip_rev_cons_after_amt)  as   equip_rev_cons_after_amt     -- 设备收入额（对价后） dm_fop_dimension_cost_price_sum_t 无数据
      ,sum(ictpl.equip_cost_cons_after_amt) as    equip_cost_cons_after_amt  -- 设备成本额（对价后） dm_fop_dimension_cost_price_sum_t 无数据  select  *
      ,ictpl.del_flag 
      ,ictpl.remark 
      from   dimension_ict_pl_sum_temp ictpl 
      group  by  
      ictpl.version_code	                  -- 版本编码 
      ,ictpl.time_window_code	              -- 统计时间窗          
      ,ictpl.period_id	                      -- 会计期
      ,ictpl.bg_code	                          -- BG编码
      ,ictpl.bg_name	                          -- BG名称
      ,ictpl.oversea_code	                  -- 区域编码
      ,ictpl.oversea_desc	                  -- 区域
      ,ictpl.lv1_code                        -- 重量级团队LV1编码
      ,ictpl.lv1_name                       -- 重量级团队LV1描述      
      ,ictpl.currency
      ,ictpl.del_flag 
      ,ictpl.remark       ;

      
      -- 3.关联上述两表，得到对价前后的数据
      drop table if exists dm_fop_lv1lv2_base_process_tmp;
	    create temporary table  dm_fop_lv1lv2_base_process_tmp
		  as
      select   -- 59976
      ictpl.version_code	                  -- 版本编码
      ,ictpl.level_flag
--       ,ictpl.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,ictpl.time_window_code	              -- 统计时间窗          
      ,ictpl.period_id	                      -- 会计期
      ,''  AS  target_period	                  -- 目标时点 无 我来计算的
      ,ictpl.bg_code	                          -- BG编码
      ,ictpl.bg_name	                          -- BG名称
      ,ictpl.oversea_code	                  -- 区域编码
      ,ictpl.oversea_desc	                  -- 区域
      ,ictpl.lv1_code	                      -- 重量级团队LV1编码
      ,ictpl.lv1_name	                      -- 重量级团队LV1描述
      ,ictpl.lv2_code	                      -- 重量级团队LV2编码
      ,ictpl.lv2_name	                      -- 重量级团队LV2名称
      ,ictpl.currency	                      -- 币种
      ,cps.equip_rev_cons_before_amt	      -- 设备收入额（对价前）  
      ,cps.equip_cost_cons_before_amt	      -- 设备成本额（对价前）  
      ,ictpl.equip_rev_cons_after_amt	      -- 设备收入额（对价后）  
      ,ictpl.equip_cost_cons_after_amt	      -- 设备成本额（对价后）
      ,case when ictpl.del_flag = 'Y'  or  cps.del_flag='Y'  then  'Y'  else 'N'  end  as del_flag
      ,case when ictpl.del_flag = 'Y'  or  cps.del_flag='Y'  then  ictpl.del_flag||cps.del_flag||''||ictpl.remark||cps.remark  else null end  as remark
      from    dm_fop_dimension_ict_pl_sum_tmp	  ictpl 
      left  join  dm_fop_lv1lv2_cost_price_sum_tmp	 cps 
      on 
      ictpl.version_code =  cps.version_code                
      and ictpl.level_flag	 =     cps.level_flag                     
      and ictpl.time_window_code	 =     cps.time_window_code                    
      and ictpl.period_id	 =     cps.period_id             
      and ictpl.bg_code	 =    cps.bg_code                   
      and ictpl.oversea_code	=   cps.oversea_code                
      and ictpl.lv1_code	=     cps.lv1_code                 
      and coalesce(ictpl.lv2_code,'SNULLC')	=     coalesce(cps.lv2_code ,'SNULLC') 
      and ictpl.currency = cps.currency       ;      
      
      -- 4.将上面数据全部汇总到一张表里面
      -- 开始分别得到YTD Q H Y的数据
--       SELECT  unnest(ARRAY['Y','Q']), time_window_code FROM  (
--       select  DISTINCT  time_window_code  from   fin_dm_opt_fop.dm_fop_dimension_info_t  where  time_window_code = 'YTD'  )a
--       分开按照年  YTD  季度  半年度的数据插入到结果表
       -- YTD和年度数据，值一致
       
--         ,case when lv1_code in ('137565','133277','134557') then  coalesce(dimension_group_code,'无量纲') 
--              when lv1_code in ('100001','101775')   then  coalesce( dimension_subcategory_code,'无量纲')
--              else 'lv2' end as dimension_code   
--       from   dm_fop_dimension_all_item_process_tmp
--       where  lv1_code    in ('137565','133277','134557','100001','101775','100011') 
      
      
      drop table if exists dm_fop_lv1lv2_base_process_all_tmp;
	    create temporary table  dm_fop_lv1lv2_base_process_all_tmp
		  as  
      select     
      base.version_code	                  -- 版本编码
      ,base.level_flag
      ,case when base.level_flag = 'LV1' then 'LV1'
            when  base.lv1_code in  ('137565','133277','134557') then  '量纲分组'
            when  base.lv1_code in  ('100001','101775')  then  '量纲子类'
            else  'LV2' end  scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,UNNEST(ARRAY[left(base.period_id::VARCHAR,4),base.period_id::VARCHAR||'YTD'])    AS  target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前）  
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前）  
      ,base.equip_rev_cons_after_amt	      -- 设备收入额（对价后）  
      ,base.equip_cost_cons_after_amt	      -- 设备成本额（对价后） 
      ,base.del_flag 
      ,base.remark 
      from   dm_fop_lv1lv2_base_process_tmp  base 
      union  all 
       -- 计算季度
       select     
      base.version_code	                  -- 版本编码
      ,base.level_flag
      ,case when base.level_flag = 'LV1' then 'LV1'
            when  base.lv1_code in  ('137565','133277','134557') then  '量纲分组'
            when  base.lv1_code in  ('100001','101775')  then  '量纲子类'
            else  'LV2' end  scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）   
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,left(base.period_id::VARCHAR,4)||'Q'||(CEIL(right(base.period_id::VARCHAR,2)::numeric / 3)::VARCHAR)   AS  target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.currency	                      -- 币种
      -- 设备收入额（对价前） 
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.equip_rev_cons_before_amt 
        else  base.equip_rev_cons_before_amt - qd.equip_rev_cons_before_amt  end  as    equip_rev_cons_before_amt
      -- 设备成本额（对价前）  
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.equip_cost_cons_before_amt 
        else  base.equip_cost_cons_before_amt - qd.equip_cost_cons_before_amt  end as    equip_cost_cons_before_amt 
      -- 设备收入额（对价后）  
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.equip_rev_cons_after_amt 
        else  base.equip_rev_cons_after_amt - qd.equip_rev_cons_after_amt  end  as    equip_rev_cons_after_amt
      -- 设备成本额（对价后）  
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = 1 then base.equip_cost_cons_after_amt 
        else  base.equip_cost_cons_after_amt - qd.equip_cost_cons_after_amt  end as    equip_cost_cons_after_amt
       ,base.del_flag 
      ,base.remark  
      from   dm_fop_lv1lv2_base_process_tmp  base 
      LEFT JOIN ( SELECT case when right(q1.period_id::VARCHAR,1) = '3' then  2 
                              when right(q1.period_id::VARCHAR,1) = '6' then  3 
                         else  4 end  as q_flag, 
                         left(q1.period_id::VARCHAR,4) as y_flag,
                  q1.*  from  dm_fop_lv1lv2_base_process_tmp  q1  where  right(q1.period_id::VARCHAR,1) in ('3','6','9')
                  ) qd
      on base.version_code = qd.version_code 
      and base.level_flag =  qd.level_flag  
      and   left(base.period_id::VARCHAR,4) = qd.y_flag
       and   CEIL(right(base.period_id::VARCHAR,2)::numeric / 3) = qd.q_flag  
       and  base.bg_code = qd.bg_code
        and  base.oversea_code = qd.oversea_code   
        and  base.lv1_code = qd.lv1_code   
        and  coalesce(base.lv2_code,'SNULLC') = coalesce(qd.lv2_code,'SNULLC')
        and   base.currency = qd.currency 
        and   base.del_flag = qd.del_flag 
        and   coalesce(base.remark,'SNULLC') = coalesce(qd.remark ,'SNULLC')
      union  all 
      -- 计算半年度
      select     
      base.version_code	                  -- 版本编码
      ,base.level_flag
      ,case when base.level_flag = 'LV1' then 'LV1'
            when  base.lv1_code in  ('137565','133277','134557') then  '量纲分组'
            when  base.lv1_code in  ('100001','101775')  then  '量纲子类'
            else  'LV2' end  scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2） 
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期 
      ,left(base.period_id::VARCHAR,4)||'H'||(CEIL(right(base.period_id::VARCHAR,2)::numeric / 6)::VARCHAR)     AS  target_period             -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.currency	                      -- 币种 
      -- 设备收入额（对价前） 
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.equip_rev_cons_before_amt 
        else  base.equip_rev_cons_before_amt - qd.equip_rev_cons_before_amt  end  as    equip_rev_cons_before_amt
      -- 设备成本额（对价前）  
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.equip_cost_cons_before_amt 
        else  base.equip_cost_cons_before_amt - qd.equip_cost_cons_before_amt  end as    equip_cost_cons_before_amt 
      -- 设备收入额（对价后）  
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.equip_rev_cons_after_amt 
        else  base.equip_rev_cons_after_amt - qd.equip_rev_cons_after_amt  end  as    equip_rev_cons_after_amt
      -- 设备成本额（对价后）  
      ,case when  CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = 1 then base.equip_cost_cons_after_amt 
        else  base.equip_cost_cons_after_amt - qd.equip_cost_cons_after_amt  end as    equip_cost_cons_after_amt 
      ,base.del_flag 
      ,base.remark  
      from   dm_fop_lv1lv2_base_process_tmp  base 
      LEFT JOIN ( SELECT  2  as q_flag, 
                         left(q1.period_id::VARCHAR,4) as y_flag,
                  q1.*  from  dm_fop_lv1lv2_base_process_tmp  q1  where  right(q1.period_id::VARCHAR,1) ='6' 
                  ) qd
      on base.version_code = qd.version_code and base.level_flag =  qd.level_flag  and   left(base.period_id::VARCHAR,4) = qd.y_flag
       and   CEIL(right(base.period_id::VARCHAR,2)::numeric / 6) = qd.q_flag  and  base.bg_code = qd.bg_code
        and  base.oversea_code = qd.oversea_code   and  base.lv1_code = qd.lv1_code   and  coalesce(base.lv2_code,'SNULLC') = coalesce(qd.lv2_code,'SNULLC')
        and   base.currency = qd.currency
        and   base.del_flag = qd.del_flag 
        and   coalesce(base.remark,'SNULLC') = coalesce(qd.remark ,'SNULLC')  ;
      
      drop table if exists dm_fop_lv1lv2_base_process_all_item_tmp;
	    create temporary table  dm_fop_lv1lv2_base_process_all_item_tmp
		  as  
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前）  
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前）  
      ,base.equip_rev_cons_after_amt	      -- 设备收入额（对价后）  
      ,base.equip_cost_cons_after_amt	      -- 设备成本额（对价后）
      ,case when base.equip_rev_cons_after_amt=0 and  (base.equip_rev_cons_after_amt -base.equip_cost_cons_after_amt)  = 0 then null 
            when base.equip_rev_cons_after_amt=0 and (base.equip_rev_cons_after_amt -base.equip_cost_cons_after_amt) != 0 then -999999
            else  (1-base.equip_cost_cons_after_amt/base.equip_rev_cons_after_amt) end as  mgp_ratio_after  -- 制毛率（对价后） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,case when base.equip_rev_cons_before_amt=0 and (base.equip_rev_cons_before_amt - base.equip_cost_cons_before_amt) = 0 then null 
            when base.equip_rev_cons_before_amt=0 and (base.equip_rev_cons_before_amt - base.equip_cost_cons_before_amt) != 0 then -999999
            else  (1-base.equip_cost_cons_before_amt/base.equip_rev_cons_before_amt)  end as  mgp_ratio_before  -- 制毛率（对价前） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,case when base.equip_rev_cons_after_amt=0 and (base.equip_rev_cons_after_amt-base.equip_rev_cons_before_amt) = 0 then null 
            when base.equip_rev_cons_after_amt=0 and (base.equip_rev_cons_after_amt-base.equip_rev_cons_before_amt) != 0 then -999999
            else  (1-base.equip_rev_cons_before_amt/base.equip_rev_cons_after_amt) end  as  mca_adjust_ratio -- MCA调整率  用新的累计的算：=（对价后收入-对价前收入）/对价后收入
      ,case when base.equip_rev_cons_after_amt=0 and (base.equip_rev_cons_after_amt-base.equip_cost_cons_after_amt) = 0 then null 
            when base.equip_rev_cons_after_amt=0 and (base.equip_rev_cons_after_amt-base.equip_cost_cons_after_amt) != 0 then -999999
            else  (1-base.equip_cost_cons_after_amt/base.equip_rev_cons_after_amt) end - 
            case when base.equip_rev_cons_before_amt=0 and (base.equip_rev_cons_before_amt-base.equip_cost_cons_before_amt) = 0 then null 
            when base.equip_rev_cons_before_amt=0 and (base.equip_rev_cons_before_amt-base.equip_cost_cons_before_amt) != 0 then -999999
            else  (1-base.equip_cost_cons_before_amt/base.equip_rev_cons_before_amt)  end as mgp_adjust_ratio --制毛调整率 用新的累计的算：=（对价后收入-对价后成本）/对价后收入-（对价前收入-对价前成本）/对价前收入
      ,base.remark||',率指标计算出来得到'  as  remark
      ,'-1'  as created_by
      ,now()  as creation_date
      ,'-1'  as  last_updated_by
      ,now()  as  last_update_date
      ,base.del_flag 
      from   dm_fop_lv1lv2_base_process_all_tmp  base
       ;
       
--        从上有表拿到无量纲的逻辑，后面用来更新场景字段的无量纲为LV2
      drop table if exists nodimensions_carryover_win_tmp;
	    create temporary table  nodimensions_carryover_win_tmp
		  as
      with  dimensions_win  as  (
      select    distinct 
--        lv1_prod_rnd_team_code  as lv1_code	  
--        ,lv2_prod_rnd_team_code  as lv2_code	  
--        ,case when lv1_prod_rnd_team_code   in ('137565','133277','134557') then  coalesce(dimension_group_code,'无量纲') 
--              when lv1_prod_rnd_team_code   in ('100001','101775')   then  coalesce( dimension_subcategory_code,'无量纲')
--              else 'lv2' end as dimension_code  
       lv1_code	  
       ,lv2_code	  
       ,case when lv1_code   in ('137565','133277','134557') then  coalesce(dimension_group_code,'无量纲') 
             when lv2_code   in ('100001','101775')   then  coalesce( dimension_subcategory_code,'无量纲')
             else 'lv2' end as dimension_code   
--       from   fin_dm_opt_fop.dm_fop_dimension_cost_price_sum_t
      from   dm_fop_dimension_cost_price_sum_tmp
      where  lv1_code    in ('137565','133277','134557','100001','101775','100011') 
      and  del_flag = 'N'  --and  version_code = v_version_code
      )
      select  dw.lv1_code	  
       ,dw.lv2_code	
       ,dw.dimension_code
      from  dimensions_win dw 
      where  dw.dimension_code in ('无量纲')
      and  not exists (select  1  from  dimensions_win win where   win.dimension_code not in ('无量纲') 
                                and win.lv1_code = dw.lv1_code	
                                and win.lv2_code = dw.lv2_code	) ;
       
     update   dm_fop_lv1lv2_base_process_all_item_tmp  tmp    
        SET tmp.scenarios='LV2',tmp.remark=tmp.remark||'，更新场景的LV2'
        from  nodimensions_carryover_win_tmp upd 
        where   tmp.scenarios in ('量纲分组','量纲子类')
        and  tmp.lv1_code = upd.lv1_code  
        and tmp.lv2_code = upd.lv2_code  ;
       
      
      -- 更新YTD的数据，在填充表填充数据
--       drop table if exists max_period_rate_ytd_data_tmp;
-- 	    create temporary table  max_period_rate_ytd_data_tmp  -- 3124
-- 		  as 
--       select  
--         'YTD'  as   data_flag,version_code, scenarios, time_window_code, left(period_id,4) as y_code,   bg_code,   oversea_code,   lv1_code,   lv2_code,   currency, 
--         avg(coalesce(equip_rev_cons_after_amt,0))  as  equip_rev_cons_after_amt_ytd_data, 
--         avg(coalesce(mgp_ratio_after,0))   as  mgp_ratio_ytd_data, 
--         avg(coalesce(mca_adjust_ratio,0))   as  mca_adjust_ratio_ytd_data, 
--         avg(coalesce(mgp_adjust_ratio,0))  as   mgp_adjust_ratio_ytd_data, 
--         '' remark
--         from   dm_fop_lv1lv2_base_process_all_item_tmp 
--       where   right(target_period,3)='YTD'
--         and   left(period_id,4) = v_max_ytd
--         group by  version_code, scenarios, time_window_code, left(period_id,4)  ,   bg_code,  oversea_code, lv1_code,  lv2_code, currency ;

      


      
--       把前面汇总到一起的数据，再计算率指标插入到结果表
--       最后把数据更新到接口表
      -- 更新插入TGT接口表
      truncate  table   fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t ;
      insert  into fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_t (
      version_code	                  -- 版本编码
      ,scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,time_window_code	              -- 统计时间窗          
      ,period_id	                      -- 会计期
      ,target_period	                  -- 目标时点 无 我来计算的
      ,bg_code	                          -- BG编码
      ,bg_name	                          -- BG名称
      ,oversea_code	                  -- 区域编码
      ,oversea_desc	                  -- 区域
      ,lv1_code	                      -- 重量级团队LV1编码
      ,lv1_name	                      -- 重量级团队LV1描述
      ,lv2_code	                      -- 重量级团队LV2编码
      ,lv2_name	                      -- 重量级团队LV2名称          
      ,currency	                      -- 币种
      ,equip_rev_cons_before_amt	      -- 设备收入额（对价前） 
      ,equip_cost_cons_before_amt	      -- 设备成本额（对价前） 
      ,equip_rev_cons_after_amt	      -- 设备收入额（对价后） 
      ,equip_cost_cons_after_amt	      -- 设备成本额（对价后）  
      ,mgp_ratio_after	                      -- 制毛率（对价后）
      ,mgp_ratio_before	                      -- 制毛率（对价前）
      ,mca_adjust_ratio                 -- MCA调整率
      ,mgp_adjust_ratio                 -- 制毛调整率
--       ,equip_rev_cons_after_amt_ytd_data
--       ,mgp_ratio_ytd_data
--       ,mca_adjust_ratio_ytd_data
--       ,mgp_adjust_ratio_ytd_data
      ,remark	                          -- 备注
      ,created_by	                      -- 创建人
      ,creation_date	                  -- 创建时间
      ,last_updated_by	                  -- 修改人
      ,last_update_date	              -- 修改时间
      ,del_flag	                      -- 是否删除
      )
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前）  
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前）  
      ,base.equip_rev_cons_after_amt	      -- 设备收入额（对价后）  
      ,base.equip_cost_cons_after_amt	      -- 设备成本额（对价后）
      ,base.mgp_ratio_after  -- 制毛率（对价后） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,base.mgp_ratio_before  -- 制毛率（对价前） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,base.mca_adjust_ratio -- MCA调整率  用新的累计的算：=（对价后收入-对价前收入）/对价后收入
      ,base.mgp_adjust_ratio --制毛调整率 用新的累计的算：=（对价后收入-对价后成本）/对价后收入-（对价前收入-对价前成本）/对价前收入
--       ,max_ytd.equip_rev_cons_after_amt_ytd_data
--       ,max_ytd.mgp_ratio_ytd_data
--       ,max_ytd.mca_adjust_ratio_ytd_data
--       ,max_ytd.mgp_adjust_ratio_ytd_data
      ,base.remark
      ,'-1'  as created_by
      ,now()  as creation_date
      ,'-1'  as  last_updated_by
      ,now()  as  last_update_date
      ,base.del_flag
      from   dm_fop_lv1lv2_base_process_all_item_tmp  base 
--       left join max_period_rate_ytd_data_tmp  max_ytd
--       on   base.version_code = max_ytd.version_code
--       and  base.scenarios = max_ytd.scenarios
--       and  base.time_window_code = max_ytd.time_window_code
--       and  base.bg_code = max_ytd.bg_code
--       and  base.oversea_code = max_ytd.oversea_code
--       and  base.lv1_code = max_ytd.lv1_code
--       and  coalesce(base.lv2_code,'SNULL') = coalesce(max_ytd.lv2_code,'SNULL') 
--       and  base.currency = max_ytd.currency  and  right(base.target_period,3) = max_ytd.data_flag 
      ;
			
      
      delete  from     fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t  where  version_code= v_version_code ;
      insert  into fin_dm_opt_fop.dm_fop_dimension_lv2_tgt_period_his_t (
      version_code	                  -- 版本编码
      ,scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,time_window_code	              -- 统计时间窗          
      ,period_id	                      -- 会计期
      ,target_period	                  -- 目标时点 无 我来计算的
      ,bg_code	                          -- BG编码
      ,bg_name	                          -- BG名称
      ,oversea_code	                  -- 区域编码
      ,oversea_desc	                  -- 区域
      ,lv1_code	                      -- 重量级团队LV1编码
      ,lv1_name	                      -- 重量级团队LV1描述
      ,lv2_code	                      -- 重量级团队LV2编码
      ,lv2_name	                      -- 重量级团队LV2名称          
      ,currency	                      -- 币种
      ,equip_rev_cons_before_amt	      -- 设备收入额（对价前） 
      ,equip_cost_cons_before_amt	      -- 设备成本额（对价前） 
      ,equip_rev_cons_after_amt	      -- 设备收入额（对价后） 
      ,equip_cost_cons_after_amt	      -- 设备成本额（对价后）  
      ,mgp_ratio_after	                      -- 制毛率（对价后）
      ,mgp_ratio_before	                      -- 制毛率（对价前）
      ,mca_adjust_ratio                 -- MCA调整率
      ,mgp_adjust_ratio                 -- 制毛调整率
--       ,equip_rev_cons_after_amt_ytd_data
--       ,mgp_ratio_ytd_data
--       ,mca_adjust_ratio_ytd_data
--       ,mgp_adjust_ratio_ytd_data
      ,remark	                          -- 备注
      ,created_by	                      -- 创建人
      ,creation_date	                  -- 创建时间
      ,last_updated_by	                  -- 修改人
      ,last_update_date	              -- 修改时间
      ,del_flag	                      -- 是否删除
      )
      select     
      base.version_code	                  -- 版本编码
      ,base.scenarios	                      -- 场景（区分三种粒度的场景：量纲子类，量纲分组，lv2）  
      ,base.time_window_code	              -- 统计时间窗 
      ,base.period_id	                      -- 会计期
      ,base.target_period	                  -- 目标时点 无 我来计算的
      ,base.bg_code	                          -- BG编码
      ,base.bg_name	                          -- BG名称
      ,base.oversea_code	                  -- 区域编码
      ,base.oversea_desc	                  -- 区域
      ,base.lv1_code	                      -- 重量级团队LV1编码
      ,base.lv1_name	                      -- 重量级团队LV1描述
      ,base.lv2_code	                      -- 重量级团队LV2编码
      ,base.lv2_name	                      -- 重量级团队LV2名称
      ,base.currency	                      -- 币种
      ,base.equip_rev_cons_before_amt	      -- 设备收入额（对价前）  
      ,base.equip_cost_cons_before_amt	      -- 设备成本额（对价前）  
      ,base.equip_rev_cons_after_amt	      -- 设备收入额（对价后）  
      ,base.equip_cost_cons_after_amt	      -- 设备成本额（对价后）
      ,base.mgp_ratio_after  -- 制毛率（对价后） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,base.mgp_ratio_before  -- 制毛率（对价前） 用新的累计的算：=（对价后收入-对价后成本）/对价后收入
      ,base.mca_adjust_ratio -- MCA调整率  用新的累计的算：=（对价后收入-对价前收入）/对价后收入
      ,base.mgp_adjust_ratio --制毛调整率 用新的累计的算：=（对价后收入-对价后成本）/对价后收入-（对价前收入-对价前成本）/对价前收入
--       ,max_ytd.equip_rev_cons_after_amt_ytd_data
--       ,max_ytd.mgp_ratio_ytd_data
--       ,max_ytd.mca_adjust_ratio_ytd_data
--       ,max_ytd.mgp_adjust_ratio_ytd_data
      ,base.remark
      ,'-1'  as created_by
      ,now()  as creation_date
      ,'-1'  as  last_updated_by
      ,now()  as  last_update_date
      ,base.del_flag
      from   dm_fop_lv1lv2_base_process_all_item_tmp  base 
--       left join max_period_rate_ytd_data_tmp  max_ytd
--       on   base.version_code = max_ytd.version_code
--       and  base.scenarios = max_ytd.scenarios
--       and  base.time_window_code = max_ytd.time_window_code
--       and  base.bg_code = max_ytd.bg_code
--       and  base.oversea_code = max_ytd.oversea_code
--       and  base.lv1_code = max_ytd.lv1_code
--       and  coalesce(base.lv2_code,'SNULL') = coalesce(max_ytd.lv2_code,'SNULL') 
--       and  base.currency = max_ytd.currency  and  right(base.target_period,3) = max_ytd.data_flag 
      ;
     
  
  	-- 写结束日志
  perform fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
        p_log_version_id => null,                 --版本
        p_log_sp_name => v_sp_name,    --sp名称
        p_log_para_list => '',--参数
        p_log_step_num  => 3,
        p_log_cal_log_desc => '生成的版本编码:'||v_version_code||',dm_fop_dimension_lv2_tgt_period_t目标表的数据量:'||v_dml_row_count||',结束运行',--日志描述
        p_log_formula_sql_txt => null,--错误信息
        p_log_row_count => v_dml_row_count,
        p_log_errbuf => null  --错误编码
      ) ;
-- 

-- 
-- --处理异常信息
-- 	exception
-- 		when others then
-- 		perform  fin_dm_opt_fop.f_dm_fop_capture_log_info_t(
-- 			p_log_sp_name => v_sp_name,    --sp名称
-- 			p_log_step_num  => null,
-- 			p_log_cal_log_desc => v_sp_name||'：运行错误'--日志描述
-- 			) ;
-- 	x_success_flag := '2001';
	
	
		
 end;
 $BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100 ;