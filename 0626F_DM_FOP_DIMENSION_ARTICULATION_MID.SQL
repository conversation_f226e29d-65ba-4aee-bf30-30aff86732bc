DROP FUNCTION IF EXISTS FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ARTICULATION_MID;

CREATE OR REPLACE FUNCTION FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ARTICULATION_MID(P_VERSION_CODE CHARACTER VARYING DEFAULT NULL::CHARACTER VARYING,OUT X_SUCCESS_FLAG TEXT)
 RETURNS TEXT
 LANGUAGE PLPGSQL
 NOT FENCED NOT SHIPPABLE
AS $$
 /*
创建时间：
创建人  ：TWX1139790
背景描述：将量本价勾稽后的预测数据，按照常用数据表结构转换存中间表
参数描述：参数一(P_VERSION_CODE)：版本编码202505
          参数四(X_SUCCESS_FLAG):返回状态 1-SUCCESS/2001-ERROR
事例    ：SELECT FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ARTICULATION_MID();
*/
 
 DECLARE
    V_SP_NAME VARCHAR(100)  := 'FIN_DM_OPT_FOP.F_DM_FOP_DIMENSION_ARTICULATION_MID';
    V_VERSION_CODE VARCHAR(50);  -- 目标表的最大版本编码，格式：当前年月
    V_STEP_NUM   BIGINT := 0; --步骤号
    V_FCST_PERIOD INT;   -- 算法预测输出表中使用的类似版本概念值
BEGIN
    X_SUCCESS_FLAG := '1';                                 --1表示成功
    
  -- 如果是传 VERSION_CODE 调函数取JAVA传入的 P_VERSION_CODE ，如果是自动调度的则取 当前年月 版本
  IF P_VERSION_CODE IS NOT NULL THEN 
  SELECT  P_VERSION_CODE INTO V_VERSION_CODE ;
  ELSE 
  SELECT TO_CHAR(CURRENT_DATE,'YYYYMM') AS VERSION_CODE INTO V_VERSION_CODE ;
  END IF ;
  
  -- 取得算法预测输出表中最大的一般数据
--  SELECT MAX(PERIOD_ID) INTO V_FCST_PERIOD FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_FCST_T;
  V_FCST_PERIOD := V_VERSION_CODE;
  
  -- 删除结果表历史版本数据
  EXECUTE IMMEDIATE 'TRUNCATE TABLE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_MID_T';   -- 量纲层级YTD表
  
  -- 创建临时表
  DROP TABLE IF EXISTS FOP_ARTICULATION_MID_TMP;
  CREATE TEMPORARY TABLE FOP_ARTICULATION_MID_TMP (
         VERSION_CODE CHARACTER VARYING(100),
         MON_CODE VARCHAR(50),
         SCENARIOS CHARACTER VARYING(50),
         PHASE_DATE CHARACTER VARYING(100),
         BG_CODE CHARACTER VARYING(50),
         OVERSEA_CODE CHARACTER VARYING(50),
         LV1_CODE CHARACTER VARYING(50),
         LV2_CODE CHARACTER VARYING(50),
         DIMENSION_GROUP_CODE CHARACTER VARYING(50),
         DIMENSION_SUBCATEGORY_CODE CHARACTER VARYING(50),
         CURRENCY CHARACTER VARYING(50),
         FCST_TYPE CHARACTER VARYING(100),
         DATA_SOURCE CHARACTER VARYING(100),
         EQUIP_REV_CONS_BEFORE_AMT NUMERIC,
         EQUIP_REV_CONS_AFTER_AMT NUMERIC,
         CARRYOVER_QTY NUMERIC,
         SHIP_QTY NUMERIC,
         MGP_RATIO_BEFORE NUMERIC,
         MGP_RATIO_AFTER NUMERIC
     )
  ON COMMIT PRESERVE ROWS
  DISTRIBUTE BY HASH(DIMENSION_GROUP_CODE,DIMENSION_SUBCATEGORY_CODE);
  
  
  -- 将量纲层级需要的数据处理后插入临时表(量纲层级需要指标：量价设备收入、结转量、SOP计划量)
  INSERT INTO FOP_ARTICULATION_MID_TMP(
         VERSION_CODE,
         MON_CODE,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         OVERSEA_CODE,
         LV2_CODE,
         DIMENSION_GROUP_CODE,
         DIMENSION_SUBCATEGORY_CODE,
         CURRENCY,
         FCST_TYPE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         CARRYOVER_QTY,
         SHIP_QTY
    )
  SELECT VER_CODE,
         MON_CODE,   -- 假设MON_CODE存储为202506
         SCE_CODE,
         PHASE_DATE,
         BG_CODE,
         GEO_CODE,
         LV2_CODE,
         DIMENSION_GROUP_CODE,   -- 当场景为量纲分组时，取值，否则置空
         DIMENSION_SUBCATEGORY_CODE,   -- 当场景为量纲子类时，取值，否则置空
         CURRENCY,
         MET_CODE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,   -- 设备收入（对价前）
         CARRYOVER_QTY,   -- 结转量
         SHIP_QTY 
  FROM(
  SELECT T1.VER_CODE,
         T1.MON_CODE,   -- 假设MON_CODE存储为202506
         T1.SCE_CODE,
         T3.PHASE_DATE,
         T1.BG_CODE,
         T1.GEO_CODE,
         SUBSTR(T1.DIV_CODE,1,INSTR(T1.DIV_CODE,'_')-1) AS LV2_CODE,
         DECODE(T1.SCE_CODE,'量纲分组',SUBSTR(T1.DIV_CODE,INSTR(T1.DIV_CODE,'_')+1),NULL) AS DIMENSION_GROUP_CODE,   -- 当场景为量纲分组时，取值，否则置空
         DECODE(T1.SCE_CODE,'量纲子类',SUBSTR(T1.DIV_CODE,INSTR(T1.DIV_CODE,'_')+1),NULL) AS DIMENSION_SUBCATEGORY_CODE,   -- 当场景为量纲子类时，取值，否则置空
         'CNY' AS CURRENCY,
         T1.MET_CODE,
         'TAB_DIMENSION' AS DATA_SOURCE,
         ROW_NUMBER ( ) OVER ( PARTITION BY T1.MON_CODE,T1.SCE_CODE,T1.BG_CODE,T1.GEO_CODE,T3.PHASE_DATE,T1.DIV_CODE,T1.MET_CODE ORDER BY 1,2 ) RN,  
         SUM(DECODE(T1.LIC_CODE,'量价设备收入',NVL(AMOUNT,0),0)) AS EQUIP_REV_CONS_BEFORE_AMT,   -- 设备收入（对价前）
         SUM(DECODE(T1.LIC_CODE,'结转量',NVL(AMOUNT,0),0)) AS CARRYOVER_QTY,   -- 结转量
         SUM(DECODE(T1.LIC_CODE,'SOP计划量',NVL(AMOUNT,0),0)) AS SHIP_QTY   -- 发货量
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_T T1 
      LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
      ON T1.SOP_CODE = T3.SOP_CODE 
      WHERE T1.VER_CODE = V_FCST_PERIOD
      AND T3.PERIOD_ID = V_FCST_PERIOD
--      AND SUBSTR(T1.MON_CODE,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
      AND T1.MON_CODE BETWEEN 202406 AND 202412   -- 只取当年预测数据，为预算处理的下一年预测数据不取
      AND LIC_CODE IN('结转量', '量价设备收入', 'SOP计划量')
      AND AMOUNT <> 0
      AND SCE_CODE IN('量纲子类', '量纲分组')
      AND DIV_CODE IN(SELECT MEMBER_CODE
                        FROM DM_FSPPUB_DIM_MEMBER_T
                       WHERE DIM_ID =(SELECT DIM_ID
                                        FROM DM_FSPPUB_MY_DIMENSION_T
                                       WHERE DIM_NAME = '产业'
                                         AND WORKSPACE_ID IN(SELECT WORKSPACE_ID
                            FROM DM_FSPPUB_WORKSPACE_BASE_INFO_T
                           WHERE WORKSPACE_NAME = '盈利测试'))
                         AND LEVEL IN ('4','5'))
      AND DIV_CODE NOT LIKE '%SNULL'
      AND T1.SOP_CODE IS NOT NULL 
      GROUP BY T1.VER_CODE,
               T1.MON_CODE,
               T3.PHASE_DATE,
               T1.SCE_CODE,
               T1.BG_CODE,
               T1.GEO_CODE,
               T1.DIV_CODE,
               T1.MET_CODE
    ) 
    WHERE RN = 1;
  
  DBMS_OUTPUT.PUT_LINE('量纲层级数据插入临时表');
  
  -- 将LV2层级需要的数据处理后插入临时表(LV2层级需要指标：量价设备收入、损益口径设备收入、量价制毛率、损益口径制毛率)
  INSERT INTO FOP_ARTICULATION_MID_TMP(
         VERSION_CODE,
         MON_CODE,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         OVERSEA_CODE,
         LV2_CODE,
         CURRENCY,
         FCST_TYPE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER
    )
  SELECT VER_CODE,
         MON_CODE,   -- 假设MON_CODE存储为202506
         SCE_CODE,
         PHASE_DATE,
         BG_CODE,
         GEO_CODE,
         LV2_CODE,
         CURRENCY,
         MET_CODE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,   -- 设备收入（对价前）
         EQUIP_REV_CONS_AFTER_AMT,   -- 设备收入（对价后）
         MGP_RATIO_BEFORE,   -- 待确认制毛率（对价前）存储字段
         MGP_RATIO_AFTER
  FROM (
  SELECT T1.VER_CODE,
         T1.MON_CODE,   -- 假设MON_CODE存储为202506
         T1.SCE_CODE,
         T3.PHASE_DATE,
         T1.BG_CODE,
         T1.GEO_CODE,
         T1.DIV_CODE AS LV2_CODE,
         'CNY' AS CURRENCY,
         T1.MET_CODE,
         'TAB_LV2' AS DATA_SOURCE,
         SUM(DECODE(T1.LIC_CODE,'量价设备收入',NVL(AMOUNT,0),0)) AS EQUIP_REV_CONS_BEFORE_AMT,   -- 设备收入（对价前）
         SUM(DECODE(T1.LIC_CODE,'损益口径设备收入',NVL(AMOUNT,0),0)) AS EQUIP_REV_CONS_AFTER_AMT,   -- 设备收入（对价后）
         SUM(DECODE(T1.LIC_CODE,'量价制毛率',NVL(AMOUNT,0),0)) AS MGP_RATIO_BEFORE,   -- 待确认制毛率（对价前）存储字段
         SUM(DECODE(T1.LIC_CODE,'损益口径制毛率',NVL(AMOUNT,0),0)) AS MGP_RATIO_AFTER,   -- 待确认制毛率（对价后）存储字段
         ROW_NUMBER ( ) OVER ( PARTITION BY T1.MON_CODE,T1.SCE_CODE,T1.BG_CODE,T1.GEO_CODE,T3.PHASE_DATE,T1.DIV_CODE,T1.MET_CODE ORDER BY 1,2 ) RN  
      FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_T T1 
      LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
      ON T1.SOP_CODE = T3.SOP_CODE 
      WHERE T1.VER_CODE = V_FCST_PERIOD
      AND T3.PERIOD_ID = V_FCST_PERIOD
--      AND SUBSTR(T1.MON_CODE,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
      AND T1.MON_CODE BETWEEN 202406 AND 202412   -- 只取当年预测数据，为预算处理的下一年预测数据不取
      AND T1.LIC_CODE IN('量价设备收入', '损益口径设备收入', '量价制毛率', '损益口径制毛率')
      AND T1.AMOUNT <> 0
      AND T1.SOP_CODE IS NOT NULL 
      AND T1.SCE_CODE IN('量纲子类', '量纲分组','LV2')
      AND T1.DIV_CODE IN(SELECT MEMBER_CODE
                            FROM DM_FSPPUB_DIM_MEMBER_T
                            WHERE DIM_ID =(SELECT DIM_ID
                                              FROM DM_FSPPUB_MY_DIMENSION_T
                                             WHERE DIM_NAME = '产业'
                                               AND WORKSPACE_ID IN(SELECT WORKSPACE_ID
                                                                      FROM DM_FSPPUB_WORKSPACE_BASE_INFO_T
                                                                     WHERE WORKSPACE_NAME = '盈利测试'))
                            AND LEVEL = '3')
      AND T1.DIV_CODE NOT LIKE '%SNULL'
      GROUP BY T1.VER_CODE,
               T1.MON_CODE,
               T3.PHASE_DATE,
               T1.SCE_CODE,
               T1.BG_CODE,
               T1.GEO_CODE,
               T1.DIV_CODE,
               T1.MET_CODE
    ) 
    WHERE RN = 1;
  
  DBMS_OUTPUT.PUT_LINE('LV2层级数据插入临时表');
  
/*  -- 将LV1层级需要的数据处理后插入临时表(LV1层级需要指标：损益口径设备收入)
  INSERT INTO FOP_ARTICULATION_MID_TMP(
         VERSION_CODE,
         MON_CODE,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         OVERSEA_CODE,
         LV1_CODE,
         CURRENCY,
         FCST_TYPE,
         DATA_SOURCE,
         EQUIP_REV_CONS_AFTER_AMT
    )
  SELECT VER_CODE,
         MON_CODE,   -- 假设MON_CODE存储为202506
         SCE_CODE,
         PHASE_DATE,
         BG_CODE,
         GEO_CODE,
         LV1_CODE,
         CURRENCY,
         MET_CODE,
         DATA_SOURCE,
         EQUIP_REV_CONS_AFTER_AMT   -- 设备收入（对价后）
  FROM (
  SELECT T1.VER_CODE,
         T1.MON_CODE,   -- 假设MON_CODE存储为202506
         T1.SCE_CODE,
         T3.PHASE_DATE,
         T1.BG_CODE,
         T1.GEO_CODE,
         T1.DIV_CODE AS LV1_CODE,
         'CNY' AS CURRENCY,
         T1.MET_CODE,
         'TAB_LV1' AS DATA_SOURCE,
         SUM(DECODE(T1.LIC_CODE,'损益口径设备收入',NVL(T1.AMOUNT,0),0)) AS EQUIP_REV_CONS_AFTER_AMT,   -- 设备收入（对价后）
         ROW_NUMBER ( ) OVER ( PARTITION BY T1.MON_CODE,T1.SCE_CODE,T1.BG_CODE,T1.GEO_CODE,T3.PHASE_DATE,T1.DIV_CODE,T1.MET_CODE ORDER BY 1,2 ) RN  
       FROM FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_T T1 
         LEFT JOIN FIN_DM_OPT_FOP.DM_FOP_DIMENSION_SOP_PHASE_CONVERT_T T3
         ON T1.SOP_CODE = T3.SOP_CODE 
         WHERE T1.VER_CODE = V_FCST_PERIOD
         AND T3.PERIOD_ID = V_FCST_PERIOD
         AND T1.LIC_CODE IN('损益口径设备收入')
         AND T1.AMOUNT <> 0
         AND T1.DIV_CODE IN(SELECT MEMBER_CODE
                              FROM DM_FSPPUB_DIM_MEMBER_T
                              WHERE DIM_ID =(SELECT DIM_ID
                                                FROM DM_FSPPUB_MY_DIMENSION_T
                                               WHERE DIM_NAME = '产业'
                                                 AND WORKSPACE_ID IN(
                                                                     SELECT WORKSPACE_ID
                                                                         FROM DM_FSPPUB_WORKSPACE_BASE_INFO_T
                                                                         WHERE WORKSPACE_NAME = '盈利测试'
                                                                         )
                                            )
                           AND LEVEL = '2')
--         AND SUBSTR(T1.MON_CODE,1,4) = V_TGT_YEAR   -- 只取当年预测数据，为预算处理的下一年预测数据不取
         AND T1.MON_CODE BETWEEN 202406 AND 202412   -- 只取当年预测数据，为预算处理的下一年预测数据不取
         AND T1.SOP_CODE IS NOT NULL 
         AND T1.SCE_CODE = 'LV1'
         GROUP BY T1.VER_CODE,
                  T1.MON_CODE,
                  T3.PHASE_DATE,
                  T1.SCE_CODE,
                  T1.BG_CODE,
                  T1.GEO_CODE,
                  T1.DIV_CODE,
                  T1.MET_CODE
    ) 
    WHERE RN = 1;
  
  DBMS_OUTPUT.PUT_LINE('LV1层级数据插入临时表');
  */
  INSERT INTO FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_MID_T(
         VERSION_CODE,
         MON_CODE,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         OVERSEA_CODE,
         LV1_CODE,
         LV2_CODE,
         DIMENSION_GROUP_CODE,
         DIMENSION_SUBCATEGORY_CODE,
         CURRENCY,
         FCST_TYPE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         CARRYOVER_QTY,
         SHIP_QTY,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER,
         CREATED_BY,
         CREATION_DATE,
         LAST_UPDATED_BY,
         LAST_UPDATE_DATE,
         DEL_FLAG
    )
  SELECT VERSION_CODE,
         MON_CODE,
         SCENARIOS,
         PHASE_DATE,
         BG_CODE,
         OVERSEA_CODE,
         LV1_CODE,
         LV2_CODE,
         DIMENSION_GROUP_CODE,
         DIMENSION_SUBCATEGORY_CODE,
         CURRENCY,
         FCST_TYPE,
         DATA_SOURCE,
         EQUIP_REV_CONS_BEFORE_AMT,
         EQUIP_REV_CONS_AFTER_AMT,
         CARRYOVER_QTY,
         SHIP_QTY,
         MGP_RATIO_BEFORE,
         MGP_RATIO_AFTER,
         -1 AS CREATED_BY,
         CURRENT_TIMESTAMP AS CREATION_DATE,
         -1 AS LAST_UPDATED_BY,
         CURRENT_TIMESTAMP AS LAST_UPDATE_DATE,
         'N' AS DEL_FLAG
      FROM FOP_ARTICULATION_MID_TMP;
  DBMS_OUTPUT.PUT_LINE('数据插入结果表');
 
  --收集统计信息
  EXECUTE IMMEDIATE 'ANALYZE FIN_DM_OPT_FOP.DM_FOP_DIMENSION_ARTICULATION_MID_T';
  
  RETURN 'SUCCESS';
  
END$$
/