CREATE TABLE dm_fop_dimension_sop_phase_convert_t (
	version_code varchar(20) NOT NULL,
	phase_date varchar(60) NOT NULL,
	sop_code varchar(50) NOT NULL,
	sop_type varchar(20) NOT NULL
)
WITH (orientation=ROW)
DISTRIBUTE BY replication;


CREATE SEQUENCE dm_fop_dimension_articulation_s;

CREATE TABLE dm_fop_dimension_articulation_t (
  row_id bigint NOT NULL DEFAULT nextval('dm_fop_dimension_articulation_s'),
	ver_code varchar(20),
	sop_code varchar(50),
	mon_code varchar(100) NOT NULL,
	sce_code varchar(100),
	met_code varchar(100) NOT NULL,
	bg_code varchar(50) NOT NULL,
	geo_code varchar(50) NOT NULL,
	div_code varchar(200) NOT NULL,
	lic_code varchar(50) NOT NULL,
	amount numeric,
	origin_expression varchar(100),
	workspace_id bigint NOT NULL
)
WITH (orientation=ROW)
DISTRIBUTE BY HASH(row_id);

ALTER TABLE dm_fop_dimension_articulation_t ADD CONSTRAINT dm_fop_dimension_articulation_t_pk PRIMARY KEY (row_id);


CREATE SEQUENCE dm_fop_dimension_articulation_calculation_s;

CREATE TABLE dm_fop_dimension_articulation_t_calculation (
  row_id bigint NOT NULL DEFAULT nextval('dm_fop_dimension_articulation_calculation_s'),
	ver_code varchar(20),
	sop_code varchar(50),
	mon_code varchar(100) NOT NULL,
	sce_code varchar(100),
	met_code varchar(100) NOT NULL,
	bg_code varchar(50) NOT NULL,
	geo_code varchar(50) NOT NULL,
	div_code varchar(200) NOT NULL,
	lic_code varchar(50) NOT NULL,
	amount numeric,
  created_by varchar(200),
  creation_date timestamp(6) without time zone,
  last_updated_by varchar(200),
  last_update_date timestamp(6) without time zone,
  del_flag char(1),
  workspace_id bigint NOT NULL
)
WITH (orientation=ROW)
DISTRIBUTE BY HASH(row_id);

ALTER TABLE dm_fop_dimension_articulation_t_calculation ADD CONSTRAINT dm_fop_dimension_articulation_t_calculation_pk PRIMARY KEY (row_id);




CREATE OR REPLACE VIEW dm_fop_dimension_lv2_fcst_t_v AS SELECT scenarios as sce_code,target_period as mon_code,fcst_type as met_code,bg_code,oversea_code as geo_code,lv2_code as div_code,equip_rev_after_fcst,mgp_rate_after_fcst,mca_adjust_ratio_fcst,mgp_adjust_ratio_fcst,period_id FROM dm_fop_dimension_lv2_fcst_t
WHERE currency='CNY' and del_flag='N' and period_id=TO_CHAR(CURRENT_DATE, 'YYYYMM');

CREATE OR REPLACE VIEW dm_fop_dimension_fcst_t_1_v AS SELECT scenarios as sce_code,target_period as mon_code,fcst_type as met_code,bg_code,oversea_code as geo_code,lv2_code || '_' || dimension_subcategory_code as div_code,dimension_subcategory_code,unit_cost_fcst,unit_price_fcst,rev_percent_fcst,period_id,lv2_code FROM dm_fop_dimension_fcst_t
WHERE lv1_name in ('无线','数据存储') and currency='CNY' and del_flag='N' AND period_id=TO_CHAR(CURRENT_DATE, 'YYYYMM');


CREATE OR REPLACE VIEW dm_fop_dimension_fcst_t_2_v AS SELECT scenarios as sce_code,target_period as mon_code,fcst_type as met_code,bg_code,oversea_code as geo_code,lv2_code || '_' || dimension_group_code as div_code,dimension_group_code,unit_cost_fcst,unit_price_fcst,rev_percent_fcst,period_id,lv2_code FROM dm_fop_dimension_fcst_t
WHERE lv1_name in ('数据通信','计算','光') and currency='CNY' and del_flag='N' AND period_id=TO_CHAR(CURRENT_DATE, 'YYYYMM');



CREATE OR REPLACE VIEW dm_fop_dimension_fcst_sop_1_v AS SELECT fcst.scenarios as sce_code,fcst.target_period as mon_code,fcst.fcst_type as met_code,fcst.bg_code,fcst.oversea_code as geo_code,fcst.lv2_code || '_' || fcst.dimension_subcategory_code as div_code,fcst.dimension_subcategory_code,snop_quantity*carryover_ratio_fcst amount,phase_date,version_code,DECODE(instr(phase_date, '_'), 0,'FCST','BUDGET') sop_type,fcst.lv2_code FROM (SELECT * FROM dm_fop_dimension_sop_plan_sum_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM') AND del_flag='N') sop,
(SELECT * FROM dm_fop_dimension_fcst_t WHERE period_id=TO_CHAR(CURRENT_DATE, 'YYYYMM') AND fcst_type='YTD法' AND lv1_name in ('无线','数据存储') and currency='CNY' and del_flag='N') fcst
WHERE sop.period_id=fcst.target_period AND sop.scenarios=fcst.scenarios AND sop.bg_code=fcst.bg_code AND sop.oversea_code=fcst.oversea_code AND sop.lv1_prod_rnd_team_code=fcst.lv1_code AND sop.lv2_prod_rnd_team_code=fcst.lv2_code AND sop.dimension_group_code=fcst.dimension_group_code AND sop.dimension_subcategory_code=fcst.dimension_subcategory_code;

CREATE OR REPLACE VIEW dm_fop_dimension_fcst_sop_2_v AS SELECT fcst.scenarios as sce_code,fcst.target_period as mon_code,fcst.fcst_type as met_code,fcst.bg_code,fcst.oversea_code as geo_code,fcst.lv2_code || '_' || fcst.dimension_group_code as div_code,fcst.dimension_group_code,snop_quantity*carryover_ratio_fcst amount,phase_date,version_code,DECODE(instr(phase_date, '_'), 0,'FCST','BUDGET') sop_type,fcst.lv2_code FROM (SELECT * FROM dm_fop_dimension_sop_plan_sum_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM') AND del_flag='N') sop,
(SELECT * FROM dm_fop_dimension_fcst_t WHERE period_id=TO_CHAR(CURRENT_DATE, 'YYYYMM') AND fcst_type='YTD法' AND lv1_name in ('数据通信','计算','光') and currency='CNY' and del_flag='N') fcst
WHERE sop.period_id=fcst.target_period AND sop.scenarios=fcst.scenarios AND sop.bg_code=fcst.bg_code AND sop.oversea_code=fcst.oversea_code AND sop.lv1_prod_rnd_team_code=fcst.lv1_code AND sop.lv2_prod_rnd_team_code=fcst.lv2_code AND sop.dimension_group_code=fcst.dimension_group_code AND sop.dimension_subcategory_code=fcst.dimension_subcategory_code;




-- 期次转化
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_sop_phase_convert_t_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  DELETE FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM');
  
  INSERT INTO dm_fop_dimension_sop_phase_convert_t(version_code,phase_date,sop_code,sop_type)
  SELECT TO_CHAR(CURRENT_DATE, 'YYYYMM'),phase_date,'预测期次' || ROW_NUMBER() OVER (ORDER BY phase_date),'FCST' FROM (
  SELECT DISTINCT phase_date FROM dm_fop_dimension_sop_plan_sum_t WHERE instr(phase_date, '_') = 0 and version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM') AND del_flag='N');
  
  INSERT INTO dm_fop_dimension_sop_phase_convert_t(version_code,phase_date,sop_code,sop_type)
  SELECT TO_CHAR(CURRENT_DATE, 'YYYYMM'),phase_date,'预算期次' || ROW_NUMBER() OVER (ORDER BY phase_date),'BUDGET' FROM (
  SELECT DISTINCT phase_date FROM dm_fop_dimension_sop_plan_sum_t WHERE instr(phase_date, '_') != 0 and version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM') AND del_flag='N');
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;


CREATE OR REPLACE FUNCTION f_dm_fop_dimension_lv2_fcst_t_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,equip_rev_after_fcst,'损益口径设备收入' from dm_fop_dimension_lv2_fcst_t_v;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,mgp_rate_after_fcst,'损益口径制毛率' from dm_fop_dimension_lv2_fcst_t_v;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,mca_adjust_ratio_fcst,'对价等转化系数' from dm_fop_dimension_lv2_fcst_t_v;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,div_code,mgp_adjust_ratio_fcst,'量价到损益制毛调整率' from dm_fop_dimension_lv2_fcst_t_v;
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;


-- 量纲子类
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_t_1_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,lv2_code,unit_cost_fcst,'均本（分摊后）' from dm_fop_dimension_fcst_t_1_v WHERE sce_code='LV2';
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_cost_fcst,'均本（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_1_v WHERE sce_code!='LV2') fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,lv2_code,unit_price_fcst,'均价（分摊后）' from dm_fop_dimension_fcst_t_1_v WHERE sce_code='LV2';
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_price_fcst,'均价（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_1_v WHERE sce_code!='LV2') fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,lv2_code,rev_percent_fcst,'占比' from dm_fop_dimension_fcst_t_1_v WHERE sce_code='LV2' and dimension_subcategory_code='NOSUB';
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,rev_percent_fcst,'占比' from (SELECT * from dm_fop_dimension_fcst_t_1_v WHERE sce_code!='LV2' and dimension_group_code='NOSUB') fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc;
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;


-- 量纲分组
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_t_2_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,lv2_code,unit_cost_fcst,'均本（分摊后）' from dm_fop_dimension_fcst_t_2_v WHERE sce_code='LV2';
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_cost_fcst,'均本（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_2_v WHERE sce_code!='LV2') fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,lv2_code,unit_price_fcst,'均价（分摊后）' from dm_fop_dimension_fcst_t_2_v WHERE sce_code='LV2';
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,unit_price_fcst,'均价（分摊后）' from (SELECT * from dm_fop_dimension_fcst_t_2_v WHERE sce_code!='LV2') fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,'SNULL',sce_code,mon_code,met_code,bg_code,geo_code,lv2_code,rev_percent_fcst,'占比' from dm_fop_dimension_fcst_t_2_v WHERE sce_code='LV2' and dimension_group_code='NODIM';
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT period_id,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,rev_percent_fcst,'占比' from (SELECT * from dm_fop_dimension_fcst_t_2_v WHERE sce_code!='LV2' and dimension_group_code='NODIM') fv,(SELECT sop_code FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc;
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;

-- 量纲子类
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_sop_1_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT version_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),amount,'结转量' from dm_fop_dimension_fcst_sop_1_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT version_code,sop_code,sce_code,mon_code,'分月法',bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),amount,'结转量' from dm_fop_dimension_fcst_sop_1_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date;
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;

-- 量纲分组
CREATE OR REPLACE FUNCTION f_dm_fop_dimension_fcst_sop_2_v()
  RETURNS VARCHAR
  LANGUAGE plpgsql
AS $BODY$
  
BEGIN

  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT version_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),amount,'结转量' from dm_fop_dimension_fcst_sop_2_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date;
  
  INSERT INTO dm_fop_dimension_articulation_t(ver_code,sop_code,sce_code,mon_code,met_code,bg_code,geo_code,div_code,amount,lic_code)
  SELECT version_code,sop_code,sce_code,mon_code,'分月法',bg_code,geo_code,decode(sce_code,'LV2',lv2_code,div_code),amount,'结转量' from dm_fop_dimension_fcst_sop_2_v sv,(SELECT sop_code,phase_date,sop_type FROM dm_fop_dimension_sop_phase_convert_t WHERE version_code=TO_CHAR(CURRENT_DATE, 'YYYYMM')) spc WHERE sv.sop_type=spc.sop_type AND sv.phase_date=spc.phase_date;
  
  RETURN 'SUCCESS';

EXCEPTION
  WHEN OTHERS THEN
  RETURN SQLSTATE||':'||SQLERRM;
END; $BODY$;
